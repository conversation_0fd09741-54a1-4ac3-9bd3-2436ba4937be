"use client";

import { useUserQuery, useUpdateUser } from "./hook";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

const schema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
});

export default function ProfileForm() {
  const { data: user, isLoading } = useUserQuery();
  const { mutate: updateUser, isPending } = useUpdateUser();

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      name: user?.name || "",
      email: user?.email || "",
    },
    values: user ? { name: user.name, email: user.email } : undefined,
  });

  const onSubmit = (values: { name: string; email: string }) => {
    updateUser(values, {
      onSuccess: () => toast.success("Profile updated"),
      onError: () => toast.error("Failed to update profile"),
    });
  };

  if (isLoading) return <p>Loading...</p>;

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 max-w-xl">
      <div>
        <Label>Name</Label>
        <Input {...form.register("name")} />
      </div>
      <div>
        <Label>Email</Label>
        <Input type="email" {...form.register("email")} />
      </div>
      <Button type="submit" disabled={isPending}>
        {isPending ? "Saving..." : "Save Changes"}
      </Button>
    </form>
  );
}
