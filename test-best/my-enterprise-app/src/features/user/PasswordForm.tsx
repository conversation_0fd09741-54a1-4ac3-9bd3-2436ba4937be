"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import api from "@/lib/axios";

const schema = z.object({
  currentPassword: z.string().min(6),
  newPassword: z.string().min(8, "Minimum 8 characters"),
});

export default function PasswordForm() {
  const form = useForm({
    resolver: zodResolver(schema),
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    try {
      await api.patch("/auth/update-password", data);
      toast.success("Password updated");
    } catch {
      toast.error("Failed to update password");
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 max-w-md">
      <div>
        <Label>Current Password</Label>
        <Input type="password" {...form.register("currentPassword")} />
      </div>
      <div>
        <Label>New Password</Label>
        <Input type="password" {...form.register("newPassword")} />
      </div>
      <Button type="submit">Update Password</Button>
    </form>
  );
}
