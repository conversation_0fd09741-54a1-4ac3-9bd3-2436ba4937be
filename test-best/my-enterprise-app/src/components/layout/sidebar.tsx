"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

const links = [
  { href: "/dashboard", label: "Dashboard" },
  { href: "/dashboard/profile", label: "Profile" },
];

export default function Sidebar() {
  const pathname = usePathname();

  return (
    <aside className="w-64 bg-gray-100 h-full p-4">
      <nav className="space-y-2">
        {links.map(({ href, label }) => (
          <Link
            key={href}
            href={href}
            className={cn(
              "block px-3 py-2 rounded hover:bg-gray-200",
              pathname === href && "bg-gray-300 font-bold"
            )}>
            {label}
          </Link>
        ))}
      </nav>
    </aside>
  );
}
