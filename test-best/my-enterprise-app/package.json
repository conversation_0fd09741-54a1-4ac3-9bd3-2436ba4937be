{"name": "my-enterprise-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.83.0", "axios": "^1.11.0", "next": "15.4.2", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "sonner": "^2.0.6", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}