"use client";

import { useEffect, useRef, useState } from "react";
import Head from "next/head";

interface Paddle {
  x: number;
  y: number;
  width: number;
  height: number;
  speed: number;
}

interface FallingObject {
  x: number;
  y: number;
  width: number;
  height: number;
}

export default function Home() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [score, setScore] = useState<number>(0);
  const [gameOver, setGameOver] = useState<boolean>(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    let animationFrameId: number;

    // Game state
    const paddle: Paddle = {
      x: 250,
      y: 580,
      width: 100,
      height: 20,
      speed: 10,
    };
    const objects: FallingObject[] = [];
    const objectSpeed: number = 5;
    const objectSpawnRate: number = 1000; // ms

    // Handle keyboard input
    let leftPressed: boolean = false;
    let rightPressed: boolean = false;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") leftPressed = true;
      if (e.key === "ArrowRight") rightPressed = true;
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") leftPressed = false;
      if (e.key === "ArrowRight") rightPressed = false;
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    // Spawn falling objects
    const spawnObject = () => {
      const x = Math.random() * (canvas.width - 20);
      objects.push({ x, y: 0, width: 20, height: 20 });
    };

    const spawnInterval = setInterval(spawnObject, objectSpawnRate);

    // Game loop
    const gameLoop = () => {
      if (gameOver) return;

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Move paddle
      if (leftPressed && paddle.x > 0) paddle.x -= paddle.speed;
      if (rightPressed && paddle.x < canvas.width - paddle.width)
        paddle.x += paddle.speed;

      // Draw paddle
      ctx.fillStyle = "blue";
      ctx.fillRect(paddle.x, paddle.y, paddle.width, paddle.height);

      // Update and draw objects
      ctx.fillStyle = "red";
      objects.forEach((obj, index) => {
        obj.y += objectSpeed;
        ctx.fillRect(obj.x, obj.y, obj.width, obj.height);

        // Check collision with paddle
        if (
          obj.y + obj.height > paddle.y &&
          obj.x + obj.width > paddle.x &&
          obj.x < paddle.x + paddle.width
        ) {
          setScore((prev) => prev + 1);
          objects.splice(index, 1);
        }

        // Check if object missed
        if (obj.y > canvas.height) {
          setGameOver(true);
        }
      });

      animationFrameId = requestAnimationFrame(gameLoop);
    };

    gameLoop();

    return () => {
      cancelAnimationFrame(animationFrameId);
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
      clearInterval(spawnInterval);
    };
  }, [gameOver]);

  const restartGame = () => {
    setScore(0);
    setGameOver(false);
  };

  return (
    <>
      <Head>
        <title>Catch the Objects</title>
      </Head>
      <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center">
        <h1 className="text-4xl font-bold mb-4">Catch the Objects</h1>
        <p className="text-xl mb-4">Score: {score}</p>
        {gameOver && (
          <div className="text-center">
            <p className="text-2xl text-red-500 mb-4">Game Over!</p>
            <button
              onClick={restartGame}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
              Restart
            </button>
          </div>
        )}
        <canvas
          ref={canvasRef}
          width={600}
          height={600}
          className="border-2 border-black"
        />
      </div>
    </>
  );
}
