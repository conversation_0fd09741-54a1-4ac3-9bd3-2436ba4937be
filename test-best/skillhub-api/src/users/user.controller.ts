import { <PERSON><PERSON><PERSON><PERSON>, Controller, Get, Req, <PERSON>, Body } from '@nestjs/common';
import { UserService } from './user.service';

@UseGuards(JwtGuard) // Or your custom auth guard
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('me')
  getMe(@Req() req) {
    return this.userService.getMe(req.user.id);
  }

  @Patch('me')
  updateMe(@Req() req, @Body() dto) {
    return this.userService.updateMe(req.user.id, dto);
  }

  @Patch('update-password')
  updatePassword(@Req() req, @Body() dto: { currentPassword: string; newPassword: string }) {
    return this.userService.updatePassword(req.user.id, dto.currentPassword, dto.newPassword);
  }
}
