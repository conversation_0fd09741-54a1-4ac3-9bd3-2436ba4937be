import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNotesToLineItem1747994321242 implements MigrationInterface {
  name = 'AddNotesToLineItem1747994321242';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "notes" text[] DEFAULT '{}'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "line_items" DROP COLUMN "notes"`);
  }
}
