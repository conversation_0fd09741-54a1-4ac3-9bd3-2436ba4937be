import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeNumberToDecimal1747229555139 implements MigrationInterface {
  name = 'ChangeNumberToDecimal1747229555139'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "shippingWeight" TYPE numeric(10,2)`);
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "productLength" TYPE numeric(10,2)`);
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "productWidth" TYPE numeric(10,2)`);
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "productHeight" TYPE numeric(10,2)`);
    await queryRunner.query(`ALTER TYPE "public"."product_sku_exceptionhandlingrule_enum" RENAME TO "product_sku_exceptionhandlingrule_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."product_sku_exceptionhandlingrule_enum" AS ENUM('Send to Image Needed Queue', 'Auto-Assign from First Image on Order')`);
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "exceptionHandlingRule" TYPE "public"."product_sku_exceptionhandlingrule_enum" USING "exceptionHandlingRule"::"text"::"public"."product_sku_exceptionhandlingrule_enum"`);
    await queryRunner.query(`DROP TYPE "public"."product_sku_exceptionhandlingrule_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."product_sku_exceptionhandlingrule_enum_old" AS ENUM('Send to Image Needed Queue', 'Auto-Assign from First Image on Order')`);
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "exceptionHandlingRule" TYPE "public"."product_sku_exceptionhandlingrule_enum_old" USING "exceptionHandlingRule"::"text"::"public"."product_sku_exceptionhandlingrule_enum_old"`);
    await queryRunner.query(`DROP TYPE "public"."product_sku_exceptionhandlingrule_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."product_sku_exceptionhandlingrule_enum_old" RENAME TO "product_sku_exceptionhandlingrule_enum"`);
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "productHeight" TYPE integer`);
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "productWidth" TYPE integer`);
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "productLength" TYPE integer`);
    await queryRunner.query(`ALTER TABLE "product_sku" ALTER COLUMN "shippingWeight" TYPE integer`);
  }

}
