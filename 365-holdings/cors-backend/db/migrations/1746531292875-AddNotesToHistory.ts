import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNotesToHistory1746531292875 implements MigrationInterface {
    name = 'AddNotesToHistory1746531292875'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "database_history" ADD "notes" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "database_history" DROP COLUMN "notes"`);
    }

}
