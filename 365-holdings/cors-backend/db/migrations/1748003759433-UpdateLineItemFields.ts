import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateLineItemFields1748003759433 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update cancelReason to jsonb
    await queryRunner.query(`
      ALTER TABLE line_items
      ADD COLUMN "cancelReason_new" jsonb;
    `);

    await queryRunner.query(`
      UPDATE line_items
      SET "cancelReason_new" = jsonb_build_object(
        'status', "cancelReason",
        'timestamp', CURRENT_TIMESTAMP,
        'username', 'system'
      )
      WHERE "cancelReason" IS NOT NULL;
    `);

    await queryRunner.query(`
      ALTER TABLE line_items
      DROP COLUMN "cancelReason";
    `);

    await queryRunner.query(`
      ALTER TABLE line_items
      RENAME COLUMN "cancelReason_new" TO "cancelReason";
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert cancelReason to text
    await queryRunner.query(`
      ALTER TABLE line_items
      ADD COLUMN "cancelReason_old" text;
    `);

    await queryRunner.query(`
      UPDATE line_items
      SET "cancelReason_old" = "cancelReason"->>'status'
      WHERE "cancelReason" IS NOT NULL;
    `);

    await queryRunner.query(`
      ALTER TABLE line_items
      DROP COLUMN "cancelReason";
    `);

    await queryRunner.query(`
      ALTER TABLE line_items
      RENAME COLUMN "cancelReason_old" TO "cancelReason";
    `);
  }
} 