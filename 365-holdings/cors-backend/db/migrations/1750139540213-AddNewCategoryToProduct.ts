import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNewCategoryToProduct1750139540213
  implements MigrationInterface
{
  name = 'AddNewCategoryToProduct1750139540213';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."products_category_enum" RENAME TO "products_category_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."products_category_enum" AS ENUM('Rush & Add Ons', 'Pajamas', 'Plush', 'Jewelry', 'Socks', 'Sweaters', 'Apparel', 'Portraits', 'Accessories', 'Uncategorized')`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" ALTER COLUMN "category" TYPE "public"."products_category_enum" USING "category"::"text"::"public"."products_category_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."products_category_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."products_category_enum_old" AS ENUM('Rush & Add Ons', 'Pajamas', 'Plush', 'Jewelry', 'Socks', 'Sweaters', 'Apparel', 'Portraits', 'Accessories')`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" ALTER COLUMN "category" TYPE "public"."products_category_enum_old" USING "category"::"text"::"public"."products_category_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."products_category_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."products_category_enum_old" RENAME TO "products_category_enum"`,
    );
  }
}
