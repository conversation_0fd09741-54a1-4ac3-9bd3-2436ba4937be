import { MigrationInterface, QueryRunner } from "typeorm";

export class AddBandanaVariantToLineItem1752577021090 implements MigrationInterface {
  name = 'AddBandanaVariantToLineItem1752577021090'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "line_items" ADD "customBandanaVariant" character varying`);
    await queryRunner.query(`ALTER TABLE "line_items" DROP CONSTRAINT "FK_94264119ca7539f84a313655ebe"`);
    await queryRunner.query(`ALTER TABLE "line_items" ALTER COLUMN "productSkuId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "line_items" ADD CONSTRAINT "FK_94264119ca7539f84a313655ebe" FOREIGN KEY ("productSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "line_items" DROP CONSTRAINT "FK_94264119ca7539f84a313655ebe"`);
    await queryRunner.query(`ALTER TABLE "line_items" ALTER COLUMN "productSkuId" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "line_items" ADD CONSTRAINT "FK_94264119ca7539f84a313655ebe" FOREIGN KEY ("productSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "line_items" DROP COLUMN "customBandanaVariant"`);
  }
}
