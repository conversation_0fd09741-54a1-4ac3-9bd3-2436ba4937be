import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateWorkflowCategoryStringToEnum1748950551071
  implements MigrationInterface
{
  name = 'UpdateWorkflowCategoryStringToEnum1748950551071';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product_sku" DROP COLUMN "workflowCategory"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."product_sku_workflowcategory_enum" AS ENUM('Crop Image Only', 'Art Only', 'Customer Image Only', 'Crop Image+Template Placement', 'Art+Template Placement', 'Art+Template Placement+Approval', 'Plush', 'Add-on')`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_sku" ADD "workflowCategory" "public"."product_sku_workflowcategory_enum"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product_sku" DROP COLUMN "workflowCategory"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."product_sku_workflowcategory_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_sku" ADD "workflowCategory" character varying`,
    );
  }
}
