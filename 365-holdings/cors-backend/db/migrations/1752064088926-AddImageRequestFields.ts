import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNewImageRequestFields1752064088926
  implements MigrationInterface
{
  name = 'AddNewImageRequestFields1752064088926';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "lastImageRequestSentAt" TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "imageRequestAttempts" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "currentImageRequestWorkflowStartedAt" TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "workflowTags" text[] NOT NULL DEFAULT '{}'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "workflowTags"`);
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "currentImageRequestWorkflowStartedAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "imageRequestAttempts"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "lastImageRequestSentAt"`,
    );
  }
}
