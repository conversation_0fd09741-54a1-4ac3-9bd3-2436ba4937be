import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateVendorRelatedTables1747044842455 implements MigrationInterface {
    name = 'CreateVendorRelatedTables1747044842455'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "vendor_supported_skus" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "productionTimeDays" bigint, "handlePeakSeason" boolean, "peakProductionTimeDays" bigint, "maxCapacityPerDay" bigint, "cost" numeric, "rushCost" numeric, "shippingPerOrder" numeric, "vendorId" uuid, "productSkuId" uuid, CONSTRAINT "PK_30491ab9beef18dc3375b4ece70" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "vendor_api_details" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "integrationType" character varying, "apiUrl" character varying, "apiKey" character varying, "apiOrderFormat" character varying, "flatFileTransfer" character varying, "syncOrderStatus" boolean, "synctrackingNumber" boolean, "vendorId" uuid, CONSTRAINT "PK_098e1467cb100fbdda5c46c33b2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "maxCapacityPerDay"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "productionTime"`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "type" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "email" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "phoneNumber" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "address" json`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "country" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "timeZone" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "isActive" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "primaryAssignmentRule" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "secondaryAssignmentRule" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "capacityBasedAssignment" boolean`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "backupVendorId" uuid`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "canManualOverride" boolean`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "paymentTerms" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "canDropship" boolean`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "shippingCarrier" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "transitTimeDays" bigint`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "requireTrackingNumber" boolean`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "shipstationStore" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "shippingMethodOptions" character varying`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "fulfillmentRate" integer`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "defectRate" integer`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "performanceGap" bigint`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD CONSTRAINT "UQ_cb074a9eec68ba7e436d46f8c55" UNIQUE ("sku")`);
        await queryRunner.query(`CREATE INDEX "IDX_cb074a9eec68ba7e436d46f8c5" ON "vendors" ("sku") `);
        await queryRunner.query(`ALTER TABLE "vendor_supported_skus" ADD CONSTRAINT "FK_276ded106dfff8be7e4337dcd90" FOREIGN KEY ("vendorId") REFERENCES "vendors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vendor_supported_skus" ADD CONSTRAINT "FK_db03238746666d7ea46af1e0937" FOREIGN KEY ("productSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vendor_api_details" ADD CONSTRAINT "FK_0d0400081b7476f14589750dbd5" FOREIGN KEY ("vendorId") REFERENCES "vendors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "vendor_api_details" DROP CONSTRAINT "FK_0d0400081b7476f14589750dbd5"`);
        await queryRunner.query(`ALTER TABLE "vendor_supported_skus" DROP CONSTRAINT "FK_db03238746666d7ea46af1e0937"`);
        await queryRunner.query(`ALTER TABLE "vendor_supported_skus" DROP CONSTRAINT "FK_276ded106dfff8be7e4337dcd90"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cb074a9eec68ba7e436d46f8c5"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP CONSTRAINT "UQ_cb074a9eec68ba7e436d46f8c55"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "performanceGap"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "defectRate"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "fulfillmentRate"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "shippingMethodOptions"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "shipstationStore"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "requireTrackingNumber"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "transitTimeDays"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "shippingCarrier"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "canDropship"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "paymentTerms"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "canManualOverride"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "backupVendorId"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "capacityBasedAssignment"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "secondaryAssignmentRule"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "primaryAssignmentRule"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "isActive"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "timeZone"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "country"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "address"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "phoneNumber"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "email"`);
        await queryRunner.query(`ALTER TABLE "vendors" DROP COLUMN "type"`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "productionTime" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "vendors" ADD "maxCapacityPerDay" integer NOT NULL`);
        await queryRunner.query(`DROP TABLE "vendor_api_details"`);
        await queryRunner.query(`DROP TABLE "vendor_supported_skus"`);
    }

}
