import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeUserRoleTOManyToMany1745313092510 implements MigrationInterface {
  name = 'ChangeUserRoleTOManyToMany1745313092510'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE "user_role_pivot" ("userId" uuid NOT NULL, "roleId" uuid NOT NULL, CONSTRAINT "PK_74a41f867f705f863a0b9b1f224" PRIMARY KEY ("userId", "roleId"))`);
    await queryRunner.query(`CREATE INDEX "IDX_1ebb2ab55b22c25cbc981f7fb2" ON "user_role_pivot" ("userId") `);
    await queryRunner.query(`CREATE INDEX "IDX_7208b87497ae2f6d4d6c1c8a2f" ON "user_role_pivot" ("roleId") `);
    await queryRunner.query(`ALTER TABLE "user_role_pivot" ADD CONSTRAINT "FK_1ebb2ab55b22c25cbc981f7fb2e" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    await queryRunner.query(`ALTER TABLE "user_role_pivot" ADD CONSTRAINT "FK_7208b87497ae2f6d4d6c1c8a2f7" FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_role_pivot" DROP CONSTRAINT "FK_7208b87497ae2f6d4d6c1c8a2f7"`);
    await queryRunner.query(`ALTER TABLE "user_role_pivot" DROP CONSTRAINT "FK_1ebb2ab55b22c25cbc981f7fb2e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7208b87497ae2f6d4d6c1c8a2f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1ebb2ab55b22c25cbc981f7fb2"`);
    await queryRunner.query(`DROP TABLE "user_role_pivot"`);
  }
}
