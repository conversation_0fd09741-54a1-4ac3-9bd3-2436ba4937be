import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateHistoryTable1746004590359 implements MigrationInterface {
    name = 'CreateHistoryTable1746004590359'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "database_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "entityId" character varying NOT NULL, "entityType" character varying NOT NULL, "oldValue" jsonb, "newValue" jsonb, "changes" jsonb, "action" character varying NOT NULL, "userId" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c8f27bd14d9841b194fae267483" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_908e16a2ead6df18e04ac3dfd2" ON "database_history" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_0ba7b72b02c6bd4b5fc0dd03a3" ON "database_history" ("entityType", "entityId") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_0ba7b72b02c6bd4b5fc0dd03a3"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_908e16a2ead6df18e04ac3dfd2"`);
        await queryRunner.query(`DROP TABLE "database_history"`);
    }

}
