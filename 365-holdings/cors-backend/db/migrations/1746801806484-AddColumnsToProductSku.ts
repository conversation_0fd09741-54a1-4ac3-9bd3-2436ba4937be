import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnsToProductSku1746801806484 implements MigrationInterface {
    name = 'AddColumnsToProductSku1746801806484'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "followupTiming"`);
        await queryRunner.query(`CREATE TYPE "public"."product_sku_vendorassignmentrule_enum" AS ENUM('Fastest Cycle Time', 'Lowest Cost')`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "vendorAssignmentRule" "public"."product_sku_vendorassignmentrule_enum"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "processingPriority" integer`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "shopifyNativeVariant" jsonb`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "shopifyCustomVariant" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "shopifyCustomVariant"`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "shopifyNativeVariant"`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "processingPriority"`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "vendorAssignmentRule"`);
        await queryRunner.query(`DROP TYPE "public"."product_sku_vendorassignmentrule_enum"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "followupTiming" integer`);
    }

}
