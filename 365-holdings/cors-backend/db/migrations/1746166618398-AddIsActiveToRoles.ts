import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsActiveToRoles1746166618398 implements MigrationInterface {
    name = 'AddIsActiveToRoles1746166618398'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "roles" RENAME COLUMN "status" TO "isActive"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "isActive"`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "isActive" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TYPE "public"."products_category_enum" RENAME TO "products_category_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."products_category_enum" AS ENUM('Rush & Add Ons', 'Pajamas', 'Plush', 'Jewelry', 'Socks', 'Sweaters', 'Apparel', 'Portraits', 'Accessories')`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "category" TYPE "public"."products_category_enum" USING "category"::"text"::"public"."products_category_enum"`);
        await queryRunner.query(`DROP TYPE "public"."products_category_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."products_category_enum_old" AS ENUM('Rush & Add Ons', 'Pajamas', 'Plush', 'Jewellery', 'Socks', 'Sweaters', 'Apparel', 'Portraits', 'Accessories')`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "category" TYPE "public"."products_category_enum_old" USING "category"::"text"::"public"."products_category_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."products_category_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."products_category_enum_old" RENAME TO "products_category_enum"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "isActive"`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "isActive" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "roles" RENAME COLUMN "isActive" TO "status"`);
    }

}
