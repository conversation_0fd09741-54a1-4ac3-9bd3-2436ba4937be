import { DataSource, DataSourceOptions } from 'typeorm';
import databaseConfig from '../src/config/database.config';
import { entities } from '../src/entities';

const dbConfig = databaseConfig();

export const dataSourceOptions: DataSourceOptions = {
  ...dbConfig,
  type: 'postgres',
  entities,
  migrations: ['db/migrations/*.ts', 'db/migrations/*.js'],
};

const dataSource = new DataSource(dataSourceOptions);

export default dataSource;
