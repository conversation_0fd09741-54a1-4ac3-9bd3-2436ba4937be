import { DataSource, ILike } from 'typeorm';
import { Role } from '../../src/roles/entities/role.entity';
import { User } from '../../src/users/entities/user.entity';
import * as bcrypt from 'bcryptjs';
import { permissions } from 'src/permissions/permissions';
import { PermissionResources } from 'src/constants/permission-resources';
import { Queue } from '../../src/workflow-queues/entities/queue.entity';

const allPermission = permissions;
let permissionCount = 0;

export async function getDynamicPermissions(dataSource: DataSource) {
  const queueRepository = dataSource.getRepository(Queue);
  const queues = await queueRepository.find({
    where: {
      name: ILike('%Ready for Artwork%'),
    },
  });
  const dynamicPermissions = queues.map(queue => ({
    resource: queue.name as PermissionResources,
    actions: [
      `View ${queue.name}`,
      `Start/Stop ${queue.name}`,
      `Add Request ${queue.name}`,
    ],
    dependencies: {
      [`Start/Stop ${queue.name}`]: [`View ${queue.name}`],
      [`Add Request ${queue.name}`]: [
        `Start/Stop ${queue.name}`,
        `View ${queue.name}`,
      ],
    },
  }));
  const allPermissions = [...allPermission, ...dynamicPermissions];
  permissionCount = allPermissions.reduce(
    (acc, curr) => acc + curr.actions.length,
    0,
  );

  return allPermissions;
}

export async function seedDevUser(dataSource: DataSource) {
  const developerRole = await createRole(dataSource, {
    name: 'Developer',
    isActive: true,
    rolePermissions: await getDynamicPermissions(dataSource),
    permissionCount,
  });

  const superAdminRole = await createRole(dataSource, {
    name: 'Super Admin',
    isActive: true,
    rolePermissions: await getDynamicPermissions(dataSource),
    permissionCount,
  });

  const ownerRole = await createRole(dataSource, {
    name: 'Owner',
    isActive: true,
    rolePermissions: await getDynamicPermissions(dataSource),
    permissionCount,
  });

  await createUser(dataSource, {
    firstName: 'Dev',
    lastName: 'User',
    email: '<EMAIL>',
    roles: [developerRole],
  });

  await createUser(dataSource, {
    firstName: 'Dev',
    lastName: 'Example',
    email: '<EMAIL>',
    roles: [superAdminRole],
  });

  await createUser(dataSource, {
    firstName: 'Owner',
    lastName: 'User',
    email: '<EMAIL>',
    roles: [ownerRole],
  });

  await createUser(dataSource, {
    firstName: 'Chris',
    lastName: 'Anderson',
    email: '<EMAIL>',
    roles: [ownerRole],
  });

  console.log('Development user seeded successfully');
}

const createUser = async (
  dataSource: DataSource,
  user: Pick<User, 'firstName' | 'lastName' | 'email' | 'roles'>,
) => {
  const userRepository = dataSource.getRepository(User);

  let userRecord = await userRepository.findOne({
    where: { email: user.email },
  });

  const hashedPassword = await bcrypt.hash('test1234', 10);

  if (userRecord) {
    userRepository.merge(userRecord, {
      ...user,
      password: hashedPassword,
      isActive: true,
    });
    await userRepository.save(userRecord);
    console.log(`User ${user.email} updated successfully`);
    return userRecord;
  }

  // Create new user if doesn't exist
  const newUser = userRepository.create({
    ...user,
    password: hashedPassword,
    isActive: true,
  });

  await userRepository.save(newUser);
  console.log(`User ${user.email} created successfully`);
  return newUser;
};

const createRole = async (
  dataSource: DataSource,
  role: Pick<Role, 'name' | 'isActive' | 'rolePermissions' | 'permissionCount'>,
) => {
  const roleRepository = dataSource.getRepository(Role);

  let roleRecord = await roleRepository.findOne({
    where: { name: role.name },
  });

  if (roleRecord) {
    // Update existing role
    roleRepository.merge(roleRecord, {
      isActive: role.isActive,
      rolePermissions: role.rolePermissions,
      permissionCount: role.permissionCount,
    });
    await roleRepository.save(roleRecord);
    console.log(`Role ${role.name} updated successfully`);
    return roleRecord;
  }

  // Create new role if doesn't exist
  const newRole = roleRepository.create({
    name: role.name,
    isActive: role.isActive,
    rolePermissions: role.rolePermissions,
    permissionCount: role.permissionCount,
  });
  await roleRepository.save(newRole);
  console.log(`Role ${role.name} created successfully`);
  return newRole;
};
