import dataSource from '../data-source';
import { seedDevUser } from './user.seed';

async function runSeeds() {
  try {
    await dataSource.initialize();
    console.log('Data Source has been initialized!');

    await seedDevUser(dataSource);
    await dataSource.destroy();
    console.log('Data Source has been disconnected!');
  } catch (error) {
    console.error('Error during seeding:', error);
    process.exit(1);
  }
}

runSeeds();
