# Deployment Guide

This guide explains how to deploy the application to EC2 using GitHub Actions.

## Prerequisites

Before deploying, ensure you have the following:

1. An EC2 instance running with:

   - Node.js 22 installed
   - pnpm installed
   - PM2 installed globally (`npm install -g pm2`)
   - NVM installed
   - Proper security group rules for SSH access

2. GitHub repository secrets and variables set up:

   - `EC2_SSH_KEY`: Your EC2 instance's private key (PEM file content)
   - `EC2_HOST`: Your EC2 instance's public IP or DNS
   - `EC2_USER`: The username to connect to your EC2 instance

3. Required environment variables (Refer .env.example):
   - Secrets (sensitive data)
   - Variables (non-sensitive data)

## Deployment Process

The deployment is handled automatically through GitHub Actions. Here's what happens during deployment:

1. **Build Process**:

   - Sets up Node.js 22
   - Installs pnpm 8
   - Installs project dependencies
   - Builds the application

2. **Environment Setup**:

   - Creates `.env` file with all required environment variables
   - Configures SSH access to EC2

3. **Deployment**:
   - Creates deployment directory on EC2
   - Copies built files and configuration to EC2
   - Installs production dependencies
   - Restarts the application using PM2

## How to Deploy

1. **Manual Deployment**:

   - Go to your GitHub repository
   - Navigate to "Actions" tab
   - Select "Deploy to EC2" workflow
   - Click "Run workflow"
   - Select the branch to deploy
   - Click "Run workflow"

2. **Automatic Deployment**:
   - Push your changes to the main branch
   - The workflow will automatically trigger

## Post-Deployment

After deployment, verify that:

1. The application is running:

   ```bash
   pm2 status
   ```

2. Check the logs:

   ```bash
   pm2 logs cors-backend
   ```

3. Run database migrations if needed:
   ```bash
   pnpm migration:run:prod
   ```

## Troubleshooting

If you encounter issues:

1. **Deployment Fails**:

   - Check GitHub Actions logs for errors
   - Verify all required secrets and variables are set
   - Ensure EC2 instance is accessible

2. **Application Not Starting**:

   - Check PM2 logs
   - Verify environment variables are correctly set
   - Check database connection

3. **Database Issues**:
   - Verify database credentials
   - Check database connection
   - Run migrations manually if needed

## Rollback

If you need to rollback:

1. **Code Rollback**:

   - Revert to previous commit
   - Trigger deployment workflow

2. **Database Rollback**:
   ```bash
   pnpm migration:revert:prod
   ```

## Security Notes

1. Never commit sensitive information to the repository
2. Keep your EC2 SSH key secure
3. Regularly rotate secrets and access tokens
4. Monitor application logs for security issues

## Maintenance

Regular maintenance tasks:

1. Update Node.js and dependencies
2. Monitor application logs
3. Check database performance
4. Review security settings
5. Backup database regularly
