import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { accessEnv } from '../../env.validation';

@Injectable()
export class ShopifyTokenGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedException('Authorization header is required');
    }

    const token = authHeader.replace('Bearer ', '');
    const expectedToken = accessEnv('SHOPIFY_CUSTOMER_TOKEN');

    if (!expectedToken) {
      throw new UnauthorizedException('Shopify customer token not configured');
    }

    if (token !== expectedToken) {
      throw new UnauthorizedException('Invalid Shopify customer token');
    }

    return true;
  }
} 