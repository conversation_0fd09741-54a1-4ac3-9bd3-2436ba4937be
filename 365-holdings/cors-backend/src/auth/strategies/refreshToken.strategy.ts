import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { accessEnv } from '../../env.validation';

export class RefreshJwtStrategy extends PassportStrategy(
  Strategy,
  'jwt-refresh',
) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromBodyField('refresh'),
      ignoreExpiration: false,
      secretOrKey: accessEnv('JWT_SECRET'),
    });
  }

  async validate(payload: any) {
    return {
      user: payload.sub,
      email: payload.email,
    };
  }
}
