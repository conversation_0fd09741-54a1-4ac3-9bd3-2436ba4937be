import { PassportStrategy } from '@nestjs/passport';
import { Injectable, Logger } from '@nestjs/common';
import { Strategy } from 'passport-strategy';
import * as crypto from 'crypto';
import { HttpException, HttpStatus } from '@nestjs/common';
import { accessEnv } from '../../env.validation';
@Injectable()
export class ShopifyWebhookStrategy extends PassportStrategy(
  Strategy,
  'shopify-webhook',
) {
  private readonly logger = new Logger(ShopifyWebhookStrategy.name);
  constructor() {
    super();
    // Must manually bind the strategy to be compatible with Passport
    (this as any).name = 'shopify-webhook';
  }

  async authenticate(req: any) {
    const secret = accessEnv('SHOPIFY_WEBHOOK_SECRET');
    if (!secret) {
      return (this as any).error(
        new HttpException('SHOPIFY_WEBHOOK_SECRET is not defined', HttpStatus.INTERNAL_SERVER_ERROR)
      );
    }

    const shopifySignature = req.headers['x-shopify-hmac-sha256'];
    if (!shopifySignature) {
      this.logger.error('Missing Shopify Signature');
      return (this as any).fail('Missing Shopify Signature', HttpStatus.BAD_REQUEST);
    }

    const rawBody = req.rawBody || req.body;
    if (!rawBody) {
      this.logger.error('Raw body missing');
      return (this as any).fail('Raw body missing', HttpStatus.BAD_REQUEST);
    }

    const hash = crypto
      .createHmac('sha256', secret)
      .update(rawBody)
      .digest('base64');

    this.logger.debug('Computed Hash:', hash);
    this.logger.debug('Shopify Signature:', shopifySignature);

    if (hash !== shopifySignature) {
      this.logger.error('Invalid Shopify Webhook signature');
      return (this as any).fail('Invalid Shopify Webhook signature', HttpStatus.UNAUTHORIZED);
    }

    try {
      req.body = typeof rawBody === 'string' ? JSON.parse(rawBody) : rawBody;
      req.rawBody = req.body;

      this.logger.debug('Shopify Webhook signature verified');
      return (this as any).success({}, null); // Allows controller to proceed
    } catch (err) {
      this.logger.error('Error parsing raw body', err);
      return (this as any).error(err);
    }
  }
}
