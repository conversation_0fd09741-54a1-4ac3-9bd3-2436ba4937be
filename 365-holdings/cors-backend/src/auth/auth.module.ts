// src/auth/auth.module.ts
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { LocalStrategy } from './strategies/local-strategy';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtStrategy } from './strategies/jwt-strategy';
import { RefreshJwtStrategy } from './strategies/refreshToken.strategy';
import { ShopifyWebhookStrategy } from './strategies/shopify-webhook-strategy';
import { UsersModule } from '../users/users.module';
import { PassportModule } from '@nestjs/passport';
import { EmailUtil } from '../utils/email.util';
import { accessEnv } from '../env.validation';
import { Role } from 'src/roles/entities/role.entity';
import { RolesModule } from 'src/roles/roles.module';

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forFeature([User, Role]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: accessEnv('JWT_SECRET'),
        signOptions: { expiresIn: accessEnv('JWT_ACCESS_EXPIRES_IN') },
      }),
    }),
    UsersModule,
    RolesModule,
    PassportModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    EmailUtil,
    LocalStrategy,
    JwtStrategy,
    RefreshJwtStrategy,
    ShopifyWebhookStrategy,
  ],
  exports: [AuthService],
})
export class AuthModule {}
