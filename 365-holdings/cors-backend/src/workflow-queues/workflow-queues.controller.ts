import {
  Controller,
  Get,
  Query,
  UseGuards,
  Put,
  Body,
  Post,
  Headers,
} from '@nestjs/common';
import { QueuesService } from './workflow-queues.service';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiBody,
} from '@nestjs/swagger';
import { JwtGuard } from 'src/auth/guards/jwt-auth.guard';
import { CurrentUser } from 'src/utils/current-user.decorator';
import { User } from 'src/users/entities/user.entity';
import { SkipAuth } from 'src/auth/decorators/public.decorator';
import { verifyShopifyToken } from 'src/utils/workflow.utils';

@UseGuards(JwtGuard)
@ApiBearerAuth()
@ApiTags('Queues')
@Controller('workflow-queues')
export class QueuesController {
  constructor(private readonly queuesService: QueuesService) {}

  @ApiOperation({ summary: 'Get Specific Queue' })
  @ApiResponse({ status: 200, description: 'Returns Specific Queue' })
  @ApiQuery({
    name: 'id',
    required: true,
    type: String,
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Get()
  async getQueues(
    @Query('id') id: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return await this.queuesService.findQueue(id, page || 1, limit || 25);
  }

  @ApiOperation({ summary: 'Get all Queues' })
  @ApiResponse({ status: 200, description: 'Returns all Queues' })
  @Get('all')
  async getAllQueues() {
    const queues = await this.queuesService.findAll({
      relations: ['attachments', 'lineItems'],
    });

    // Define the queue priority order based on workflow status
    const queuePriorityOrder = {
      'Crop Review': 1,
      'Crop Needed': 2,
      'Template Placement': 3,
      // Add other queue types with their priority numbers
    };

    // Map the queues with their data and include priority in response
    const mappedQueues = queues.data.map(queue => {
      // Determine priority based on queue name, default to a high number if not found
      const priority = queuePriorityOrder[queue.name] || 999;

      return {
        id: queue.id,
        name: queue.name,
        maxItemsToAssign: queue.maxItemsToAssign,
        item_count: queue.attachments
          ? queue.attachments.length + queue.lineItems.length
          : 0,
        priority: priority, // Include priority in the response
      };
    });

    // Sort by priority (ascending)
    return mappedQueues.sort((a, b) => {
      // First sort by priority
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      // If priorities are the same, sort alphabetically by name
      return a.name.localeCompare(b.name);
    });
  }

  @ApiOperation({ summary: 'Assign Queue items to a user' })
  @ApiQuery({
    name: 'queueId',
    required: true,
    type: String,
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({ status: 200, description: 'Assigns Queue items to a user' })
  @Get('assign')
  async assignQueueItems(
    @CurrentUser() currentUser: User,
    @Query('queueId') queueId: string,
  ) {
    return await this.queuesService.assignQueueItems(currentUser, queueId);
  }

  @ApiOperation({ summary: 'Update queues settings' })
  @ApiResponse({ status: 200, description: 'Updates queue settings' })
  @ApiBody({
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          maxItemsToAssign: { type: 'number' },
        },
      },
      example: [
        { id: '5a3dc484-2d29-4858-84a4-480621812ff0', maxItemsToAssign: 2 },
      ],
    },
  })
  @Put('update-settings')
  async updateQueueSettings(
    @Body() queueSettings: Array<{ id: string; maxItemsToAssign: number }>,
  ) {
    return await this.queuesService.updateQueueSettings(queueSettings);
  }

  @ApiOperation({ summary: 'Stop Queue Review Process' })
  @ApiResponse({ status: 200, description: 'Stops Queue Review Process' })
  @Get('stop-review')
  async stopReview(
    @CurrentUser() currentUser: User,
    @Query('queueId') queueId: string,
  ) {
    return await this.queuesService.stopReview(currentUser, queueId);
  }

  @ApiOperation({ summary: 'Get Queue Item for Review' })
  @ApiQuery({
    name: 'queueId',
    required: true,
    type: String,
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Get('get-queue-item')
  async getQueueItem(
    @CurrentUser() currentUser: User,
    @Query('queueId') queueId: string,
  ) {
    return await this.queuesService.getQueueItem(currentUser, queueId);
  }

  @ApiOperation({ summary: 'Update Queue Item Status' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        queueId: { type: 'string' },
        itemId: { type: 'string' },
        action: { type: 'string' },
        type: { type: 'string' },
      },
    },
  })
  @Put('update-queue-item-status')
  async updateQueueItemStatus(
    @CurrentUser() currentUser: User,
    @Body()
    body: {
      queueId: string;
      itemId: string;
      action: string;
      type: string;
    },
  ) {
    try {
      return await this.queuesService.updateQueueItemStatus(currentUser, body);
    } catch (error) {
      console.log(error);
    }
  }

  @ApiOperation({ summary: 'Upload Completed Art File' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        attachmentId: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-426614174000',
        },
        completedArtFileUrl: {
          type: 'string',
          example: 'https://example.com/completed-art-file.png',
        },
        queueId: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-426614174000',
        },
      },
      required: ['attachmentId', 'completedArtFileUrl', 'queueId'],
    },
  })
  @Post('upload-completed-art-file')
  async uploadCompletedArtFile(
    @CurrentUser() currentUser: User,
    @Body()
    body: {
      attachmentId: string;
      completedArtFileUrl: string;
      queueId: string;
    },
  ) {
    return await this.queuesService.uploadCompletedArtFile(
      currentUser,
      body.attachmentId,
      body.completedArtFileUrl,
      body.queueId,
    );
  }

  @ApiOperation({ summary: 'Upload Template Placement File' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        lineItemId: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-426614174000',
        },
        templatePlacementFileUrl: {
          type: 'string',
          example: 'https://example.com/completed-art-file.png',
        },
        queueId: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-426614174000',
        },
      },
      required: ['lineItemId', 'templatePlacementFileUrl', 'queueId'],
    },
  })
  @Post('upload-template-placement-file')
  async uploadTemplatePlacementFile(
    @CurrentUser() currentUser: User,
    @Body()
    body: {
      lineItemId: string;
      templatePlacementFileUrl: string;
      queueId: string;
    },
  ) {
    return await this.queuesService.uploadTemplatePlacementFile(
      currentUser,
      body.lineItemId,
      body.templatePlacementFileUrl,
      body.queueId,
    );
  }

  @ApiOperation({ summary: 'Customer response to artwork approval' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        lineItemId: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-426614174000',
        },
        action: {
          type: 'string',
          example: 'approved/rejected',
        },
      },
      required: ['lineItemId', 'type'],
    },
  })
  @SkipAuth()
  @Post('customer-response-to-artwork')
  async customerResponseToArtwork(
    @Headers() headers: Record<string, any>,
    @Body()
    body: {
      itemNumber: string;
      action: string;
    },
  ) {
    verifyShopifyToken(headers);
    return await this.queuesService.customerResponseToArtwork(
      body.itemNumber,
      body.action,
    );
  }

  @ApiOperation({ summary: 'Add artwork request' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        lineItemId: { type: 'string' },
        attachmentUrls: { type: 'array', items: { type: 'string' } },
        notes: { type: 'string' },
        queueId: { type: 'string' },
      },
    },
  })
  @Post('add-artwork-request')
  async addArtworkRequest(
    @CurrentUser() currentUser: User,
    @Body()
    body: {
      lineItemId: string;
      attachmentUrls: string[];
      notes: string;
      queueId: string;
    },
  ) {
    return await this.queuesService.addArtworkRequest(body, currentUser);
  }
}
