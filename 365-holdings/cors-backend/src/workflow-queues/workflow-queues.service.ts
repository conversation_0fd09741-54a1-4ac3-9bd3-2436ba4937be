import {
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Queue } from './entities/queue.entity';
import { BaseService } from 'src/common/base.service';
import { User } from 'src/users/entities/user.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { LineItem } from 'src/orders/entities/line-item.entity';
import {
  checkAttachmentStatusTransition,
  checkStatusTransition,
  userFullName,
} from 'src/utils/workflow.utils';
import { DBHelper } from 'src/helpers/db.helpers';
import { EmailUtil } from 'src/utils/email.util';
import { NewImageRequestWorkflow } from 'src/jobs/customer-communication/workflows/new-image-request/new-image-request.workflow';
import { CustomerApprovalWorkflow } from 'src/jobs/customer-communication/workflows/customer-approval/customer-approval.workflow';

import { WorkflowCategory } from 'src/product-sku/enums/product-sku.enums';
import {
  LineItemRequest,
  RequestType,
} from 'src/orders/entities/line-item-request.entity';
import { AttachmentStatus } from 'src/orders/enums/attachment-status.enum';
import { LineItemStatus } from 'src/orders/enums/line-item-status.enum';
@Injectable()
export class QueuesService extends BaseService<Queue> {
  private readonly logger = new Logger(QueuesService.name);

  constructor(
    @InjectRepository(Queue)
    private queueRepository: Repository<Queue>,
    @InjectRepository(Attachment)
    private attachmentRepository: Repository<Attachment>,
    @InjectRepository(LineItem)
    private lineItemRepository: Repository<LineItem>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(LineItemRequest)
    private lineItemRequestRepository: Repository<LineItemRequest>,
    private emailUtil: EmailUtil,
    private newImageRequestQueue: NewImageRequestWorkflow,
    private customerApprovalWorkflow: CustomerApprovalWorkflow,
  ) {
    super(queueRepository);
  }

  async findQueue(id: string, page: number, limit: number) {
    const queue = await DBHelper.findOne(this.queueRepository, {
      where: {
        id: id,
      },
      relations: [
        'lineItems',
        'lineItems.attachments',
        'lineItems.productSku',
        'lineItems.order',
        'lineItems.assignedTo',
        'lineItems.productSku.artworkType',
        'attachments',
        'attachments.lineItem',
        'attachments.lineItem.productSku',
        'attachments.lineItem.order',
        'attachments.assignedTo',
      ],
    });

    if (!queue) {
      throw new NotFoundException('Queue not found');
    }

    // Process attachments with pagination
    const sortedAttachments = queue.attachments
      .map(attachment => ({
        id: attachment.id,
        priority: attachment.lineItem?.priority,
        filename: attachment.filename,
        attachment_url: attachment.url,
        cutout_pro_url: attachment.cutoutProImageUrl,
        cropType: attachment.lineItem?.productSku?.cropType,
        order_number: attachment.lineItem?.order?.shopifyOrderNumber,
        order_date: attachment.lineItem?.order?.orderDate,
        assigned_to: attachment.assignedTo
          ? userFullName(attachment?.assignedTo)
          : null,
      }))
      .sort((a, b) => {
        const hasPriorityA = !!a.priority;
        const hasPriorityB = !!b.priority;

        if (hasPriorityA && !hasPriorityB) return -1; // a comes first
        if (!hasPriorityA && hasPriorityB) return 1; // b comes first

        // If neither has priority or both have priority, sort by order number
        if (!hasPriorityA && !hasPriorityB) {
          // Convert order numbers to numbers for proper numeric comparison
          const orderNumA = parseInt(a.order_number || '0', 10);
          const orderNumB = parseInt(b.order_number || '0', 10);
          return orderNumA - orderNumB; // ascending order
        }
        return 0;
      });

    // Process line items with pagination
    const sortedLineItems = queue.lineItems
      .map(lineItem => ({
        id: lineItem.id,
        priority: lineItem.priority,
        sku: lineItem.productSku.sku,
        attachments: lineItem.attachments.map(attachment => ({
          id: attachment.id,
          name: attachment.filename,
          status: attachment.status,
          attachment_url: attachment.url,
          cutout_pro_url: attachment.cutoutProImageUrl,
        })),
        order_number: lineItem.order.shopifyOrderNumber,
        order_date: lineItem.order.orderDate,
        line_item_status: lineItem.status,
        quantity: lineItem.quantity,
        artwork_type: lineItem.productSku.artworkType?.name,
        properties: lineItem.metadata,
        assigned_to: lineItem.assignedTo
          ? userFullName(lineItem.assignedTo)
          : null,
      }))
      .sort((a, b) => {
        const hasPriorityA = !!a.priority;
        const hasPriorityB = !!b.priority;

        if (hasPriorityA && !hasPriorityB) return -1; // a comes first
        if (!hasPriorityA && hasPriorityB) return 1; // b comes first
        // If neither has priority or both have priority, sort by order number
        if (!hasPriorityA && !hasPriorityB) {
          // Convert order numbers to numbers for proper numeric comparison
          const orderNumA = parseInt(a.order_number || '0', 10);
          const orderNumB = parseInt(b.order_number || '0', 10);
          return orderNumA - orderNumB; // ascending order
        }
        return 0;
      });

    // Apply pagination
    const paginatedAttachments = sortedAttachments.slice(
      (page - 1) * limit,
      page * limit,
    );
    const paginatedLineItems = sortedLineItems.slice(
      (page - 1) * limit,
      page * limit,
    );

    return {
      id: queue.id,
      name: queue.name,
      attachments: paginatedAttachments,
      lineItems: paginatedLineItems,
      page: page,
      limit: limit,
    };
  }

  async assignQueueItems(currentUser: User, queueId: string) {
    // Use a transaction to ensure atomicity and prevent race conditions
    return await this.queueRepository.manager.transaction(
      async transactionalEntityManager => {
        // First fetch the queue without locking to check if it exists
        const queue = await transactionalEntityManager.findOne(
          this.queueRepository.target,
          {
            where: { id: queueId },
          },
        );

        if (!queue) {
          throw new NotFoundException('Queue not found');
        }
        // Fetch attachments separately
        const attachments = await transactionalEntityManager
          .createQueryBuilder(Attachment, 'attachment')
          .leftJoinAndSelect('attachment.lineItem', 'lineItem')
          .leftJoinAndSelect('lineItem.order', 'order')
          .where('attachment.queueId = :queueId', { queueId })
          .andWhere('attachment.assignedTo IS NULL')
          .getMany();

        // Fetch line items separately
        const lineItems = await transactionalEntityManager
          .createQueryBuilder(LineItem, 'lineItem')
          .leftJoinAndSelect('lineItem.productSku', 'productSku')
          .leftJoinAndSelect('lineItem.order', 'order')
          .leftJoinAndSelect('lineItem.attachments', 'attachments')
          .leftJoinAndSelect('lineItem.product', 'product')
          .where('lineItem.queueId = :queueId', { queueId })
          .andWhere('lineItem.assignedTo IS NULL')
          .getMany();

        const userId = (currentUser as any).user;
        const user = await DBHelper.findOne(this.userRepository, {
          where: { id: userId, isActive: true },
          relations: [
            'lineItems',
            'attachments',
            'attachments.queue',
            'lineItems.queue',
          ],
        });

        if (!user) {
          throw new NotFoundException('User not found');
        }

        // Sort attachments based on their line item's priority
        const sortedAttachments = attachments.sort((a, b) => {
          const hasPriorityA = !!a.lineItem?.priority;
          const hasPriorityB = !!b.lineItem?.priority;

          if (hasPriorityA && !hasPriorityB) return -1; // a comes first
          if (!hasPriorityA && hasPriorityB) return 1; // b comes first
          if (!hasPriorityA && !hasPriorityB) {
            // Convert order numbers to numbers for proper numeric comparison
            const orderNumA = parseInt(
              a.lineItem.order.shopifyOrderNumber || '0',
              10,
            );
            const orderNumB = parseInt(
              b.lineItem.order.shopifyOrderNumber || '0',
              10,
            );
            return orderNumA - orderNumB; // ascending order
          }
          return 0;
        });

        // Sort line items based on priority
        const sortedLineItems = lineItems.sort((a, b) => {
          const hasPriorityA = !!a.priority;
          const hasPriorityB = !!b.priority;

          if (hasPriorityA && !hasPriorityB) return -1; // a comes first
          if (!hasPriorityA && hasPriorityB) return 1; // b comes first
          if (!hasPriorityA && !hasPriorityB) {
            // Convert order numbers to numbers for proper numeric comparison
            const orderNumA = parseInt(a.order.shopifyOrderNumber || '0', 10);
            const orderNumB = parseInt(b.order.shopifyOrderNumber || '0', 10);
            return orderNumA - orderNumB; // ascending order
          }
          return 0;
        });

        const assignedAttachments: Attachment[] = [];
        const assignedLineItems: LineItem[] = [];
        const userAttachments = user.attachments.filter(
          attachment => attachment.queue && attachment.queue.id === queue.id,
        );
        const userLineItems = user.lineItems.filter(
          lineItem => lineItem.queue && lineItem.queue.id === queue.id,
        );
        if (userAttachments.length === 0 && sortedAttachments.length > 0) {
          const attachmentsToAssign = sortedAttachments.slice(
            0,
            queue.maxItemsToAssign,
          );
          for (const attachment of attachmentsToAssign) {
            attachment.assignedTo = user;
            await transactionalEntityManager.save(attachment);
            assignedAttachments.push(attachment);
          }
        }

        if (userLineItems.length === 0 && sortedLineItems.length > 0) {
          const lineItemsToAssign = sortedLineItems.slice(
            0,
            queue.maxItemsToAssign,
          );
          for (const lineItem of lineItemsToAssign) {
            lineItem.assignedTo = user;
            await transactionalEntityManager.save(lineItem);
            assignedLineItems.push(lineItem);
          }
        }

        const userWithAssigned = await transactionalEntityManager.findOne(
          this.userRepository.target,
          {
            where: { id: userId, isActive: true },
            relations: [
              'lineItems',
              'attachments',
              'lineItems.queue',
              'attachments.queue',
            ],
          },
        );

        // Filter line items and attachments where queue id matches
        if (userWithAssigned) {
          userWithAssigned.lineItems = userWithAssigned.lineItems.filter(
            lineItem => lineItem.queue && lineItem.queue.id === queue.id,
          );
          userWithAssigned.attachments = userWithAssigned.attachments.filter(
            attachment => attachment.queue && attachment.queue.id === queue.id,
          );
        }
        if (!userWithAssigned) {
          throw new NotFoundException('User not found');
        }

        const modifiedAttachments = assignedAttachments
          .map(attachment => ({
            id: attachment.id,
            priority: attachment.lineItem?.priority,
            filename: attachment.filename,
            attachment_url: attachment.url,
            cutout_pro_url: attachment.cutoutProImageUrl,
            cropType: attachment.lineItem?.productSku?.cropType,
            order_number: attachment.lineItem?.order?.shopifyOrderNumber,
            order_date: attachment.lineItem?.order?.orderDate,
            assigned_to: userFullName(attachment.assignedTo),
            attachment_status: attachment.status,
            line_item_status: attachment.lineItem?.status,
          }))
          .sort((a, b) => {
            const hasPriorityA = !!a.priority;
            const hasPriorityB = !!b.priority;

            if (hasPriorityA && !hasPriorityB) return -1; // a comes first
            if (!hasPriorityA && hasPriorityB) return 1; // b comes first
            if (!hasPriorityA && !hasPriorityB) {
              // Convert order numbers to numbers for proper numeric comparison
              const orderNumA = parseInt(a.order_number || '0', 10);
              const orderNumB = parseInt(b.order_number || '0', 10);
              return orderNumA - orderNumB; // ascending order
            }
            return 0;
          });

        const modifiedLineItems = assignedLineItems
          .map(lineItem => ({
            id: lineItem.id,
            priority: lineItem.priority,
            sku: lineItem.productSku.sku,
            product_name: lineItem.product?.name,
            attachments: lineItem.attachments.map(attachment => ({
              id: attachment.id,
              name: attachment.filename,
              status: attachment.status,
              attachment_url: attachment.url,
              cutout_pro_url: attachment.cutoutProImageUrl,
            })),
            order_number: lineItem.order.shopifyOrderNumber,
            order_date: lineItem.order.orderDate,
            line_item_status: lineItem.status,
            assigned_to: userFullName(lineItem.assignedTo),
          }))
          .sort((a, b) => {
            const hasPriorityA = !!a.priority;
            const hasPriorityB = !!b.priority;

            if (hasPriorityA && !hasPriorityB) return -1; // a comes first
            if (!hasPriorityA && hasPriorityB) return 1; // b comes first
            if (!hasPriorityA && !hasPriorityB) {
              // Convert order numbers to numbers for proper numeric comparison
              const orderNumA = parseInt(a.order_number || '0', 10);
              const orderNumB = parseInt(b.order_number || '0', 10);
              return orderNumA - orderNumB; // ascending order
            }
            return 0;
          });

        const total_assigned =
          userWithAssigned.attachments &&
          userWithAssigned.attachments.length > 0
            ? userWithAssigned.attachments.length
            : userWithAssigned.lineItems &&
                userWithAssigned.lineItems.length > 0
              ? userWithAssigned.lineItems.length
              : 0;
        return {
          total_assigned,
          attachments: modifiedAttachments[0],
          lineItems: modifiedLineItems[0],
        };
      },
    );
  }

  async updateQueueSettings(
    queueSettings: Array<{ id: string; maxItemsToAssign: number }>,
  ) {
    const queues = await DBHelper.findMany(this.queueRepository, {
      where: { id: In(queueSettings.map(q => q.id)) },
    });

    for (const queue of queues) {
      queue.maxItemsToAssign =
        queueSettings.find(q => q.id === queue.id)?.maxItemsToAssign ?? 5;
      await this.queueRepository.save(queue);
    }
    return queues;
  }

  async stopReview(currentUser: User, queueId: string) {
    const queue = await DBHelper.findOne(this.queueRepository, {
      where: { id: queueId },
      relations: [
        'attachments',
        'lineItems',
        'attachments.assignedTo',
        'lineItems.assignedTo',
      ],
    });
    if (!queue) {
      throw new NotFoundException('Queue not found');
    }

    const userId = (currentUser as any).user;
    const user = await DBHelper.findOne(this.userRepository, {
      where: { id: userId, isActive: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const hello = queue.attachments.filter(
      attachment => attachment.assignedTo?.id === user.id,
    );

    if (queue.attachments.length > 0) {
      for (const attachment of queue.attachments) {
        if (attachment.assignedTo?.id === user.id) {
          (attachment as any).assignedTo = null;
          await this.attachmentRepository.save(attachment);
        }
      }
    }

    if (queue.lineItems.length > 0) {
      for (const lineItem of queue.lineItems) {
        if (lineItem.assignedTo?.id === user.id) {
          (lineItem as any).assignedTo = null;
          await this.lineItemRepository.save(lineItem);
        }
      }
    }

    return {
      message: 'Review process stopped',
    };
  }

  async getQueueItem(currentUser: User, queueId: string) {
    const queue = await DBHelper.findOne(this.queueRepository, {
      where: { id: queueId },
      relations: [
        'attachments',
        'lineItems',
        'attachments.assignedTo',
        'lineItems.assignedTo',
        'attachments.lineItem',
        'lineItems.order',
        'attachments.lineItem.productSku',
        'attachments.lineItem.order',
        'lineItems.productSku',
        'lineItems.product',
        'lineItems.attachments',
        'lineItems.requests',
        'lineItems.requests.attachments',
        'lineItems.productSku.artworkType',
      ],
    });

    if (!queue) {
      throw new NotFoundException('Queue not found');
    }

    const userId = (currentUser as any).user;
    const user = await DBHelper.findOne(this.userRepository, {
      where: { id: userId, isActive: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }
    const attachments = queue.attachments
      .filter(attachment => attachment.assignedTo?.id === user.id)
      .sort((a, b) => {
        const hasPriorityA = !!a.lineItem?.priority;
        const hasPriorityB = !!b.lineItem?.priority;

        if (hasPriorityA && !hasPriorityB) return -1; // a comes first
        if (!hasPriorityA && hasPriorityB) return 1; // b comes first
        if (!hasPriorityA && !hasPriorityB) {
          // Convert order numbers to numbers for proper numeric comparison
          const orderNumA = parseInt(
            a.lineItem.order.shopifyOrderNumber || '0',
            10,
          );
          const orderNumB = parseInt(
            b.lineItem.order.shopifyOrderNumber || '0',
            10,
          );
          return orderNumA - orderNumB; // ascending order
        }
        return 0;
      })
      .map(attachment => ({
        id: attachment.id,
        priority: attachment.lineItem?.priority,
        filename: attachment.filename,
        attachment_url: attachment.url,
        cutout_pro_url: attachment.cutoutProImageUrl,
        cropType: attachment.lineItem?.productSku?.cropType,
        order_number: attachment.lineItem?.order?.shopifyOrderNumber,
        order_date: attachment.lineItem?.order?.orderDate,
        assigned_to: attachment.assignedTo
          ? userFullName(attachment.assignedTo)
          : null,
        attachment_status: attachment.status,
        line_item_status: attachment.lineItem?.status,
        flagged: attachment.lineItem?.flagged,
      }));
    const lineItems = queue.lineItems
      .filter(lineItem => lineItem.assignedTo?.id === user.id)
      .sort((a, b) => {
        const hasPriorityA = !!a.priority;
        const hasPriorityB = !!b.priority;

        if (hasPriorityA && !hasPriorityB) return -1; // a comes first
        if (!hasPriorityA && hasPriorityB) return 1; // b comes first
        if (!hasPriorityA && !hasPriorityB) {
          // Convert order numbers to numbers for proper numeric comparison
          const orderNumA = parseInt(a.order.shopifyOrderNumber || '0', 10);
          const orderNumB = parseInt(b.order.shopifyOrderNumber || '0', 10);
          return orderNumA - orderNumB; // ascending order
        }
        return 0;
      })
      .map(lineItem => ({
        id: lineItem.id,
        priority: lineItem.priority,
        sku: lineItem.productSku.sku,
        product_name: lineItem.product?.name,
        attachments: lineItem.attachments
          .filter(
            attachment =>
              attachment.filename &&
              !attachment.filename.includes(lineItem.shopifyItemId.toString()),
          )
          .map(attachment => ({
            id: attachment.id,
            name: attachment.filename,
            status: attachment.status,
            attachment_url: attachment.url,
            cutout_pro_url: attachment.cutoutProImageUrl,
            completed_art_file_url: attachment.completedArtFileUrl,
          })),
        rejected_attachments: lineItem.attachments
          .filter(
            attachment =>
              attachment.filename &&
              attachment.filename.includes(lineItem.shopifyItemId.toString()),
          )
          .map(attachment => ({
            id: attachment.id,
            name: attachment.filename,
            status: attachment.status,
            completed_art_file_url: attachment.completedArtFileUrl,
            created_at: attachment.createdAt,
          })),
        order_number: lineItem.order.shopifyOrderNumber,
        order_date: lineItem.order.orderDate,
        line_item_status: lineItem.status,
        crop_type: lineItem.productSku.cropType,
        quantity: lineItem.quantity,
        artwork_type: lineItem.productSku?.artworkType?.name,
        assigned_to: lineItem.assignedTo
          ? userFullName(lineItem.assignedTo)
          : null,
        line_item_properties: lineItem.metadata,
        flagged: lineItem.flagged,
        requested_data: lineItem.requests.map(request => ({
          id: request.id,
          attachments: request.attachments.map(attachment => ({
            id: attachment.id,
            attachment_url: attachment.url,
            filename: attachment.filename,
          })),
          notes: request.notes,
          created_at: request.createdAt,
        })),
      }));

    if (attachments.length <= 0 && lineItems.length <= 0) {
      const queueItem = await this.assignQueueItems(currentUser, queueId);
      return queueItem;
    } else {
      return {
        attachments: attachments[0],
        lineItems: lineItems[0],
      };
    }
  }

  async updateQueueItemStatus(
    currentUser: User,
    queuePayload: {
      queueId: string;
      itemId: string;
      action: string;
      type: string;
    },
  ) {
    const userId = (currentUser as any).user;
    const user = await DBHelper.findOne(this.userRepository, {
      where: { id: userId, isActive: true },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const { queueId, itemId, action, type } = queuePayload;

    if (type === 'attachment') {
      const attachment = await DBHelper.findOne(this.attachmentRepository, {
        where: { id: itemId },
        relations: [
          'lineItem',
          'lineItem.order',
          'lineItem.productSku',
          'lineItem.product',
          'assignedTo',
        ],
      });
      if (!attachment) {
        throw new NotFoundException('Attachment not found');
      }
      if (attachment.assignedTo?.id !== user.id) {
        throw new ForbiddenException(
          'You are not authorized to update this attachment',
        );
      }

      if (action === 'approve') {
        await checkAttachmentStatusTransition(
          attachment.status,
          'Crop Approved',
        );
        attachment.status = 'Crop Approved';
        (attachment as any).queue = null;
        (attachment as any).assignedTo = null;
        await this.attachmentRepository.save(attachment);

        const lineItem = await DBHelper.findOne(this.lineItemRepository, {
          where: { id: attachment.lineItem.id },
          relations: ['attachments', 'productSku', 'productSku.artworkType'],
        });

        if (lineItem) {
          const allAttachmentsApproved = lineItem.attachments.every(
            att => att.status === 'Crop Approved',
          );
          if (allAttachmentsApproved) {
            if (
              lineItem.productSku.requireTemplate &&
              !lineItem.productSku.artworkRequired &&
              lineItem.productSku.artworkType.name === 'Crop'
            ) {
              let templatePlacementQueue = await DBHelper.findOne(
                this.queueRepository,
                {
                  where: { name: 'Template Placement' },
                },
              );
              if (!templatePlacementQueue) {
                templatePlacementQueue = await this.queueRepository.create({
                  name: 'Template Placement',
                });
                await this.queueRepository.save(templatePlacementQueue);
              }
              checkStatusTransition(lineItem.status, 'Template Placement');
              lineItem.status = 'Template Placement';
              lineItem.queue = templatePlacementQueue;
              await this.lineItemRepository.save(lineItem);
            } else {
              checkStatusTransition(lineItem.status, 'Ready for Vendor');
              lineItem.status = 'Ready for Vendor';
              await this.lineItemRepository.save(lineItem);
            }
          }
        }
      }

      if (action === 'deny') {
        await checkAttachmentStatusTransition(attachment.status, 'Crop Denied');
        attachment.status = 'Crop Denied';
        let cropNeededQueue = await DBHelper.findOne(this.queueRepository, {
          where: { name: 'Crop Needed' },
        });
        if (!cropNeededQueue) {
          cropNeededQueue = await this.queueRepository.create({
            name: 'Crop Needed',
          });
          await this.queueRepository.save(cropNeededQueue);
        }
        attachment.queue = cropNeededQueue;
        (attachment as any).assignedTo = null;
        await this.attachmentRepository.save(attachment);
        const currentStatus = attachment.lineItem.status;
        if (
          currentStatus !== 'Crop Needed' &&
          currentStatus !== 'Awaiting Customer Response'
        ) {
          checkStatusTransition(currentStatus, 'Crop Needed');
          attachment.lineItem.status = 'Crop Needed';
          await this.lineItemRepository.save(attachment.lineItem);
        }
      }

      if (action === 'new-image-requested') {
        try {
          await checkAttachmentStatusTransition(
            attachment.status,
            'New Image Requested',
          );
          attachment.status = 'New Image Requested';
          (attachment as any).queue = null;
          (attachment as any).assignedTo = null;
          await this.attachmentRepository.save(attachment);
          if (attachment.lineItem.status !== 'Awaiting Customer Response') {
            checkStatusTransition(
              attachment.lineItem.status,
              'Awaiting Customer Response',
            );
            attachment.lineItem.status = 'Awaiting Customer Response';
            await this.lineItemRepository.save(attachment.lineItem);
          }

          // Handle customer communication workflow
          const order = attachment.lineItem.order;
          const customerName =
            `${order.customerFirstName} ${order.customerLastName}`.trim();

          // Check if there's already an active communication workflow for this line item
          const hasActiveCommunication =
            await this.newImageRequestQueue.hasActiveWorkflowForLineItem(
              attachment.lineItem.id,
            );

          if (hasActiveCommunication) {
            this.logger.log(
              `Communication workflow already active for line item ${attachment.lineItem.id}, skipping new reminders`,
            );
            // Don't send any communication - let existing workflow handle it
          } else {
            this.logger.log(
              `No active communication workflow for line item ${attachment.lineItem.id}, scheduling new reminders`,
            );
            // Schedule communication reminders (SMS first, then email 4 hours later)
            await this.newImageRequestQueue.scheduleWorkflow(
              attachment.lineItem.id,
              order.shopifyOrderNumber,
              order.customerPhoneNumber || '',
              order.customerEmail,
              customerName,
              attachment.lineItem.product?.name ||
                attachment.lineItem.productSku?.sku,
              attachment.id,
            );

            this.logger.log(
              `Successfully scheduled new image request workflow for line item ${attachment.lineItem.id}, attachment ${attachment.id}`,
            );
          }
        } catch (error) {
          console.error('Failed to schedule communication reminders:', error);
          // Don't throw error to avoid breaking the workflow
        }
      }

      if (action === 'cs-cancel') {
        // TODO, update either single image, or all images of line Item. Requiremnet to be confirmed
      }
    }
    const queueItem = await this.getQueueItem(currentUser, queueId);
    return queueItem;
  }

  async uploadCompletedArtFile(
    currentUser: User,
    attachmentId: string,
    completedArtFileUrl: string,
    queueId: string,
  ) {
    const attachment = await DBHelper.findOne(this.attachmentRepository, {
      where: { id: attachmentId },
      relations: ['lineItem', 'queue', 'assignedTo'],
    });
    if (!attachment) {
      throw new NotFoundException('Attachment not found');
    }
    const userId = (currentUser as any).user;
    const user = await DBHelper.findOne(this.userRepository, {
      where: { id: userId, isActive: true },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (attachment.assignedTo?.id !== user.id) {
      throw new ForbiddenException(
        'You are not authorized to update this attachment',
      );
    }
    try {
      await this.attachmentRepository.manager.transaction(
        async transactionalEntityManager => {
          await checkAttachmentStatusTransition(
            attachment.status,
            'Crop Approved',
          );
          attachment.completedArtFileUrl = completedArtFileUrl;
          attachment.status = 'Crop Approved';
          (attachment as any).queue = null;
          (attachment as any).assignedTo = null;
          await transactionalEntityManager.save(attachment);

          const lineItem = await transactionalEntityManager.findOne(LineItem, {
            where: { id: attachment.lineItem.id },
            relations: ['attachments', 'productSku', 'productSku.artworkType'],
          });

          if (lineItem) {
            const allAttachmentsApproved = lineItem.attachments.every(
              att => att.status === 'Crop Approved',
            );
            if (allAttachmentsApproved) {
              if (
                lineItem.productSku.requireTemplate &&
                !lineItem.productSku.artworkRequired &&
                lineItem.productSku.artworkType.name === 'Crop'
              ) {
                let templatePlacementQueue = await DBHelper.findOne(
                  this.queueRepository,
                  {
                    where: { name: 'Template Placement' },
                  },
                );
                if (!templatePlacementQueue) {
                  templatePlacementQueue = await this.queueRepository.create({
                    name: 'Template Placement',
                  });
                  await this.queueRepository.save(templatePlacementQueue);
                }
                checkStatusTransition(lineItem.status, 'Template Placement');
                lineItem.status = 'Template Placement';
                lineItem.queue = templatePlacementQueue;
                await transactionalEntityManager.save(lineItem);
              } else {
                checkStatusTransition(lineItem.status, 'Ready for Vendor');
                lineItem.status = 'Ready for Vendor';
                await transactionalEntityManager.save(lineItem);
              }
            }
          }
        },
      );
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to update attachment and line item: ${error.message}`,
      );
    }
    const queueItem = await this.getQueueItem(currentUser, queueId);
    return queueItem;
  }

  async uploadTemplatePlacementFile(
    currentUser: User,
    lineItemId: string,
    templatePlacementFileUrl: string,
    queueId: string,
  ) {
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
      relations: [
        'attachments',
        'assignedTo',
        'productSku',
        'order',
        'product',
      ],
    });
    if (!lineItem) {
      throw new NotFoundException('Line item not found');
    }
    const userId = (currentUser as any).user;
    const user = await DBHelper.findOne(this.userRepository, {
      where: { id: userId, isActive: true },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (lineItem.assignedTo?.id !== user.id) {
      throw new ForbiddenException(
        'You are not authorized to update this line item',
      );
    }

    let shouldTriggerCustomerApproval = false;

    try {
      await this.lineItemRepository.manager.transaction(
        async transactionalEntityManager => {
          let mimetype = 'image/jpeg';

          if (templatePlacementFileUrl) {
            const urlLower = templatePlacementFileUrl.toLowerCase();
            if (urlLower.endsWith('.png')) {
              mimetype = 'image/png';
            } else if (
              urlLower.endsWith('.jpg') ||
              urlLower.endsWith('.jpeg')
            ) {
              mimetype = 'image/jpeg';
            } else if (urlLower.endsWith('.gif')) {
              mimetype = 'image/gif';
            } else if (urlLower.endsWith('.webp')) {
              mimetype = 'image/webp';
            } else if (urlLower.endsWith('.pdf')) {
              mimetype = 'application/pdf';
            }
          }
          const artFileAttachments = lineItem.attachments.filter(
            attachment =>
              attachment.filename &&
              attachment.filename.includes(lineItem.shopifyItemId.toString()),
          );

          let artFileAttachmentsCount = 0;
          artFileAttachmentsCount = +artFileAttachments.length;

          const filename =
            lineItem.productSku.workflowCategory ===
            WorkflowCategory.ART_TEMPLATE_AND_APPROVAL
              ? `${lineItem.shopifyItemId} - Art File - ${artFileAttachmentsCount}`
              : 'Template Art File';
          const attachment = this.attachmentRepository.create({
            filename: filename,
            url: templatePlacementFileUrl,
            completedArtFileUrl: templatePlacementFileUrl,
            mimetype: mimetype,
            size: 0,
          });
          await transactionalEntityManager.save(attachment);

          lineItem.attachments.push(attachment);
          await transactionalEntityManager.save(lineItem);
          if (
            lineItem.productSku.requireCustomerArtworkApproval &&
            lineItem.productSku.workflowCategory ===
              WorkflowCategory.ART_TEMPLATE_AND_APPROVAL
          ) {
            checkStatusTransition(lineItem.status, 'Customer Approval Pending');
            lineItem.status = 'Customer Approval Pending';
            (lineItem as any).queue = null;
            await transactionalEntityManager.save(lineItem);
            shouldTriggerCustomerApproval = true;
          } else {
            checkStatusTransition(lineItem.status, 'Ready for Vendor');
            lineItem.status = 'Ready for Vendor';
            (lineItem as any).queue = null;
            (lineItem as any).assignedTo = null;
            await transactionalEntityManager.save(lineItem);
          }
        },
      );

      // Trigger customer approval workflow if needed
      if (shouldTriggerCustomerApproval && lineItem.order) {
        try {
          const customerName = `${lineItem.order.customerFirstName} ${lineItem.order.customerLastName}`;
          const productName = lineItem?.product?.name || 'Custom Product';

          await this.customerApprovalWorkflow.scheduleWorkflow(
            lineItemId,
            lineItem.order.shopifyOrderNumber,
            lineItem.order.customerPhoneNumber,
            lineItem.order.customerEmail,
            customerName,
            productName,
          );

          this.logger.log(
            `Customer approval workflow scheduled for line item ${lineItemId}, order ${lineItem.order.shopifyOrderNumber}`,
          );
        } catch (workflowError) {
          this.logger.error(
            `Failed to schedule customer approval workflow for line item ${lineItemId}: ${workflowError.message}`,
            workflowError.stack,
          );
          // Don't throw the error to avoid failing the main operation
        }
      }
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to upload template placement file: ${error.message}`,
      );
    }

    const queueItem = await this.getQueueItem(currentUser, queueId);
    return queueItem;
  }

  async customerResponseToArtwork(itemNumber: string, action: string) {
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { itemNumber },
      relations: ['attachments', 'assignedTo', 'productSku'],
    });
    if (!lineItem) {
      throw new NotFoundException('Line item not found');
    }
    const lineItemId = lineItem.id;
    const artfileAttachment = lineItem.attachments?.find(
      attachment =>
        attachment.filename?.toLowerCase().includes('art file') &&
        attachment.status === 'pending',
    );

    if (!artfileAttachment) {
      throw new NotFoundException(
        'No pending artfile attachment found for this line item',
      );
    }

    if (action === 'approved') {
      checkAttachmentStatusTransition(
        artfileAttachment.status,
        AttachmentStatus.CUSTOMER_APPROVED,
      );
      artfileAttachment.status = AttachmentStatus.CUSTOMER_APPROVED;
      await this.attachmentRepository.save(artfileAttachment);

      // Update line item status
      checkStatusTransition(lineItem.status, LineItemStatus.READY_FOR_VENDOR);
      lineItem.status = LineItemStatus.READY_FOR_VENDOR;
      (lineItem as any).queue = null;
      (lineItem as any).assignedTo = null;
      await this.lineItemRepository.save(lineItem);

      this.logger.log(
        `Customer approved artwork for line item ${lineItemId}. Artfile attachment ${artfileAttachment.id} status set to 'Ready for Vendor'`,
      );
    } else if (action === 'rejected') {
      checkAttachmentStatusTransition(
        artfileAttachment.status,
        AttachmentStatus.CUSTOMER_REJECTED,
      );
      artfileAttachment.status = AttachmentStatus.CUSTOMER_REJECTED;
      await this.attachmentRepository.save(artfileAttachment);

      // Update line item status
      checkStatusTransition(lineItem.status, LineItemStatus.REVISION_REQUESTED);
      lineItem.status = LineItemStatus.REVISION_REQUESTED;

      // Assign to Artwork Revision queue
      let revisionRequestedQueue = await DBHelper.findOne(
        this.queueRepository,
        {
          where: { name: 'Artwork Revision' },
        },
      );
      if (!revisionRequestedQueue) {
        revisionRequestedQueue = await this.queueRepository.create({
          name: 'Artwork Revision',
        });
        await this.queueRepository.save(revisionRequestedQueue);
      }
      lineItem.queue = revisionRequestedQueue;
      await this.lineItemRepository.save(lineItem);

      this.logger.log(
        `Customer rejected artwork for line item ${lineItemId}. Artfile attachment ${artfileAttachment.id} status set to 'Revision Requested'`,
      );
    }
    // Cancel customer approval workflow since customer has responded
    try {
      await this.customerApprovalWorkflow.cancelCommunicationsForLineItem(
        lineItemId,
      );
      this.logger.log(
        `Cancelled customer approval communications for line item ${lineItemId} due to customer response: ${action}`,
      );
    } catch (workflowError) {
      this.logger.error(
        `Failed to cancel customer approval communications for line item ${lineItemId}: ${workflowError.message}`,
        workflowError.stack,
      );
    }
  }

  async addArtworkRequest(
    body: {
      lineItemId: string;
      attachmentUrls: string[];
      notes: string;
      queueId: string;
    },
    currentUser: User,
  ) {
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: body.lineItemId },
    });
    if (!lineItem) {
      throw new NotFoundException('Line item not found');
    }

    const queue = await DBHelper.findOne(this.queueRepository, {
      where: { id: body.queueId },
    });
    if (!queue) {
      throw new NotFoundException('Queue not found');
    }

    const request = this.lineItemRequestRepository.create({
      lineItem: lineItem,
      notes: body.notes,
      type: RequestType.ARTWORK,
    });
    const savedRequest = await this.lineItemRequestRepository.save(request);
    const attachments = body.attachmentUrls.map(url => ({
      url: url,
      filename: 'Artwork Request',
      mimetype: 'image/png',
      size: 0,
      request: savedRequest,
    }));
    await this.attachmentRepository.save(attachments);
    return await this.getQueueItem(currentUser, queue.id);
  }
}
