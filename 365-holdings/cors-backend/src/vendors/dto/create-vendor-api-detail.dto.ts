import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean } from 'class-validator';

export class CreateVendorApiDetailDto {
  @ApiProperty({ example: 'REST' })
  @IsString()
  integrationType: string;

  @ApiProperty({ example: 'https://api.vendor.com' })
  @IsString()
  apiUrl: string;

  @ApiProperty({ example: 'api-key-123' })
  @IsString()
  apiKey: string;

  @ApiProperty({ example: 'json' })
  @IsString()
  apiOrderFormat: string;

  @ApiProperty({ example: 'SFTP' })
  @IsString()
  flatFileTransfer: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  syncOrderStatus: boolean;

  @ApiProperty({ example: false })
  @IsBoolean()
  synctrackingNumber: boolean;
}
