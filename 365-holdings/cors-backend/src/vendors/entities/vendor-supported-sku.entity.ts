import { Entity, Column, <PERSON>To<PERSON>ne, Join<PERSON>olumn } from 'typeorm';
import { Vendor } from './vendor.entity';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';

@Entity('vendor_supported_skus')
export class VendorSupportedSku extends BaseEntity {
  @ManyToOne(() => Vendor, vendor => vendor.supportedSkus)
  @JoinColumn({ name: 'vendorId' })
  vendor: Vendor;

  @ManyToOne(() => ProductSku, productSku => productSku.supportedSkus)
  @JoinColumn({ name: 'productSkuId' })
  productSku: ProductSku;

  @Column('bigint', { nullable: true })
  @ApiProperty({ description: 'Production time in days', example: 3 })
  productionTimeDays: number;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Handles peak season', example: true })
  handlePeakSeason: boolean;

  @Column('bigint', { nullable: true })
  @ApiProperty({ description: 'Peak production time in days', example: 5 })
  peakProductionTimeDays: number;

  @Column('bigint', { nullable: true })
  @ApiProperty({ description: 'Rush production time in days', example: 5 })
  rushProductionTimeDays: number;

  @Column('bigint', { nullable: true })
  @ApiProperty({ description: 'Max capacity per day', example: 100 })
  maxCapacityPerDay: number;

  @Column('decimal', { nullable: true })
  @ApiProperty({ description: 'Base cost', example: 12.99 })
  cost: number;

  @Column('decimal', { nullable: true })
  @ApiProperty({ description: 'Rush cost', example: 5.0 })
  rushCost: number;

  @Column('decimal', { nullable: true })
  @ApiProperty({ description: 'Shipping cost per order', example: 3.5 })
  shippingPerOrder: number;
}
