import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { Vendor } from './vendor.entity';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';

@Entity('vendor_api_details')
export class VendorApiDetail extends BaseEntity {
  @Column({ nullable: true })
  @ApiProperty({ description: 'Type of integration', example: 'REST' })
  integrationType: string;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'API base URL',
    example: 'https://api.vendor.com',
  })
  apiUrl: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'API key', example: 'api-key-123' })
  apiKey: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'API order format', example: 'json' })
  apiOrderFormat: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Flat file transfer method', example: 'SFTP' })
  flatFileTransfer: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Whether order status is synced', example: true })
  syncOrderStatus: boolean;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Whether tracking number is synced',
    example: false,
  })
  synctrackingNumber: boolean;

  @ManyToOne(() => Vendor, vendor => vendor.apiDetails)
  @JoinColumn({ name: 'vendorId' })
  vendor: Vendor;
}
