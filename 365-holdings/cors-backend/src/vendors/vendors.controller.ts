import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
} from '@nestjs/common';
import { VendorsService } from './vendors.service';
import { CreateVendorDto } from './dto/create-vendor.dto';
import { UpdateVendorDto } from './dto/update-vendor.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtGuard } from 'src/auth/guards/jwt-auth.guard';
import { VendorAdvancedFilterDto } from './dto/vendor-advance-filter-dto';
import { PermissionsGuard } from 'src/permissions/permissions.guards';
import { Permissions } from 'src/permissions/permissions.decorator';
import { PermissionResources } from 'src/constants/permission-resources';
import { FieldPermissionsGuard } from 'src/permissions/field-permissions.guard';
import { FieldPermissions } from 'src/permissions/field-permissions.decorator';
import { getFieldPermissions } from 'src/permissions/field-permissions.config';

@UseGuards(JwtGuard, PermissionsGuard, FieldPermissionsGuard)
@ApiBearerAuth()
@ApiTags('Vendors')
@Controller('vendors')
export class VendorsController {
  constructor(private readonly vendorsService: VendorsService) {}

  @Permissions(PermissionResources.VENDORS, 'Create Vendor')
  @Post()
  @ApiOperation({ summary: 'Create a new vendor' })
  @ApiResponse({ status: 201, description: 'Returns the created vendor' })
  create(@Body() createVendorDto: CreateVendorDto) {
    return this.vendorsService.createVendor(createVendorDto);
  }

  @Permissions(PermissionResources.VENDORS, 'View Vendors Listing Page')
  @Get()
  @ApiOperation({ summary: 'Get all vendors' })
  @ApiResponse({ status: 200, description: 'Returns all vendors' })
  @ApiQuery({
    name: 'q',
    required: false,
    type: String,
    example: 'name:like:supplier',
  })
  @ApiQuery({
    name: 'fq',
    required: false,
    type: String,
    example: 'isActive:eq:true',
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    type: String,
    example: 'name:asc',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  async findAll(
    @Query('q') q?: string,
    @Query('fq') fq?: string,
    @Query('sort') sort?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    const relations = ['supportedSkus', 'apiDetails'];
    return this.vendorsService.findAll({ q, fq, sort, page, limit, relations });
  }

  @Permissions(PermissionResources.VENDORS, 'View Vendor Detail')
  @Get(':id')
  @ApiOperation({ summary: 'Get a vendor by ID' })
  @ApiResponse({ status: 200, description: 'Returns the vendor' })
  findOne(@Param('id') id: string, @Query('select') select?: string) {
    return this.vendorsService.findByIdOrThrow(
      id,
      ['supportedSkus', 'apiDetails'],
      select,
    );
  }

  @Permissions(PermissionResources.VENDORS, 'Edit Vendor')
  @FieldPermissions(getFieldPermissions('vendors', 'updateVendor'))
  @Patch(':id')
  @ApiOperation({ summary: 'Update a vendor by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated vendor' })
  patch(@Param('id') id: string, @Body() updateVendorDto: UpdateVendorDto) {
    return this.vendorsService.updateVendor(id, updateVendorDto);
  }

  @Permissions(PermissionResources.VENDORS, 'Edit Vendor')
  @FieldPermissions(getFieldPermissions('vendors', 'updateVendor'))
  @Put(':id')
  @ApiOperation({ summary: 'Update a vendor by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated vendor' })
  put(@Param('id') id: string, @Body() updateVendorDto: UpdateVendorDto) {
    return this.vendorsService.updateVendor(id, updateVendorDto);
  }

  @Permissions(PermissionResources.VENDORS, 'Delete Vendor')
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a vendor by ID' })
  @ApiResponse({ status: 200, description: 'Returns the deleted vendor' })
  remove(@Param('id') id: string) {
    return this.vendorsService.deleteById(id);
  }

  @Permissions(PermissionResources.VENDORS, 'View Vendors Listing Page')
  @Post('advanced-filter')
  @ApiOperation({ summary: 'Filter vendors with advanced conditions' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['filters'],
      properties: {
        filters: {
          type: 'array',
          description:
            'Array of filter conditions. Each inner array represents AND conditions, different arrays are combined with OR.',
          items: {
            type: 'array',
            items: {
              type: 'object',
              required: ['attribute', 'operator', 'value'],
              properties: {
                attribute: {
                  type: 'string',
                  description:
                    'Field name to filter on (e.g., sku, name, type, isActive)',
                  example: 'sku',
                },
                operator: {
                  type: 'string',
                  description: 'Comparison operator (eq,ne,like)',
                  example: 'eq',
                },
                value: {
                  description: 'Value to compare against',
                  example: 'VND-001',
                },
              },
            },
          },
          example: [
            [
              { attribute: 'sku', operator: 'eq', value: 'VND-001' },
              { attribute: 'name', operator: 'like', value: 'Awesome Vendor' },
            ],
            [{ attribute: 'isActive', operator: 'eq', value: true }],
          ],
        },
        page: {
          type: 'integer',
          minimum: 1,
          description: 'Page number for pagination',
          example: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          description: 'Number of items per page',
          example: 10,
        },
      },
    },
  })
  async filterWithAdvancedConditions(@Body() body: VendorAdvancedFilterDto) {
    const { filters, page, limit } = body;

    const result = await this.vendorsService.findAllWithAdvancedFilters({
      filters,
      page,
      limit,
      relations: ['supportedSkus', 'apiDetails'],
    });

    return {
      data: result.data,
      count: result.count,
      page: result.page,
      limit: result.limit,
    };
  }
}
