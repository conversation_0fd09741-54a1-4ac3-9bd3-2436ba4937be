import { Queue } from 'bullmq';
import { getQueueToken } from '@nestjs/bullmq';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import {
  DocumentBuilder,
  SwaggerModule,
  SwaggerCustomOptions,
} from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import * as basicAuth from 'express-basic-auth';
import { useContainer } from 'class-validator';
import { accessEnv } from './env.validation';
import { setupBullBoard } from './bull-board';
const customCss = `
  html {
    filter: invert(1) hue-rotate(180deg);
  }
  img {
    filter: invert(1) hue-rotate(180deg);
  }
`;

// Swagger UI dark mode via custom CSS
const customOptions: SwaggerCustomOptions = {
  customCss: customCss,
  customSiteTitle: 'CORS API (Dark)',
};

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { bodyParser: false });
  app.enableCors({
    origin: '*',
    credentials: true,
  });

  const expressApp = app.getHttpAdapter().getInstance();
  const orderSyncQueue = app.get<Queue>(getQueueToken('order-sync'));
  const shopifyOrderSyncQueue = app.get<Queue>(getQueueToken('shopify-order'));
  const smsQueue = app.get<Queue>(getQueueToken('sms-queue'));
  const emailQueue = app.get<Queue>(getQueueToken('email-queue'));

  setupBullBoard(expressApp, [orderSyncQueue, shopifyOrderSyncQueue, smsQueue, emailQueue]);

  useContainer(app.select(AppModule), { fallbackOnErrors: true });
  app.useGlobalPipes(new ValidationPipe());

  // Basic auth for swagger
  app.use(
    ['/api', '/api-json'],
    basicAuth({
      users: {
        [accessEnv('SWAGGER_USERNAME')]: accessEnv('SWAGGER_PASSWORD'),
      },
      challenge: true,
    }),
  );

  const config = new DocumentBuilder()
    .setTitle('CORS API')
    .setDescription('CORS API Description')
    .setVersion('1.0')
    .addTag('CORS')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, customOptions);
  await app.listen(accessEnv('PORT'), '0.0.0.0');
}
bootstrap();
