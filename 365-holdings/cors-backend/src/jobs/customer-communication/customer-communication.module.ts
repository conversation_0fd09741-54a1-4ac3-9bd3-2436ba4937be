import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SmsQueue } from './sms-queue/sms-queue';
import { EmailQueue } from './email-queue/email-queue';
import { SmsProcessor } from './sms-queue/sms.processor';
import { EmailProcessor } from './email-queue/email.processor';
import { NewImageRequestWorkflow } from './workflows/new-image-request/new-image-request.workflow';
import { CustomerApprovalWorkflow } from './workflows/customer-approval/customer-approval.workflow';
import { LineItem } from '../../orders/entities/line-item.entity';
import { Order } from '../../orders/entities/order.entity';
import { Attachment } from '../../attachments/entities/attachment.entity';
import { EmailUtil } from '../../utils/email.util';
import { SmsUtil } from '../../utils/sms.util';
import { Queue } from 'src/workflow-queues/entities/queue.entity';

@Module({
  imports: [
    BullModule.registerQueue(
      {
        name: 'sms-queue',
        defaultJobOptions: {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      },
      {
        name: 'email-queue',
        defaultJobOptions: {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      },
    ),
    TypeOrmModule.forFeature([LineItem, Order, Attachment, Queue]),
  ],
  providers: [
    SmsQueue,
    EmailQueue,
    SmsProcessor,
    EmailProcessor,
    NewImageRequestWorkflow,
    CustomerApprovalWorkflow,
    EmailUtil,
    SmsUtil,
  ],
  exports: [NewImageRequestWorkflow, CustomerApprovalWorkflow],
})
export class CustomerCommunicationModule {}
