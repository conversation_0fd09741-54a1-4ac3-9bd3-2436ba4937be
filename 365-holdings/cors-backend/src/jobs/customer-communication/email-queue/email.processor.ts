import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LineItem } from '../../../orders/entities/line-item.entity';
import { Attachment } from '../../../attachments/entities/attachment.entity';
import { Queue } from '../../../workflow-queues/entities/queue.entity';
import { Order } from '../../../orders/entities/order.entity';
import { EmailUtil } from '../../../utils/email.util';
import { DBHelper } from '../../../helpers/db.helpers';
import {
  ImageRequestTemplates,
  ImageRequestTemplateData,
} from '../workflows/new-image-request/image-request-templates';
import { CustomerApprovalTemplates } from '../workflows/customer-approval/customer-approval-templates';
import { EmailJobData } from './email-queue';
import { accessEnv } from '../../../env.validation';
import { NewImageRequestWorkflow } from '../workflows/new-image-request/new-image-request.workflow';
import { CustomerApprovalWorkflow } from '../workflows/customer-approval/customer-approval.workflow';
import { AttachmentStatus } from '../../../orders/enums/attachment-status.enum';
import {
  checkAttachmentStatusTransition,
  checkStatusTransition,
} from '../../../utils/workflow.utils';
import { LineItemStatus } from 'src/orders/enums/line-item-status.enum';

@Processor('email-queue')
export class EmailProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailProcessor.name);

  constructor(
    @InjectRepository(LineItem)
    private readonly lineItemRepository: Repository<LineItem>,
    @InjectRepository(Attachment)
    private readonly attachmentRepository: Repository<Attachment>,
    @InjectRepository(Queue)
    private readonly queueRepository: Repository<Queue>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly emailUtil: EmailUtil,
    private readonly NewImageRequestWorkflow: NewImageRequestWorkflow,
    private readonly CustomerApprovalWorkflow: CustomerApprovalWorkflow,
  ) {
    super();
  }

  async process(job: Job<EmailJobData>) {
    const { name, data } = job;

    try {
      this.logger.log(
        `Processing Email job: ${name} for workflow: ${data.workflowType}`,
      );

      switch (data.workflowType) {
        case 'new_image_request':
          await this.processImageRequestEmailJob(data);
          break;
        case 'customer_approval':
          await this.processCustomerApprovalEmailJob(data);
          break;
        case 'customer_approval_timeout':
          await this.processCustomerApprovalTimeoutJob(data);
          break;
        default:
          this.logger.warn(`Unknown Email workflow type: ${data.workflowType}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing Email job ${name}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async processImageRequestEmailJob(data: EmailJobData) {
    const { attempt } = data;
    if (attempt === 4) {
      await this.sendImageRequestFinalNotification(data);
    } else {
      await this.sendImageRequestReminderEmail(data);
    }
  }

  private async processCustomerApprovalEmailJob(data: EmailJobData) {
    await this.sendCustomerApprovalEmail(data);
  }

  private async processCustomerApprovalTimeoutJob(data: EmailJobData) {
    await this.handleCustomerApprovalTimeout(data);
  }

  private async handleCustomerApprovalTimeout(data: EmailJobData) {
    const { lineItemId, orderNumber } = data;

    if (!lineItemId || !orderNumber) {
      this.logger.error(
        `Missing required fields for customer approval timeout: lineItemId=${lineItemId}, orderNumber=${orderNumber}`,
      );
      throw new Error('Missing required fields for customer approval timeout');
    }

    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
    });

    if (!lineItem) {
      this.logger.warn(
        `Line item ${lineItemId} not found during timeout processing`,
      );
      return;
    }

    if (lineItem.status === LineItemStatus.ARTWORK_APPROVAL_PENDING) {
      checkStatusTransition(lineItem.status, LineItemStatus.READY_FOR_VENDOR);
      lineItem.status = LineItemStatus.READY_FOR_VENDOR;
      (lineItem as any).queue = null;
      (lineItem as any).assignedTo = null;
      await this.lineItemRepository.save(lineItem);

      this.logger.log(
        `Auto-approved line item ${lineItemId} (order ${orderNumber}) after 84-hour timeout`,
      );
    } else {
      this.logger.log(
        `Line item ${lineItemId} (order ${orderNumber}) status is already ${lineItem.status}, skipping timeout processing`,
      );
    }
  }

  private async sendCustomerApprovalEmail(data: EmailJobData) {
    const {
      lineItemId,
      orderNumber,
      customerEmail,
      customerName,
      productName,
      approvalUrl,
      attempt,
    } = data;

    // Validate required fields for customer approval
    if (
      !lineItemId ||
      !orderNumber ||
      !customerEmail ||
      !approvalUrl ||
      !customerName ||
      !productName
    ) {
      this.logger.error(
        `Missing required fields for customer approval email: lineItemId=${lineItemId}, orderNumber=${orderNumber}, customerEmail=${customerEmail}, customerName=${customerName}, productName=${productName}`,
      );
      throw new Error('Missing required fields for customer approval email');
    }

    // Check if line item still exists and is in customer approval status
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
    });

    if (!lineItem) {
      this.logger.warn(`Line item ${lineItemId} not found`);
      return;
    }

    // Check if customer has already responded (status changed from Customer Approval Pending)
    if (lineItem.status !== 'Customer Approval Pending') {
      this.logger.log(
        `Customer has already responded for line item ${lineItemId} (status: ${lineItem.status}), cancelling remaining communications`,
      );

      // Cancel all pending communications
      await this.CustomerApprovalWorkflow.cancelCommunicationsForLineItem(
        lineItemId,
      );
      return; // Don't send this communication
    }

    // Generate email content based on attempt number
    const templateData = {
      customerName: customerName as string,
      orderNumber,
      productName: productName as string,
      approvalUrl: approvalUrl as string,
    };

    let emailContent;
    if (attempt === 1) {
      emailContent = CustomerApprovalTemplates.getFirstEmail(templateData);
    } else if (attempt === 2) {
      emailContent = CustomerApprovalTemplates.getSecondEmail(templateData);
    }

    if (emailContent) {
      // Send the email
      await this.emailUtil.sendCustomEmail(
        customerEmail,
        emailContent.subject,
        emailContent.html,
      );

      this.logger.log(
        `Customer Approval Email ${attempt} sent to ${customerEmail} for order ${orderNumber}`,
      );
    }
  }

  private async sendImageRequestReminderEmail(data: EmailJobData) {
    const {
      lineItemId,
      orderNumber,
      customerEmail,
      customerName,
      productName,
      uploadUrl,
      attempt,
    } = data;

    // Validate required fields for new image request
    if (!lineItemId || !orderNumber || !customerName || !productName) {
      this.logger.error(
        `Missing required fields for new image request email: lineItemId=${lineItemId}, orderNumber=${orderNumber}, customerName=${customerName}, productName=${productName}`,
      );
      throw new Error('Missing required fields for new image request email');
    }

    // Check if we've already reached the maximum attempts (7 total)
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
      relations: ['attachments'],
    });

    if (!lineItem) {
      this.logger.warn(`Line item ${lineItemId} not found`);
      return;
    }

    // Check if customer has responded since workflow started
    const hasCustomerResponded = lineItem.attachments?.some(
      att =>
        att.status === 'pending' &&
        att.createdAt >
          (lineItem.currentImageRequestWorkflowStartedAt || new Date(0)),
    );

    if (hasCustomerResponded) {
      this.logger.log(
        `Customer has responded for line item ${lineItemId}, cancelling remaining communications`,
      );
      // Reset image request attempts since customer responded
      lineItem.imageRequestAttempts = 0;
      await this.lineItemRepository.save(lineItem);

      // Cancel all pending communications
      await this.NewImageRequestWorkflow.cancelAllCommunicationsForLineItem(
        lineItemId,
      );

      return; // Don't send this communication
    }

    // Check if we've exceeded maximum attempts
    if (lineItem.imageRequestAttempts >= 6) {
      this.logger.log(
        `Maximum image request attempts (7) reached for line item ${lineItemId}, not sending more communications`,
      );
      return;
    }

    const templateData: ImageRequestTemplateData = {
      customerName,
      orderNumber,
      productName,
      uploadUrl: uploadUrl || '',
    };

    let emailContent;
    if (attempt === 1) {
      emailContent = ImageRequestTemplates.getFirstEmail(templateData);
    } else if (attempt === 2) {
      emailContent = ImageRequestTemplates.getSecondEmail(templateData);
    } else if (attempt === 3) {
      emailContent = ImageRequestTemplates.getThirdEmail(templateData);
    }

    if (emailContent) {
      await this.emailUtil.sendCustomEmail(
        customerEmail,
        emailContent.subject,
        emailContent.html,
      );
    }

    this.logger.log(
      `New Image Request Email ${attempt} sent to ${customerEmail} for order ${orderNumber}`,
    );

    // Update communication state
    if (attempt !== undefined) {
      await this.updateImageRequestCommunicationState(
        lineItemId,
        'email',
        attempt,
      );
    }
  }

  private async sendImageRequestFinalNotification(data: EmailJobData) {
    const {
      lineItemId,
      orderNumber,
      customerEmail,
      customerName,
      productName,
      uploadUrl,
    } = data;

    // Validate required fields for final notification
    if (
      !lineItemId ||
      !orderNumber ||
      !customerName ||
      !productName ||
      !uploadUrl
    ) {
      this.logger.error(
        `Missing required fields for final notification: lineItemId=${lineItemId}, orderNumber=${orderNumber}, customerName=${customerName}, productName=${productName}, uploadUrl=${uploadUrl}`,
      );
      throw new Error('Missing required fields for final notification');
    }
    // Check if customer has responded
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
      relations: ['attachments', 'order'],
    });

    if (!lineItem) {
      this.logger.warn(`Line item ${lineItemId} not found`);
      return;
    }

    // Add null check for attachments
    if (!lineItem.attachments || !Array.isArray(lineItem.attachments)) {
      this.logger.warn(`Line item ${lineItemId} has no attachments array`);
      return;
    }

    // Check if any new images were uploaded after the last communication
    const hasNewImages = lineItem.attachments.some(
      att =>
        att.status === 'pending' &&
        att.createdAt > (lineItem.lastImageRequestSentAt || new Date(0)),
    );

    if (!hasNewImages) {
      checkStatusTransition(
        lineItem.status,
        LineItemStatus.REQUESTED_IMAGE_NOT_PROVIDED,
      );
      lineItem.status = LineItemStatus.REQUESTED_IMAGE_NOT_PROVIDED;
      lineItem.flagged = true;
      lineItem.flagReason = 'Customer did not provide images';
      await this.lineItemRepository.save(lineItem);

      this.logger.log(
        `Marked line item ${lineItemId} as "Requested Image Not Provided" and flagged it`,
      );

      if (lineItem.order) {
        const order = lineItem.order;

        order.flagged = true;
        order.flaggedAt = new Date();
        await this.orderRepository.save(order);
      }

      let flaggedCropsQueue = await DBHelper.findOne(this.queueRepository, {
        where: { name: 'Flagged Crops' },
      });

      if (!flaggedCropsQueue) {
        flaggedCropsQueue = this.queueRepository.create({
          name: 'Flagged Crops',
        });
        flaggedCropsQueue = await this.queueRepository.save(flaggedCropsQueue);
      }

      // Update all attachments with status "New Image Requested" to "New Image Not Provided"
      const attachmentsToUpdate = lineItem.attachments.filter(
        att => att.status === AttachmentStatus.NEW_IMAGE_REQUESTED,
      );

      if (attachmentsToUpdate.length > 0) {
        for (const attachment of attachmentsToUpdate) {
          try {
            checkAttachmentStatusTransition(
              attachment.status,
              AttachmentStatus.REQUESTED_IMAGE_NOT_PROVIDED,
            );
            attachment.status = AttachmentStatus.REQUESTED_IMAGE_NOT_PROVIDED;
            attachment.queue = flaggedCropsQueue;
            await this.attachmentRepository.save(attachment);
          } catch (error) {
            this.logger.error(
              `Failed to update attachment ${attachment.id} status: ${error.message}`,
            );
          }
        }

        this.logger.log(
          `Updated ${attachmentsToUpdate.length} attachments to "New Image Not Provided" and assigned to Flagged Crops queue for line item ${lineItemId}`,
        );
      }
    } else {
      this.logger.log(
        `Customer provided new images for line item ${lineItemId}, not sending final notification`,
      );
    }
  }

  private async updateImageRequestCommunicationState(
    lineItemId: string,
    type: string,
    attempt: string | number,
  ) {
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
    });

    if (lineItem) {
      lineItem.lastImageRequestSentAt = new Date();
      lineItem.imageRequestAttempts = (lineItem.imageRequestAttempts || 0) + 1;

      await this.lineItemRepository.save(lineItem);
      this.logger.log(
        `Updated communication state for line item ${lineItemId}: ${type}_${attempt}`,
      );
    }
  }
}
