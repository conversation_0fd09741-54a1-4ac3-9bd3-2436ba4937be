import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, JobType } from 'bullmq';

export interface EmailJobData {
  workflowType: string;
  customerEmail: string;
  lineItemId?: string;
  orderNumber?: string;
  customerName?: string;
  productName?: string;
  uploadUrl?: string;
  attempt?: number;
  [key: string]: any;
}

@Injectable()
export class EmailQueue {
  private readonly logger = new Logger(EmailQueue.name);

  constructor(
    @InjectQueue('email-queue')
    private readonly queue: Queue,
  ) {}

  async scheduleEmail(
    workflowType: string,
    data: Omit<EmailJobData, 'workflowType'>,
    delay: number,
    jobId: string,
  ) {
    const jobData: EmailJobData = {
      workflowType,
      customerEmail: data.customerEmail,
      ...data,
    };
    await this.queue.add('send-email', jobData, { delay, jobId });

    this.logger.log(`Scheduled ${workflowType} email job: ${jobId}`);
  }

  async cancelJob(jobId: string) {
    try {
      const job = await this.queue.getJob(jobId);
      if (job) {
        await job.remove();
        this.logger.log(`Cancelled email job ${jobId}`);
        return true;
      }
    } catch (error) {
      this.logger.warn(`Failed to cancel email job ${jobId}: ${error.message}`);
    }
    return false;
  }

  // Generic method for cancelling multiple jobs by job IDs
  async cancelJobs(jobIds: string[]) {
    const results = await Promise.all(
      jobIds.map(jobId => this.cancelJob(jobId)),
    );
    return results.filter(result => result).length;
  }

  // Generic method for cancelling all jobs for a specific line item
  async cancelAllJobsForLineItem(lineItemId: string) {
    this.logger.log(
      `Force cancelling all email jobs for line item ${lineItemId}`,
    );

    const jobs = await this.queue.getJobs([
      'delayed',
      'waiting',
      'active',
      'paused',
      'completed',
      'failed',
    ]);
    const jobsToRemove = jobs.filter(job => job.data.lineItemId === lineItemId);

    const jobIds = jobsToRemove.map(job => job.id as string);
    const cancelledCount = await this.cancelJobs(jobIds);

    this.logger.log(
      `Force cancelled ${cancelledCount} email jobs for line item ${lineItemId}`,
    );
    return cancelledCount;
  }

  async getJobs(states: JobType[] = ['delayed', 'waiting']) {
    return this.queue.getJobs(states);
  }
}
