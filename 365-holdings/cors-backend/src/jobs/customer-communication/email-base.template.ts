/**
 * Base Email Template
 * 
 * This file contains the base styles, headers, and footers used across all email templates.
 * It provides a consistent look and feel for all Cuddle Clones email communications.
 */

export class EmailBaseTemplate {
  /**
   * Base email container styles
   * Provides the main wrapper styling for all emails
   */
  static getBaseEmailStyles(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: white; padding: 30px; border-radius: 8px;">
    `;
  }

  /**
   * Email header with logo and social media links
   * Includes Cuddle Clones logo and social media icons
   */
  static getEmailHeader(): string {
    return `
      <table style="width: 100%; margin-bottom: 20px">
        <tr>
          <td style="text-align: left; vertical-align: top; width: 50%">
            <img
              src="https://cuddleclones-dev.myshopify.com/cdn/shop/files/CCBannerlogoresized4_951e266f-7517-4655-b1dd-6b9b84fb5195_200x.png?v=1730096788"
              style="max-width: 120px; height: auto; display: block"
              alt="Cuddle Clones Logo"
            />
          </td>
          <td style="text-align: right; vertical-align: top; width: 50%">
            <table style="margin-left: auto">
              <tr>
                <td style="padding: 2px">
                  <a href="https://www.facebook.com/CuddleClones" target="_blank" referrerpolicy="no-referrer">
                    <img
                      src="https://res.cloudinary.com/cors-bucket/image/upload/v1752583269/zdcjmkbihmowvhugh1jg.jpg"
                      style="width: 30px; height: 30px; border-radius: 4px"
                      alt="Facebook"
                    />
                  </a>
                </td>
                <td style="padding: 2px">
                  <a href="https://www.instagram.com/cuddleclones/" target="_blank" referrerpolicy="no-referrer">
                    <img
                      src="https://res.cloudinary.com/cors-bucket/image/upload/v1752583615/h0ucgkigezhnwasdylok.jpg"
                      style="width: 30px; height: 30px; border-radius: 4px"
                      alt="Instagram"
                    />
                  </a>
                </td>
                <td style="padding: 2px">
                  <a href="https://x.com/cuddleclones" target="_blank" referrerpolicy="no-referrer">
                    <img
                      src="https://res.cloudinary.com/cors-bucket/image/upload/v1752583652/efz6ocf4ezo1b1j8od1b.png"
                      style="width: 30px; height: 30px; border-radius: 4px"
                      alt="Twitter"
                    />
                  </a>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>

      <div style="text-align: center; margin: 20px 0">
        <img
          src="https://res.cloudinary.com/cors-bucket/image/upload/v1752582157/cis9gz7hh1sfof0qv3xa.jpg"
          style="
            max-width: 550px;
            width: 100%;
            height: auto;
            display: block;
            margin: 0 auto;
          "
          alt="Header Banner"
        />
      </div>
    `;
  }

  /**
   * Email footer with company information and contact details
   * Includes address, contact email, and copyright information
   */
  static getEmailFooter(): string {
    return `
      <hr style="border: 1px solid #eee; margin: 30px 0" />

      <table style="width: 100%; color: #444; font-size: 12px; line-height: 1.4;">
        <tr>
          <td style="text-align: left; vertical-align: top; width: 50%;">
            <p style="margin: 5px 0; font-weight: bold">Cuddle Clones.</p>
            <a style="color: #5460c8; text-decoration: none;" target="_blank" referrerpolicy="no-referrer" href="https://www.google.com/maps/place/115+W+Bartges+St+%23401,+Akron,+OH+44311,+USA/@41.0701791,-81.5303082,17z/data=!3m1!4b1!4m5!3m4!1s0x8830d63d67fc61b3:0x14937594603b7682!8m2!3d41.0701791!4d-81.5281195?shorturl=1">
              115 W. Bartges<br />Suite 401, Akron<br /> OH 44311
            </a>
            <p style="margin: 5px 0">All Rights Reserved. © 2025.</p>
          </td>
          <td style="text-align: right; vertical-align: top; width: 50%;">
            <span style="font-weight:bold;">E:</span>
            <a href="mailto:<EMAIL>" style="color: #5460c8; text-decoration: none;"><EMAIL></a>
          </td>
        </tr>
      </table>
        </div>
      </div>
    `;
  }

  /**
   * Standard call-to-action button styles
   * Provides consistent button styling across all emails
   */
  static getCallToActionButton(text: string, url: string, icon?: string): string {
    const iconHtml = icon ? `${icon} ` : '';
    return `
      <div style="text-align: center; margin: 30px 0;">
        <a href="${url}" 
         style="background-color: #5460c8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px;">
          ${iconHtml}${text}
        </a>
      </div>
    `;
  }

  /**
   * Standard paragraph styles
   * Provides consistent text styling
   */
  static getParagraphStyle(): string {
    return 'color: #444; line-height: 1.6; margin-bottom: 15px';
  }

  /**
   * Standard heading styles
   * Provides consistent heading styling
   */
  static getHeadingStyle(): string {
    return 'color: #333; margin: 0 0 20px 0; font-size: 14px';
  }

  /**
   * Standard link styles
   * Provides consistent link styling
   */
  static getLinkStyle(): string {
    return 'color: #5460c8; text-decoration: none;';
  }

  /**
   * Build a complete email with header, content, and footer
   * Helper method to construct a full email template
   */
  static buildEmail(content: string): string {
    return `
      ${this.getBaseEmailStyles()}
        ${this.getEmailHeader()}

        <div style="margin: 30px 0">
          ${content}
        </div>

        ${this.getEmailFooter()}
    `;
  }

  /**
   * Create a styled paragraph with the standard styling
   */
  static createParagraph(text: string): string {
    return `<p style="${this.getParagraphStyle()}">${text}</p>`;
  }

  /**
   * Create a styled heading with the standard styling
   */
  static createHeading(text: string): string {
    return `<h3 style="${this.getHeadingStyle()}">${text}</h3>`;
  }

  /**
   * Create a styled link with the standard styling
   */
  static createLink(text: string, url: string): string {
    return `<a href="${url}" style="${this.getLinkStyle()}">${text}</a>`;
  }

  /**
   * Create an email signature
   */
  static getEmailSignature(): string {
    return `
      <p style="${this.getParagraphStyle()}">
        Thank you
      </p>
      
      <p style="${this.getParagraphStyle()}">
        Cuddle Clones Team
      </p>
    `;
  }
} 