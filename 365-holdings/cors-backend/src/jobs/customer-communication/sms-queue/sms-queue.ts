import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, JobType } from 'bullmq';

export interface SmsJobData {
  workflowType: string; // e.g., 'new_image_request', 'order_confirmation', 'shipping_update'
  customerPhone: string;
  lineItemId?: string;
  orderNumber?: string;
  customerName?: string;
  productName?: string;
  uploadUrl?: string;
  attempt?: number;
  [key: string]: any; // Allow additional properties for different SMS types
}

@Injectable()
export class SmsQueue {
  private readonly logger = new Logger(SmsQueue.name);

  constructor(
    @InjectQueue('sms-queue')
    private readonly queue: Queue,
  ) {}

  // Generic method for scheduling any type of SMS
  async scheduleSms(
    workflowType: string,
    data: Omit<SmsJobData, 'workflowType'>,
    delay: number,
    jobId: string,
  ) {
      const jobData: SmsJobData = {
      workflowType,
      customerPhone: data.customerPhone,
      ...data,
      };

    await this.queue.add('send-sms', jobData, { delay, jobId });

    this.logger.log(`Scheduled ${workflowType} SMS job: ${jobId}`);
  }

  // Generic method for cancelling jobs by job ID
  async cancelJob(jobId: string) {
      try {
        const job = await this.queue.getJob(jobId);
        if (job) {
          await job.remove();
          this.logger.log(`Cancelled SMS job ${jobId}`);
        return true;
        }
      } catch (error) {
        this.logger.warn(`Failed to cancel SMS job ${jobId}: ${error.message}`);
      }
    return false;
  }

  // method for cancelling multiple jobs by job IDs
  async cancelJobs(jobIds: string[]) {
    const results = await Promise.all(
      jobIds.map(jobId => this.cancelJob(jobId)),
    );
    return results.filter(result => result).length;
  }

  async cancelAllJobsForLineItem(lineItemId: string) {
    this.logger.log(
      `Force cancelling all SMS jobs for line item ${lineItemId}`,
    );

    // Get jobs in all states to ensure complete cleanup
    const jobs = await this.queue.getJobs([
      'delayed',
      'waiting',
      'active',
      'paused',
      'completed',
      'failed',
    ]);
    const jobsToRemove = jobs.filter(job => job.data.lineItemId === lineItemId);

    const jobIds = jobsToRemove.map(job => job.id as string);
    const cancelledCount = await this.cancelJobs(jobIds);
    
    this.logger.log(
      `Force cancelled ${cancelledCount} SMS jobs for line item ${lineItemId}`,
    );
    return cancelledCount;
  }

  // Public method to get jobs for coordinator access
  async getJobs(states: JobType[] = ['delayed', 'waiting']) {
    return this.queue.getJobs(states);
  }
}
