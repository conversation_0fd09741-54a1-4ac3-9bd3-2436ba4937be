import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LineItem } from '../../../orders/entities/line-item.entity';
import { SmsUtil } from '../../../utils/sms.util';
import { DBHelper } from '../../../helpers/db.helpers';
import {
  ImageRequestTemplates,
  ImageRequestTemplateData,
} from '../workflows/new-image-request/image-request-templates';
import { SmsJobData } from './sms-queue';
import { NewImageRequestWorkflow } from '../workflows/new-image-request/new-image-request.workflow';
import { CustomerApprovalWorkflow } from '../workflows/customer-approval/customer-approval.workflow';
import { CustomerApprovalTemplates } from '../workflows/customer-approval/customer-approval-templates';
import { TimezoneUtil } from '../../../utils/timezone.util';

@Processor('sms-queue')
export class SmsProcessor extends WorkerHost {
  private readonly logger = new Logger(SmsProcessor.name);

  constructor(
    @InjectRepository(LineItem)
    private readonly lineItemRepository: Repository<LineItem>,
    private readonly smsUtil: SmsUtil,
    private readonly NewImageRequestWorkflow: NewImageRequestWorkflow,
    private readonly CustomerApprovalWorkflow: CustomerApprovalWorkflow,
  ) {
    super();
  }

  async process(job: Job<SmsJobData>) {
    const { name, data } = job;

    try {
      this.logger.log(
        `Processing SMS job: ${name} for workflow: ${data.workflowType}`,
      );

      switch (data.workflowType) {
        case 'new_image_request':
          await this.processImageRequestSmsJob(data);
          break;
        case 'customer_approval':
          await this.processCustomerApprovalSmsJob(data);
          break;
        default:
          this.logger.warn(`Unknown SMS workflow type: ${data.workflowType}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing SMS job ${name}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async processImageRequestSmsJob(data: SmsJobData) {
    await this.sendImageRequestReminderSms(data);
  }

  private async processCustomerApprovalSmsJob(data: SmsJobData) {
    await this.sendCustomerApprovalSms(data);
  }

  private async sendCustomerApprovalSms(data: SmsJobData) {
    const {
      lineItemId,
      orderNumber,
      customerPhone,
      customerName,
      productName,
      approvalUrl,
      attempt,
      countryCode,
    } = data;

    // Validate required fields for customer approval
    if (
      !lineItemId ||
      !orderNumber ||
      !customerPhone ||
      !approvalUrl ||
      !customerName ||
      !productName
    ) {
      this.logger.error(
        `Missing required fields for customer approval SMS: lineItemId=${lineItemId}, orderNumber=${orderNumber}, customerPhone=${customerPhone}, customerName=${customerName}, productName=${productName}`,
      );
      throw new Error('Missing required fields for customer approval SMS');
    }

    // Check if line item still exists and is in customer approval status
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
    });

    if (!lineItem) {
      this.logger.warn(`Line item ${lineItemId} not found`);
      return;
    }

    // Check if customer has already responded (status changed from Customer Approval Pending)
    if (lineItem.status !== 'Customer Approval Pending') {
      this.logger.log(
        `Customer has already responded for line item ${lineItemId} (status: ${lineItem.status}), cancelling remaining communications`,
      );

      // Cancel all pending communications
      await this.CustomerApprovalWorkflow.cancelCommunicationsForLineItem(
        lineItemId,
      );
      return;
    }

    // Check customer's local time before sending SMS (9 AM to 7 PM)
    if (countryCode) {
      const shouldSendSms = TimezoneUtil.shouldSendSms(countryCode);

      if (!shouldSendSms) {
        const customerLocalTime =
          TimezoneUtil.getCustomerLocalTime(countryCode);
        this.logger.log(
          `SMS skipped for customer approval - outside business hours. Line item: ${lineItemId}, Order: ${orderNumber}, Country: ${countryCode}, Customer local time: ${customerLocalTime || 'unknown'}`,
        );
        return; // Skip
      }

      this.logger.log(
        `SMS approved for customer approval - within business hours. Line item: ${lineItemId}, Order: ${orderNumber}, Country: ${countryCode}`,
      );
    } else {
      this.logger.warn(
        `No country code provided for order ${orderNumber}, proceeding with SMS send`,
      );
    }

    // Generate message based on attempt number
    const templateData = {
      customerName: customerName as string,
      orderNumber,
      productName: productName as string,
      approvalUrl: approvalUrl as string,
    };

    let message = '';
    if (attempt === 1) {
      message = CustomerApprovalTemplates.getFirstSms(templateData);
    } else if (attempt === 2) {
      message = CustomerApprovalTemplates.getSecondSms(templateData);
    }

    // Send the SMS
    await this.smsUtil.sendSMS(customerPhone, message);

    this.logger.log(
      `Customer Approval SMS ${attempt} sent to ${customerPhone} for order ${orderNumber}`,
    );
  }

  private async sendImageRequestReminderSms(data: SmsJobData) {
    const {
      lineItemId,
      orderNumber,
      customerPhone,
      customerName,
      productName,
      uploadUrl,
      attempt,
    } = data;

    // Validate required fields for new image request
    if (!lineItemId || !orderNumber || !customerName || !productName) {
      this.logger.error(
        `Missing required fields for new image request SMS: lineItemId=${lineItemId}, orderNumber=${orderNumber}, customerName=${customerName}, productName=${productName}`,
      );
      throw new Error('Missing required fields for new image request SMS');
    }

    // Check if we've already reached the maximum attempts (7 total)
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
      relations: ['attachments'],
    });

    if (!lineItem) {
      this.logger.warn(`Line item ${lineItemId} not found`);
      return;
    }

    // Check if customer has responded since workflow started
    const hasCustomerResponded = lineItem.attachments?.some(
      att =>
        att.status === 'pending' &&
        att.createdAt >
          (lineItem.currentImageRequestWorkflowStartedAt || new Date(0)),
    );

    if (hasCustomerResponded) {
      this.logger.log(
        `Customer has responded for line item ${lineItemId}, cancelling remaining communications`,
      );
      // Reset image request attempts since customer responded
      lineItem.imageRequestAttempts = 0;
      await this.lineItemRepository.save(lineItem);

      // Cancel all pending communications
      await this.NewImageRequestWorkflow.cancelAllCommunicationsForLineItem(
        lineItemId,
      );

      return; // Don't send this communication
    }

    // Check if we've exceeded maximum attempts
    if (lineItem.imageRequestAttempts >= 6) {
      this.logger.log(
        `Maximum image request attempts (7) reached for line item ${lineItemId}, not sending more communications`,
      );
      return;
    }

    const templateData: ImageRequestTemplateData = {
      customerName,
      orderNumber,
      productName,
      uploadUrl: uploadUrl || '',
    };

    let message = '';
    if (attempt === 1) {
      message = ImageRequestTemplates.getFirstSms(templateData);
    } else if (attempt === 2) {
      message = ImageRequestTemplates.getSecondSms(templateData);
    } else if (attempt === 3) {
      message = ImageRequestTemplates.getThirdSms(templateData);
    }

    await this.smsUtil.sendSMS(customerPhone, message);

    this.logger.log(
      `New Image Request SMS ${attempt} sent to ${customerPhone} for order ${orderNumber}`,
    );

    // Update communication state
    if (attempt !== undefined) {
      await this.updateImageRequestCommunicationState(
        lineItemId,
        'sms',
        attempt,
      );
    }
  }

  private async updateImageRequestCommunicationState(
    lineItemId: string,
    type: string,
    attempt: string | number,
  ) {
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
    });

    if (lineItem) {
      lineItem.lastImageRequestSentAt = new Date();
      lineItem.imageRequestAttempts = (lineItem.imageRequestAttempts || 0) + 1;

      await this.lineItemRepository.save(lineItem);
      this.logger.log(
        `Updated communication state for line item ${lineItemId}: ${type}_${attempt}`,
      );
    }
  }
}
