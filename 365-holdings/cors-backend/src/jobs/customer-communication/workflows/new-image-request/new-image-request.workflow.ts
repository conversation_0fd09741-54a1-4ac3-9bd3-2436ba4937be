import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SmsQueue } from '../../sms-queue/sms-queue';
import { EmailQueue } from '../../email-queue/email-queue';
import { accessEnv } from '../../../../env.validation';
import { getNewImageRequestDelays } from './new-image-request.config';
import { LineItem } from '../../../../orders/entities/line-item.entity';
import { Order } from '../../../../orders/entities/order.entity';
import { DBHelper } from '../../../../helpers/db.helpers';
import { WORKFLOW_TAGS } from '../../../../orders/enums/workflow-tags.enum';
import { RandomStringUtil } from '../../../../utils/random-string.util';

@Injectable()
export class NewImageRequestWorkflow {
  private readonly logger = new Logger(NewImageRequestWorkflow.name);

  constructor(
    private readonly smsQueue: SmsQueue,
    private readonly emailQueue: EmailQueue,
    @InjectRepository(LineItem)
    private readonly lineItemRepository: Repository<LineItem>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {}

  // Public methods called by base coordinator
  async scheduleWorkflow(
    lineItemId: string,
    orderNumber: string,
    customerPhone: string,
    customerEmail: string,
    customerName: string,
    productName: string,
    attachmentId?: string, // Optional attachment ID for unique job IDs
  ) {
    return this.scheduleNewImageRequestWorkflow(
      lineItemId,
      orderNumber,
      customerPhone,
      customerEmail,
      customerName,
      productName,
      attachmentId,
    );
  }

  async hasActiveWorkflowForLineItem(lineItemId: string): Promise<boolean> {
    return this.hasActiveNewImageRequestWorkflowForLineItem(lineItemId);
  }

  /**
   * Get all active new image request jobs for a specific line item
   * This method checks both SMS and Email queues for jobs with workflowType 'new_image_request'
   */
  async getNewImageRequestJobs(lineItemId: string) {
    this.logger.log(
      `Getting active new image request jobs for line item ${lineItemId}`,
    );

    // Get jobs from both queues
    const [smsJobs, emailJobs] = await Promise.all([
      this.smsQueue.getJobs(['delayed', 'waiting']),
      this.emailQueue.getJobs(['delayed', 'waiting']),
    ]);

    // Filter for new image request jobs for this line item
    const newImageRequestSmsJobs = smsJobs.filter(
      job =>
        job.data.workflowType === 'new_image_request' &&
        job.data.lineItemId === lineItemId,
    );

    const newImageRequestEmailJobs = emailJobs.filter(
      job =>
        job.data.workflowType === 'new_image_request' &&
        job.data.lineItemId === lineItemId,
    );

    const totalJobs =
      newImageRequestSmsJobs.length + newImageRequestEmailJobs.length;

    this.logger.log(
      `Found ${totalJobs} active new image request jobs for line item ${lineItemId} (${newImageRequestSmsJobs.length} SMS, ${newImageRequestEmailJobs.length} Email)`,
    );

    return {
      smsJobs: newImageRequestSmsJobs,
      emailJobs: newImageRequestEmailJobs,
      totalJobs,
    };
  }

  /**
   * Check if a line item has active new image request jobs
   */
  async hasActiveNewImageRequestJobs(lineItemId: string): Promise<boolean> {
    const jobs = await this.getNewImageRequestJobs(lineItemId);
    return jobs.totalJobs > 0;
  }

  async cancelCommunicationsForLineItem(
    lineItemId: string,
    attachmentId?: string,
  ) {
    this.logger.log(
      `Cancelling communications for line item ${lineItemId}${attachmentId ? `, attachment ${attachmentId}` : ''} due to customer response`,
    );

    if (attachmentId) {
      // Cancel specific attachment's communications
      const jobIdSuffix = `${lineItemId}_${attachmentId}`;

      // Cancel SMS and Email jobs using specific job IDs
      const smsJobIds = [
        `new_image_request_sms_1_${jobIdSuffix}`,
        `new_image_request_sms_2_${jobIdSuffix}`,
        `new_image_request_sms_3_${jobIdSuffix}`,
      ];

      const emailJobIds = [
        `new_image_request_email_1_${jobIdSuffix}`,
        `new_image_request_email_2_${jobIdSuffix}`,
        `new_image_request_email_3_${jobIdSuffix}`,
        `new_image_request_final_notification_${jobIdSuffix}`,
      ];

      await Promise.all([
        this.smsQueue.cancelJobs(smsJobIds),
        this.emailQueue.cancelJobs(emailJobIds),
      ]);
    } else {
      // Cancel all communications for the line item
      await Promise.all([
        this.smsQueue.cancelAllJobsForLineItem(lineItemId),
        this.emailQueue.cancelAllJobsForLineItem(lineItemId),
      ]);
    }

    this.logger.log(
      `Successfully cancelled communications for line item ${lineItemId}${attachmentId ? `, attachment ${attachmentId}` : ''}`,
    );
  }

  // Method to cancel all communications for a line item
  async cancelAllCommunicationsForLineItem(lineItemId: string) {
    this.logger.log(
      `Cancelling all communications for line item ${lineItemId} due to customer response`,
    );

    await Promise.all([
      this.smsQueue.cancelAllJobsForLineItem(lineItemId),
      this.emailQueue.cancelAllJobsForLineItem(lineItemId),
    ]);

    this.logger.log(
      `Successfully cancelled all communications for line item ${lineItemId}`,
    );
  }

  // Private implementation methods
  private async scheduleNewImageRequestWorkflow(
    lineItemId: string,
    orderNumber: string,
    customerPhone: string,
    customerEmail: string,
    customerName: string,
    productName: string,
    attachmentId?: string, // Optional attachment ID for unique job IDs
  ) {
    const frontendUrl = accessEnv('FRONTEND_URL') || 'http://localhost:3000';
    const uploadUrl = `${frontendUrl}/order-status?shopifyOrderNumber=${orderNumber}&customerEmail=${encodeURIComponent(customerEmail)}&newImageRequest`;

    this.logger.log(
      `Scheduling new image request workflow for line item ${lineItemId}, order ${orderNumber}${attachmentId ? `, attachment ${attachmentId}` : ''}`,
    );

    // Set workflow start time on the line item and add workflow tag to order
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
      relations: ['order'],
    });
    console.log(lineItem, 'lineItem');

    if (lineItem) {
      lineItem.currentImageRequestWorkflowStartedAt = new Date();
      lineItem.imageRequestAttempts = 0; // reset attempts for new workflow
      await this.lineItemRepository.save(lineItem);

      // Add workflow tag to order if not already present
      if (lineItem.order) {
        const order = lineItem.order;

        if (!order.workflowTags.includes(WORKFLOW_TAGS.NEW_IMAGE_REQUEST)) {
          order.workflowTags = [
            ...order.workflowTags,
            WORKFLOW_TAGS.NEW_IMAGE_REQUEST,
          ];
          await this.orderRepository.save(order);
          this.logger.log(
            `Added workflow tag '${WORKFLOW_TAGS.NEW_IMAGE_REQUEST}' to order ${orderNumber}`,
          );
        } else {
          this.logger.log(
            `Workflow tag '${WORKFLOW_TAGS.NEW_IMAGE_REQUEST}' already exists for order ${orderNumber}`,
          );
        }
      }
    }

    const delays = getNewImageRequestDelays();

    // job id = lineItemId_attachmentId
    const jobIdSuffix = attachmentId
      ? `${lineItemId}_${attachmentId}_${RandomStringUtil.generate()}`
      : `${lineItemId}_${RandomStringUtil.generate()}`;

    const smsJobs = [
      {
        attempt: 1,
        delay: delays.sms1,
        jobId: `new_image_request_sms_1_${jobIdSuffix}`,
      },
      {
        attempt: 2,
        delay: delays.sms2,
        jobId: `new_image_request_sms_2_${jobIdSuffix}`,
      },
      {
        attempt: 3,
        delay: delays.sms3,
        jobId: `new_image_request_sms_3_${jobIdSuffix}`,
      },
    ];

    for (const jobConfig of smsJobs) {
      await this.smsQueue.scheduleSms(
        'new_image_request',
        {
          lineItemId,
          orderNumber,
          customerPhone,
          customerName,
          productName,
          uploadUrl,
          attempt: jobConfig.attempt,
        },
        jobConfig.delay,
        jobConfig.jobId,
      );
    }

    const emailJobs = [
      {
        attempt: 1,
        delay: delays.email1,
        jobId: `new_image_request_email_1_${jobIdSuffix}`,
      },
      {
        attempt: 2,
        delay: delays.email2,
        jobId: `new_image_request_email_2_${jobIdSuffix}`,
      },
      {
        attempt: 3,
        delay: delays.email3,
        jobId: `new_image_request_email_3_${jobIdSuffix}`,
      },
      {
        attempt: 4,
        delay: delays.finalStatus,
        jobId: `new_image_request_final_notification_${jobIdSuffix}`,
      },
    ];

    for (const jobConfig of emailJobs) {
      await this.emailQueue.scheduleEmail(
        'new_image_request',
        {
          lineItemId,
          orderNumber,
          customerEmail,
          customerName,
          productName,
          uploadUrl,
          attempt: jobConfig.attempt,
        },
        jobConfig.delay,
        jobConfig.jobId,
      );
    }

    this.logger.log(
      `Successfully scheduled new image request workflow for line item ${lineItemId}${attachmentId ? `, attachment ${attachmentId}` : ''}`,
    );
  }

  private async hasActiveNewImageRequestWorkflowForLineItem(
    lineItemId: string,
  ): Promise<boolean> {
    this.logger.log(
      `Checking if line item ${lineItemId} has active new image request workflows`,
    );

    const jobs = await this.getNewImageRequestJobs(lineItemId);
    const hasActiveWorkflow = jobs.totalJobs > 0;

    this.logger.log(
      `Line item ${lineItemId} has active new image request workflows: ${hasActiveWorkflow} (${jobs.totalJobs} total jobs)`,
    );

    return hasActiveWorkflow;
  }
}
