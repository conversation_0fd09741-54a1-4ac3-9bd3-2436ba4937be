import { accessEnv } from '../../../../env.validation';
export interface NewImageRequestTiming {
  sms1: number;
  email1: number;
  sms2: number;
  email2: number;
  sms3: number;
  email3: number;
  finalStatus: number;
}

// New Image Request Workflow Timing (in hours)
export const NEW_IMAGE_REQUEST_TIMING: NewImageRequestTiming = {
  sms1: 0, // Immediate
  email1: 4, // 4 hours after SMS1
  sms2: 72, // 72 hours after SMS1
  email2: 76, // 76 hours (4 hours after SMS2)
  sms3: 144, // 144 hours (72 hours after SMS2)
  email3: 148, // 148 hours (4 hours after SMS3)
  finalStatus: 220, // 220 hours (72 hours after Email3)
};

// Test timing
export const NEW_IMAGE_REQUEST_TEST_TIMING: NewImageRequestTiming = {
  sms1: 0,
  email1: 1 / 60,
  sms2: 2 / 60,
  email2: 3 / 60,
  sms3: 4 / 60,
  email3: 5 / 60,
  finalStatus: 6 / 60,
};

export function getNewImageRequestDelays() {
  const isTestMode = accessEnv('COMMUNICATION_WORKFLOW_TEST') === 'true';
  const timing = isTestMode
    ? NEW_IMAGE_REQUEST_TEST_TIMING
    : NEW_IMAGE_REQUEST_TIMING;

  return {
    sms1: timing.sms1 * 60 * 60 * 1000,
    email1: timing.email1 * 60 * 60 * 1000,
    sms2: timing.sms2 * 60 * 60 * 1000,
    email2: timing.email2 * 60 * 60 * 1000,
    sms3: timing.sms3 * 60 * 60 * 1000,
    email3: timing.email3 * 60 * 60 * 1000,
    finalStatus: timing.finalStatus * 60 * 60 * 1000,
  };
}
