import { EmailBaseTemplate } from '../../email-base.template';

export interface ImageRequestTemplateData {
  customerName: string;
  orderNumber: string;
  productName: string;
  uploadUrl: string;
  rejectionReason?: string; // too dark/blurry/low resolution
}

export class ImageRequestTemplates {
  /**
   * Day 0 - First SMS (Hour 0)
   * Send SMS to Customer: 5 Minutes after reviewer clicks "send"
   */
  static getFirstSms(data: ImageRequestTemplateData): string {
    return `Hi! Cuddle Clones here.\n\n We need a new photo for your order ${data.orderNumber} (${data.productName}) to create the perfect replica.\n Please click here to submit a new image:\n ${data.uploadUrl}. Thank you!`;
  }

  /**
   * Day 3 - Second SMS (Hour 72)
   * Send SMS to Customer: 72 hours after initial sms
   */
  static getSecondSms(data: ImageRequestTemplateData): string {
    return `Action Required:\n To complete your Cuddle Clones order ${data.orderNumber} (${data.productName}), we need a new photo of your pet.\n Please click here to upload it:\n ${data.uploadUrl}. Thank you!`;
  }

  /**
   * Day 6 - Third & Final SMS (Hour 144)
   * Send SMS to Customer: 72 hours after 2nd sms
   */
  static getThirdSms(data: ImageRequestTemplateData): string {
    return `Final Reminder:\n We need new photos to proceed with your order ${data.orderNumber} (${data.productName}).\n Please upload it immediately by clicking here or risk cancelation:\n ${data.uploadUrl}`;
  }

  /**
   * Day 0 - First Email (Hour 4)
   * Send Email to Customer: Send 4 hours after SMS is sent
   */
  static getFirstEmail(data: ImageRequestTemplateData): {
    subject: string;
    html: string;
  } {
    const rejectionReason = data.rejectionReason || 'too dark/blurry/low resolution';
    
    const content = `
      ${EmailBaseTemplate.createHeading(`Hello ${data.customerName}`)}
      
      ${EmailBaseTemplate.createParagraph('Thank you for your recent order with Cuddle Clones!')}
      
      ${EmailBaseTemplate.createParagraph(
        `To create the best possible product for you, we carefully review all submitted photos. Unfortunately, the photo you provided cannot be used because it's <strong>${rejectionReason}</strong>.`
      )}
      
      ${EmailBaseTemplate.createParagraph(
        `To proceed with your order, please ${EmailBaseTemplate.createLink('click here', data.uploadUrl)} to submit a new photo.`
      )}
      
      ${EmailBaseTemplate.createParagraph(
        `For the best results, please ensure that your new photo is clear, well-lit, high-resolution, and that your pet's entire face and body are visible, without any parts being cut off, such as ears, tails, or other features.`
      )}
      
      ${EmailBaseTemplate.createParagraph(
        `Once we receive your new photo, we'll review it promptly so we can start production on your <strong>Custom ${data.productName}</strong>.`
      )}
      
      ${EmailBaseTemplate.createParagraph(
        `If you have any questions or need assistance, please don't hesitate to email us at ${EmailBaseTemplate.createLink('<EMAIL>', 'mailto:<EMAIL>')}. Our Customer Care Team is here to help.`
      )}
      
      ${EmailBaseTemplate.createParagraph('We appreciate your prompt attention to this matter and look forward to creating your custom Cuddle Clone.')}
      
      ${EmailBaseTemplate.createParagraph('Thank you, and have a wonderful day!')}
      
      ${EmailBaseTemplate.createParagraph('Best regards,')}
      
      ${EmailBaseTemplate.createParagraph('The Cuddle Clones Team')}
    `;
    
    return {
      subject: `Action Required: Please Provide a New Photo for Order #${data.orderNumber}`,
      html: EmailBaseTemplate.buildEmail(content),
    };
  }

  /**
   * Day 3 - Second Email (Hour 76)
   * Send Email to Customer: Send 4 hours after 2nd SMS is sent
   */
  static getSecondEmail(data: ImageRequestTemplateData): {
    subject: string;
    html: string;
  } {
    const content = `
      ${EmailBaseTemplate.createHeading(`Hi ${data.customerName}`)}
      
      ${EmailBaseTemplate.createParagraph('We need your help to complete your Cuddle Clones order.')}
      
      ${EmailBaseTemplate.createParagraph('The photo you submitted doesn\'t meet the quality we need to create the best possible replica of your pet.')}
      
      ${EmailBaseTemplate.createParagraph('<strong>Please upload a new, clear, and well-lit photo here:</strong>')}
      
      ${EmailBaseTemplate.getCallToActionButton('Upload New Photo', data.uploadUrl)}
      
      ${EmailBaseTemplate.createParagraph(
        `If you have any questions, please email us at ${EmailBaseTemplate.createLink('<EMAIL>', 'mailto:<EMAIL>')}. We're here to assist you.`
      )}
      
      ${EmailBaseTemplate.createParagraph('Thank you for your prompt attention.')}
      
      ${EmailBaseTemplate.createParagraph('The Cuddle Clones Team')}
    `;

    return {
      subject: `Action Still Required: New Photo Needed to Complete Your Cuddle Clones Order #${data.orderNumber}`,
      html: EmailBaseTemplate.buildEmail(content),
    };
  }

  /**
   * Day 6 - Third & Final Email (Hour 148)
   * Email Customer: Send 4 hours after 3rd SMS is sent
   */
  static getThirdEmail(data: ImageRequestTemplateData): {
    subject: string;
    html: string;
  } {
    const rejectionReason = data.rejectionReason || 'too dark/blurry/low resolution';

    const content = `
      ${EmailBaseTemplate.createHeading(`${data.customerName}, <strong style="color: #5460c8;">Immediate action is required</strong> to complete your Cuddle Clones order.`)}
      
      ${EmailBaseTemplate.createParagraph(
        `The photo you submitted cannot be used due to it being <strong>${rejectionReason}</strong>. 
        If we do not receive a new, clear, and well-lit photo within <strong style="color: #5460c8;">the next 72 hours</strong>, 
        <strong style="color: #5460c8;">your order could be canceled</strong>.`
      )}
      
      ${EmailBaseTemplate.createParagraph(
        `Please <strong>${EmailBaseTemplate.createLink('click here', data.uploadUrl)}</strong> to submit your new photo now.`
      )}
      
      ${EmailBaseTemplate.createParagraph('We want to create the perfect product for you, but we can\'t without a suitable image.')}
      
      ${EmailBaseTemplate.createParagraph(
        `If you have any questions or need assistance, email us at ${EmailBaseTemplate.createLink('<EMAIL>', 'mailto:<EMAIL>')}.`
      )}
      
      ${EmailBaseTemplate.createParagraph('Our Customer Care Team is standing by to help you.')}
      
      ${EmailBaseTemplate.createParagraph('Don\'t miss out on receiving your custom Cuddle Clone.')}
      
      ${EmailBaseTemplate.createParagraph('We look forward to hearing from you soon.')}
      
      ${EmailBaseTemplate.createParagraph('Thank you,')}
      
      ${EmailBaseTemplate.createParagraph('The Cuddle Clones Team')}
    `;
    
    return {
      subject: `Action Required: New Photo Needed to Avoid Potential Cancellation of Order #${data.orderNumber}`,
      html: EmailBaseTemplate.buildEmail(content),
    };
  }

  /**
   * Final Notification Email
   * Email to Customer when they don't provide images after all reminders
   */
  static getImageNotProvidedNotification(data: ImageRequestTemplateData): {
    subject: string;
    html: string;
  } {
    const content = `
      ${EmailBaseTemplate.createHeading(`Dear ${data.customerName}`)}
      
      ${EmailBaseTemplate.createParagraph('This is our final attempt to contact you regarding your Cuddle Clones order.')}
      
      ${EmailBaseTemplate.createParagraph(
        `Despite multiple reminders, we have not received the additional information needed to proceed with your <strong>${data.productName}</strong> order.`
      )}
      
      ${EmailBaseTemplate.createParagraph('<strong style="color: #5460c8;">Your order is now at risk of cancellation.</strong>')}
      
      ${EmailBaseTemplate.createParagraph(
        `Please <strong>${EmailBaseTemplate.createLink('click here', data.uploadUrl)}</strong> to upload your image.`
      )}
      
      ${EmailBaseTemplate.createParagraph(
        `If you have any questions or need assistance, please contact us immediately at ${EmailBaseTemplate.createLink('<EMAIL>', 'mailto:<EMAIL>')}.`
      )}
      
      ${EmailBaseTemplate.createParagraph('We want to create your custom Cuddle Clone, but we need your cooperation to proceed.')}
      
      ${EmailBaseTemplate.createParagraph('Thank you for your attention to this matter.')}
    `;

    return {
      subject: `Your Order #${data.orderNumber} Requires Immediate Attention`,
      html: EmailBaseTemplate.buildEmail(content),
    };
  }
}
