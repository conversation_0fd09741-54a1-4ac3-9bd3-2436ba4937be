import { accessEnv } from '../../../../env.validation';
export interface CustomerApprovalTiming {
  sms1: number;
  email1: number;
  sms2: number;
  email2: number;
  timeout: number;
}

export const CUSTOMER_APPROVAL_TIMING: CustomerApprovalTiming = {
  sms1: 0,
  email1: 0,
  sms2: 60,
  email2: 48,
  timeout: 84,
};

// Test timing
export const CUSTOMER_APPROVAL_TEST_TIMING: CustomerApprovalTiming = {
  sms1: 0,
  email1: 1 / 60,
  sms2: 2 / 60,
  email2: 3 / 60,
  timeout: 4 / 60,
};

export function getCustomerApprovalDelays() {
  const isTestMode = accessEnv('COMMUNICATION_WORKFLOW_TEST') === 'true';
  const timing = isTestMode
    ? CUSTOMER_APPROVAL_TEST_TIMING
    : CUSTOMER_APPROVAL_TIMING;
  return {
    sms1: timing.sms1 * 60 * 60 * 1000,
    email1: timing.email1 * 60 * 60 * 1000,
    sms2: timing.sms2 * 60 * 60 * 1000,
    email2: timing.email2 * 60 * 60 * 1000,
    timeout: timing.timeout * 60 * 60 * 1000,
  };
}
