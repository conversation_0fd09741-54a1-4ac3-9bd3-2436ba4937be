import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SmsQueue } from '../../sms-queue/sms-queue';
import { EmailQueue } from '../../email-queue/email-queue';
import { accessEnv } from '../../../../env.validation';
import { getCustomerApprovalDelays } from './customer-approval.config';
import {
  CustomerApprovalTemplates,
  CustomerApprovalTemplateData,
} from './customer-approval-templates';
import { LineItem } from '../../../../orders/entities/line-item.entity';
import { Order } from '../../../../orders/entities/order.entity';
import { DBHelper } from '../../../../helpers/db.helpers';
import { WORKFLOW_TAGS } from '../../../../orders/enums/workflow-tags.enum';
import { RandomStringUtil } from '../../../../utils/random-string.util';

@Injectable()
export class CustomerApprovalWorkflow {
  private readonly logger = new Logger(CustomerApprovalWorkflow.name);

  constructor(
    private readonly smsQueue: SmsQueue,
    private readonly emailQueue: EmailQueue,
    @InjectRepository(LineItem)
    private readonly lineItemRepository: Repository<LineItem>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {}

  // Public methods called by base coordinator
  async scheduleWorkflow(
    lineItemId: string,
    orderNumber: string,
    customerPhone: string,
    customerEmail: string,
    customerName: string,
    productName: string,
  ) {
    return this.scheduleCustomerApprovalWorkflow(
      lineItemId,
      orderNumber,
      customerPhone,
      customerEmail,
      customerName,
      productName,
    );
  }

  /**
   * Get all active customer approval jobs for a specific line item
   * This method checks both SMS and Email queues for jobs with workflowType 'customer_approval'
   */
  async getCustomerApprovalJobs(lineItemId: string) {
    this.logger.log(
      `Getting active customer approval jobs for line item ${lineItemId}`,
    );

    // Get jobs from both queues
    const [smsJobs, emailJobs] = await Promise.all([
      this.smsQueue.getJobs(['delayed', 'waiting']),
      this.emailQueue.getJobs(['delayed', 'waiting']),
    ]);

    // Filter for customer approval jobs for this line item
    const customerApprovalSmsJobs = smsJobs.filter(
      job =>
        job.data.workflowType === 'customer_approval' &&
        job.data.lineItemId === lineItemId,
    );

    const customerApprovalEmailJobs = emailJobs.filter(
      job =>
        (job.data.workflowType === 'customer_approval' ||
          job.data.workflowType === 'customer_approval_timeout') &&
        job.data.lineItemId === lineItemId,
    );

    const totalJobs =
      customerApprovalSmsJobs.length + customerApprovalEmailJobs.length;

    this.logger.log(
      `Found ${totalJobs} active customer approval jobs for line item ${lineItemId} (${customerApprovalSmsJobs.length} SMS, ${customerApprovalEmailJobs.length} Email)`,
    );

    return {
      smsJobs: customerApprovalSmsJobs,
      emailJobs: customerApprovalEmailJobs,
      totalJobs,
    };
  }

  /**
   * Check if a line item has active customer approval jobs
   */
  async hasActiveCustomerApprovalJobs(lineItemId: string): Promise<boolean> {
    const jobs = await this.getCustomerApprovalJobs(lineItemId);
    return jobs.totalJobs > 0;
  }

  async cancelCommunicationsForLineItem(lineItemId: string) {
    this.logger.log(
      `Cancelling customer approval communications for line item ${lineItemId} due to customer response`,
    );

    // Cancel all communications for the line item
    await Promise.all([
      this.smsQueue.cancelAllJobsForLineItem(lineItemId),
      this.emailQueue.cancelAllJobsForLineItem(lineItemId),
    ]);

    this.logger.log(
      `Successfully cancelled customer approval communications for line item ${lineItemId}`,
    );
  }

  // Private implementation methods
  private async scheduleCustomerApprovalWorkflow(
    lineItemId: string,
    orderNumber: string,
    customerPhone: string,
    customerEmail: string,
    customerName: string,
    productName: string,
  ) {
    const frontendUrl = accessEnv('FRONTEND_URL') || 'http://localhost:3000';
    const approvalUrl = `${frontendUrl}/order-status?shopifyOrderNumber=${orderNumber}&customerEmail=${encodeURIComponent(customerEmail)}&customerApproval`;

    this.logger.log(
      `Scheduling customer approval workflow for line item ${lineItemId}, order ${orderNumber}`,
    );

    // Set workflow start time on the line item and add workflow tag to order
    const lineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItemId },
      relations: ['order'],
    });

    // Get country code
    let countryCode: string | undefined;
    if (lineItem?.order?.billingAddress) {
      const billingAddress = lineItem.order.billingAddress as any;
      countryCode = billingAddress.country_code;
    }

    if (lineItem) {
      // Add workflow tag to order if not already present
      if (lineItem.order) {
        const order = lineItem.order;

        if (!order.workflowTags.includes(WORKFLOW_TAGS.CUSTOMER_APPROVAL)) {
          order.workflowTags = [
            ...order.workflowTags,
            WORKFLOW_TAGS.CUSTOMER_APPROVAL,
          ];
          await this.orderRepository.save(order);
          this.logger.log(
            `Added workflow tag '${WORKFLOW_TAGS.CUSTOMER_APPROVAL}' to order ${orderNumber}`,
          );
        } else {
          this.logger.log(
            `Workflow tag '${WORKFLOW_TAGS.CUSTOMER_APPROVAL}' already exists for order ${orderNumber}`,
          );
        }
      }
    }

    const delays = getCustomerApprovalDelays();
    const templateData: CustomerApprovalTemplateData = {
      customerName,
      orderNumber,
      productName,
      approvalUrl,
    };

    // Schedule SMS jobs
    const smsJobs = [
      {
        attempt: 1,
        delay: delays.sms1,
        jobId: `customer_approval_sms_1_${lineItemId}_${RandomStringUtil.generate()}`,
      },
      {
        attempt: 2,
        delay: delays.sms2,
        jobId: `customer_approval_sms_2_${lineItemId}_${RandomStringUtil.generate()}`,
      },
    ];

    for (const jobConfig of smsJobs) {
      await this.smsQueue.scheduleSms(
        'customer_approval',
        {
          customerPhone,
          lineItemId,
          orderNumber: templateData.orderNumber,
          customerName: templateData.customerName,
          productName: templateData.productName,
          approvalUrl: templateData.approvalUrl,
          attempt: jobConfig.attempt,
          countryCode, // Add country code for timezone checking
        },
        jobConfig.delay,
        jobConfig.jobId,
      );
    }

    // Schedule Email jobs
    const emailJobs = [
      {
        attempt: 1,
        delay: delays.email1,
        jobId: `customer_approval_email_1_${lineItemId}_${RandomStringUtil.generate()}`,
      },
      {
        attempt: 2,
        delay: delays.email2,
        jobId: `customer_approval_email_2_${lineItemId}_${RandomStringUtil.generate()}`,
      },
    ];

    for (const jobConfig of emailJobs) {
      await this.emailQueue.scheduleEmail(
        'customer_approval',
        {
          customerEmail,
          lineItemId,
          orderNumber: templateData.orderNumber,
          customerName: templateData.customerName,
          productName: templateData.productName,
          approvalUrl: templateData.approvalUrl,
          attempt: jobConfig.attempt,
        },
        jobConfig.delay,
        jobConfig.jobId,
      );
    }

    // Schedule timeout job for auto-approval after 84 hours
    const timeoutJobId = `customer_approval_timeout_${lineItemId}_${RandomStringUtil.generate()}`;
    await this.emailQueue.scheduleEmail(
      'customer_approval_timeout',
      {
        lineItemId,
        orderNumber: templateData.orderNumber,
        customerName: templateData.customerName,
        productName: templateData.productName,
      },
      delays.timeout,
      timeoutJobId,
    );

    this.logger.log(
      `Successfully scheduled customer approval workflow for line item ${lineItemId}`,
    );
  }
}
