import { EmailBaseTemplate } from '../../email-base.template';

export interface CustomerApprovalTemplateData {
  customerName: string;
  orderNumber: string;
  productName: string;
  approvalUrl: string;
}

export class CustomerApprovalTemplates {
  /**
   * Day 0 - First SMS (Hour 0)
   * Send SMS to Customer: Send immediately after Item Status updates to "Customer Approval Pending"
   * (Sent between the hours of 9 am - 7 pm customer's local time)
   */
  static getFirstSms(data: CustomerApprovalTemplateData): string {
    return `Great news!\n\nOur artist has finished work on the design of your ${data.productName} from Cuddle Clones and is ready for you to approve!\nClick the link below to review:\n\n${data.approvalUrl}`;
  }

  /**
   * Day 2 - Second SMS (Hour 60)
   * Send SMS to Customer: Send 60 hours after Item Status updates to "Customer Approval Pending"
   * (Sent between the hours of 9 am - 7 pm customer's local time)
   */
  static getSecondSms(data: CustomerApprovalTemplateData): string {
    return `Last Chance: Approve your ${data.productName} Design from Cuddle Clones before it goes into production!\nClick the link below to review:\n\n${data.approvalUrl}`;
  }

  /**
   * Day 0 - First Email (Hour 0)
   * Send Email to Customer: Send immediately after Item Status updates to "Customer Approval Pending"
   */
  static getFirstEmail(data: CustomerApprovalTemplateData): {
    subject: string;
    html: string;
  } {
    const content = `
      ${EmailBaseTemplate.createHeading(`Hello ${data.customerName}`)}
      
      ${EmailBaseTemplate.createParagraph(
        `Great news! Our artist has finished work on the design of your ${data.productName} from Cuddle Clones! 
        Click the link below to review your design. Once approved, we will get to work on creating your new favorite sweater!`,
      )}
      
      ${EmailBaseTemplate.getCallToActionButton('Review Your Design', data.approvalUrl)}
      
      ${EmailBaseTemplate.createParagraph('Thank you')}
      
      ${EmailBaseTemplate.createParagraph('Cuddle Clones Team')}
    `;

    return {
      subject: `Your ${data.productName} from Cuddle Clones is ready for review!`,
      html: EmailBaseTemplate.buildEmail(content),
    };
  }

  /**
   * Day 2 - Second Email (Hour 48)
   * Send Email to Customer: Send 48 hours after Item Status updates to "Customer Approval Pending"
   */
  static getSecondEmail(data: CustomerApprovalTemplateData): {
    subject: string;
    html: string;
  } {
    const content = `
      ${EmailBaseTemplate.createHeading(`Hello ${data.customerName}`)}
      
      ${EmailBaseTemplate.createParagraph(
        `Your ${data.productName} from Cuddle Clones will begin production in the next 24 hours! 
        Click the link below to review your design. Personally, we think you'll love it, but if you don't, 
        this is your last opportunity to suggest any updates before it's stitched up for good!`,
      )}
      
      ${EmailBaseTemplate.getCallToActionButton('Review Your Design', data.approvalUrl)}
      
      ${EmailBaseTemplate.createParagraph('Thank you')}
      
      ${EmailBaseTemplate.createParagraph('Cuddle Clones Team')}
    `;

    return {
      subject: `Last Chance: Approve your ${data.productName} Design!`,
      html: EmailBaseTemplate.buildEmail(content),
    };
  }
}
