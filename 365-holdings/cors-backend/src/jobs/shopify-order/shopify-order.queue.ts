import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { DateHelper } from '../../helpers/date.helper';

@Injectable()
export class ShopifyOrderQueue implements OnModuleInit {
  constructor(@InjectQueue('shopify-order') private readonly queue: Queue) {}

  async onModuleInit() {
    await this.queue.add('shopify-order', {
      runAt: DateHelper.getCurrentDateString(),
    });
  }
}
