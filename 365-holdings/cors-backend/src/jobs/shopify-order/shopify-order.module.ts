// daily-task.module.ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ShopifyOrderQueue } from './shopify-order.queue';
import { ShopifyOrderProcessor } from './shopify-order.processor';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { Order } from '../../orders/entities/order.entity';
import { LineItem } from '../../orders/entities/line-item.entity';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { accessEnv } from 'src/env.validation';
import { OrdersService } from '../../orders/orders.service';
import { Attachment } from '../../attachments/entities/attachment.entity';
import { ProductSku } from '../../product-sku/entities/product-sku.entity';
import { User } from '../../users/entities/user.entity';
import { AttachmentModule } from '../../attachments/attachment.module';
import { CutoutProService } from 'src/common/cutout-pro.service';
import { WorkflowService } from 'src/common/workflow.service';
import { CacheModule } from '@nestjs/cache-manager';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { EmailUtil } from 'src/utils/email.util';
import { Product } from 'src/product-sku/entities/product.entity';
import { DuplicateCutoutImagesUtil } from 'src/utils/duplicate-cutout-images.util';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'shopify-order',
      connection: {
        host: accessEnv('REDIS_HOST'),
        port: accessEnv('REDIS_PORT'),
        password: accessEnv('REDIS_PASSWORD') || '',
        tls: JSON.parse(accessEnv('REDIS_SSL_ENABLED'))
          ? {
              rejectUnauthorized: false,
            }
          : undefined,
      },
    }),
    TypeOrmModule.forFeature([
      Order,
      LineItem,
      Attachment,
      ProductSku,
      User,
      Queue,
      Product,
    ]),
    HttpModule,
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    CacheModule.register(),
    AttachmentModule,
  ],
  providers: [
    OrdersService,
    ShopifyOrderQueue,
    ShopifyOrderProcessor,
    ConfigService,
    WorkflowService,
    CutoutProService,
    EmailUtil,
    DuplicateCutoutImagesUtil,
  ],
})
export class ShopifyOrderModule {}
