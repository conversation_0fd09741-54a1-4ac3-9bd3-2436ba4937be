import {
  Module,
  NestModule,
  RequestMethod,
  MiddlewareConsumer,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import databaseConfig from './config/database.config';
import { RolesModule } from './roles/roles.module';
import { entities } from './entities';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { accessEnv, validate } from './env.validation';
import { ProductSkuModule } from './product-sku/product-sku.module';
import { PermissionsModule } from './permissions/permissions.module';
import { OrdersModule } from './orders/orders.module';
import { IsUniqueConstraint } from './utils/unique-validator';
import { HistoryModule } from './common/history.module';
import { ClsModule } from 'nestjs-cls';
import { JwtService } from '@nestjs/jwt';
import { AttachmentModule } from './attachments/attachment.module';
import { ArtworkTypesModule } from './artwork-types/artwork-types.module';
import { VendorsModule } from './vendors/vendors.module';
import { RawBodyMiddleware } from './common/raw-body.middleware';
import { JsonBodyMiddleware } from './common/json-body.middleware';
import { RouteInfo } from '@nestjs/common/interfaces';
import { OrderSyncModule } from './jobs/order-sync/order-sync.module';
import { BullModule } from '@nestjs/bullmq';
import { ShopifyOrderModule } from './jobs/shopify-order/shopify-order.module';
import { QueuesModule } from './workflow-queues/workflow-queues.module';
import { OrderTrackingModule } from './order-tracking/order-tracking.module';

@Module({
  imports: [
    ClsModule.forRoot({
      middleware: {
        mount: true,
        setup: (cls, req) => {
          try {
            const authHeader = req.headers.authorization;
            if (authHeader && authHeader.startsWith('Bearer ')) {
              const token = authHeader.substring(7);
              const jwtService = new JwtService();
              const decoded = jwtService.decode(token);
              if (decoded && typeof decoded === 'object') {
                cls.set('userId', decoded.sub || decoded.id);
              }
            }
          } catch (error) {
            console.warn('Failed to extract userId from JWT:', error);
          }
        },
      },
    }),
    ConfigModule.forRoot({
      validate,
      isGlobal: true,
      expandVariables: true,
      envFilePath: '.env',
    }),
    ConfigModule.forRoot({
      isGlobal: true,
      expandVariables: true,
      load: [databaseConfig],
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ...configService.get('database'),
        entities,
      }),
    }),
    UsersModule,
    RolesModule,
    AuthModule,
    ProductSkuModule,
    PermissionsModule,
    OrdersModule,
    AttachmentModule,
    HistoryModule,
    ArtworkTypesModule,
    VendorsModule,
    OrderSyncModule,
    ShopifyOrderModule,
    QueuesModule,
    OrderTrackingModule,
    BullModule.forRoot({
      connection: {
        host: accessEnv('REDIS_HOST'),
        port: accessEnv('REDIS_PORT'),
        password: accessEnv('REDIS_PASSWORD') || '',
        tls: JSON.parse(accessEnv('REDIS_SSL_ENABLED'))
          ? {
              rejectUnauthorized: false,
            }
          : undefined,
      },
    }),
    BullModule.registerQueue({
      name: 'order-sync',
    }),
    BullModule.registerQueue({
      name: 'shopify-order',
    }),
  ],
  controllers: [AppController],
  providers: [AppService, IsUniqueConstraint],
})
export class AppModule implements NestModule {
  private rawBodyParsingRoutes: Array<RouteInfo> = [
    {
      path: 'product-sku/webhook/update',
      method: RequestMethod.POST,
    },
    {
      path: 'product-sku/webhook/create',
      method: RequestMethod.POST,
    },
  ];

  public configure(consumer: MiddlewareConsumer): MiddlewareConsumer | void {
    consumer
      .apply(RawBodyMiddleware)
      .forRoutes(...this.rawBodyParsingRoutes)
      .apply(JsonBodyMiddleware)
      .exclude(...this.rawBodyParsingRoutes)
      .forRoutes('*');
  }
}
