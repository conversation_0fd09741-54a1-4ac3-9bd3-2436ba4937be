import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class AddArtworkTypeDto {
  @ApiProperty({
    description: 'Name of the artwork type',
    example: 'Vector',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class ArtworkTypeResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the artwork type',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the artwork type',
    example: 'Hand',
  })
  name: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-05-12T12:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-05-12T12:00:00Z',
  })
  updatedAt: Date;
}
