import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ArtworkTypesService } from './artwork-types.service';
import { ArtworkTypesController } from './artwork-types.controller';
import { ArtworkType } from './entities/artwork-type.entity';
import { HistoryModule } from 'src/common/history.module';

@Module({
  imports: [TypeOrmModule.forFeature([ArtworkType]), HistoryModule],
  controllers: [ArtworkTypesController],
  providers: [ArtworkTypesService],
})
export class ArtworkTypesModule {}
