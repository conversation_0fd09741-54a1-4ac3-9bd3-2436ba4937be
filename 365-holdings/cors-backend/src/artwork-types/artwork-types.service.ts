import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from 'src/common/base.service';
import { DBHelper } from 'src/helpers/db.helpers';
import { ArtworkType } from './entities/artwork-type.entity';
import { AddArtworkTypeDto } from './dto/artwork-type.dto';

@Injectable()
export class ArtworkTypesService extends BaseService<ArtworkType> {
  constructor(
    @InjectRepository(ArtworkType)
    repository: Repository<ArtworkType>,
  ) {
    super(repository);
  }

  async findAllNoPagination(): Promise<ArtworkType[]> {
    return DBHelper.findMany(this.repository);
  }

  async insert(dto: AddArtworkTypeDto): Promise<ArtworkType> {
    return this.create(dto);
  }
}
