export interface BaseMetafield {
  id: string;
  key: string;
  value: string;
  namespace: string;
  type: string;
}

export interface MetafieldEdge {
  node: BaseMetafield;
}

export interface MetafieldConnection {
  edges: MetafieldEdge[];
}

// Variant interfaces
export interface VariantMetafield {
  namespace: string;
  key: string;
  value: string;
}

export interface VariantMetafieldEdge {
  node: VariantMetafield;
}

export interface ProductVariant {
  id: string;
  title: string;
  sku: string | null;
  price: string;
  selectedOptions: {
    name: string;
    value: string;
  }[];
  metafields: {
    edges: VariantMetafieldEdge[];
  };
}

export interface VariantEdge {
  node: ProductVariant;
}

export interface VariantConnection {
  edges: VariantEdge[];
}

// Product interfaces
export interface Product {
  id: string;
  title: string;
  handle: string;
  productType: string;
  status: 'ACTIVE' | 'DRAFT';
  description: string;
  metafields: MetafieldConnection;
  variants: VariantConnection;
  variantId: string;
}

export interface NativeProduct {
  admin_graphql_api_id: string;
  id: string;
  title: string;
  handle: string;
  productType: string;
  status: 'ACTIVE' | 'DRAFT';
  description: string;
  metafields: any;
  variants: any;
  options: any;
  variantId: string;
}

export interface ProductEdge {
  node: Product;
  cursor: string;
}

// Page info interface
export interface PageInfo {
  hasNextPage: boolean;
  endCursor: string;
}

// Cost and throttle interfaces
export interface ThrottleStatus {
  maximumAvailable: number;
  currentlyAvailable: number;
  restoreRate: number;
}

export interface Cost {
  requestedQueryCost: number;
  actualQueryCost: number;
  throttleStatus: ThrottleStatus;
}

export interface Extensions {
  cost: Cost;
}

// Main response interfaces
export interface ProductConnection {
  edges: ProductEdge[];
  pageInfo: PageInfo;
}

export interface GraphQLResponse {
  products: ProductConnection;
  extensions: Extensions;
}
