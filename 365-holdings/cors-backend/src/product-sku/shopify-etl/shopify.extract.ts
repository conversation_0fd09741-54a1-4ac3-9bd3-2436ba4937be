import { GraphQLClient } from 'graphql-request';
import { GraphQLResponse, Product } from './types';
import { accessEnv } from '../../env.validation';

class ShopifyExtractor {
  private client: GraphQLClient;

  constructor() {
    this.initClient();
  }

  private initClient() {
    this.client = new GraphQLClient(
      `https://${accessEnv('SHOPIFY_STORE')}.myshopify.com/admin/api/${accessEnv('SHOPIFY_API_VERSION')}/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': accessEnv('SHOPIFY_ACCESS_TOKEN') as string, // if your API requires authentication
        },
      },
    );
  }

  async fetchShopifyProductsInBatch(
    batchSize: number,
    cursor?: string | undefined,
  ): Promise<GraphQLResponse> {
    try {
      const query = `query {
          products(first: ${batchSize}${cursor ? `, after: "${cursor}"` : ''}) {
            edges {
               node {
                  id
                  title
                  handle
                  status
                  description
                  productType
                  metafields(first: 50, namespace: "cuddleclones") {
                    edges {
                      node {
                        id
                        key
                        value
                        namespace
                        type
                      }
                    }
                  }
                  variants(first: 100) { edges { node { id title sku price selectedOptions { name value } metafields(first: 5) { edges { node { namespace key value } } } } } }
                }
            }
          pageInfo {
             hasNextPage
             endCursor
          }
        }
      }`;

      return await this.client.request(query);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Fetches products from Shopify in batches using cursor-based pagination
   *
   * @param batchSize - Number of products to fetch per API request
   * @param maxProducts - Maximum total number of products to fetch, -1 for unlimited
   * @returns Promise containing array of fetched Shopify products
   *
   * @example
   * // Fetch up to 1000 products in batches of 50
   * const products = await fetchShopifyProducts(50, 1000);
   *
   * // Fetch all products in batches of 100
   * const allProducts = await fetchShopifyProducts(100);
   */
  async fetchShopifyProducts(batchSize: number, maxProducts: number = -1) {
    const products: Product[] = [];
    let nextCursor: string | undefined;

    let index = 0;

    while (true) {
      const {
        products: { edges, pageInfo },
      } = await shopifyExtractClient.fetchShopifyProductsInBatch(
        batchSize,
        nextCursor,
      );

      nextCursor = pageInfo.endCursor;
      index++;

      const productNodes: Product[] = edges.map(edge => edge.node);
      products.push(...productNodes);

      if (maxProducts !== -1 && products.length >= maxProducts) break;
      if (!pageInfo.hasNextPage) break;
    }

    return products;
  }
}

export const shopifyExtractClient = new ShopifyExtractor();
