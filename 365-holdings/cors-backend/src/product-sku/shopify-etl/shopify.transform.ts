import { Product } from './types';
import { accessEnv } from '../../env.validation';

export interface TransformedProduct {
  id: string;
  sku: string;
  title: string;
  handle: string;
  status: string;
  productType: string;
  description: string;
  metafields: any;
  variantNativeOptions: Record<string, any>;
  variantCustomOptions: Record<string, any>;
  variantId?: string;
}

enum ProductType {
  PDP_CUSTOMIZER = 'pdp_customizer',
  PRODUCT_CUSTOMIZER = 'product_customizer',
  CUSTOM_OPTIONS = 'custom_options',
  PRODUCT_KEY_TYPE = 'product_key_type',
}

export abstract class ShopifyProductType {
  abstract transformProduct(product: Product): TransformedProduct[];
}

class ShopifyTransformUtils {
  constructor() {}
  generateCombinationsFromAllVariants(
    allOptions: (string[] | { heading: string; options: string[] })[],
    nativeVariantsWithOptions: {
      [key: string]: {
        id: string;
        selectedOptions: { name: string; value: string }[];
      };
    },
  ) {
    // If allOptions is an empty array, just return the native variants with options
    if (!allOptions || allOptions.length === 0) {
      return Object.keys(nativeVariantsWithOptions).map(sku => ({
        sku,
        nativeOptions: nativeVariantsWithOptions[sku].selectedOptions,
        nativeVariantId: nativeVariantsWithOptions[sku].id,
        customOptions: [],
      }));
    }

    // (Retain original logic if needed for non-empty allOptions)
    const combinations: {
      sku: string;
      nativeOptions: { name: string; value: string }[];
      nativeVariantId: string;
      customOptions: { name: string; value: string }[];
    }[] = [];

    const generateCombinations = (
      baseVariant: string,
      remainingArrays: (string[] | { heading: string; options: string[] })[],
      currentOptions: { name: string; value: string }[] = [],
    ) => {
      if (remainingArrays.length === 0) {
        if (baseVariant && nativeVariantsWithOptions[baseVariant]) {
          combinations.push({
            sku: baseVariant,
            nativeOptions:
              nativeVariantsWithOptions[baseVariant].selectedOptions,
            nativeVariantId: nativeVariantsWithOptions[baseVariant].id,
            customOptions: currentOptions,
          });
        }
        return;
      }

      const currentArray = remainingArrays[0];
      const rest = remainingArrays.slice(1);

      if (Array.isArray(currentArray)) {
        currentArray.forEach(variantSku => {
          if (nativeVariantsWithOptions[variantSku]) {
            generateCombinations(variantSku, rest, currentOptions);
          }
        });
      } else {
        currentArray.options.forEach(option => {
          const newOptions = [
            ...currentOptions,
            { name: currentArray.heading, value: option },
          ];

          if (rest.length === 0 && baseVariant) {
            const customOptionsSuffix = newOptions
              .map(opt => opt.value)
              .join(' - ');

            combinations.push({
              sku: `${baseVariant} - ${customOptionsSuffix}`,
              nativeOptions:
                nativeVariantsWithOptions[baseVariant].selectedOptions,
              nativeVariantId: nativeVariantsWithOptions[baseVariant].id,
              customOptions: newOptions,
            });
          } else {
            generateCombinations(baseVariant, rest, newOptions);
          }
        });
      }
    };

    generateCombinations('', allOptions);

    return combinations;
  }

  categorizeProduct(product: Product): ProductType {
    const CUSTOMIZER_TYPES = [
      ProductType.CUSTOM_OPTIONS,
      ProductType.PDP_CUSTOMIZER,
      ProductType.PRODUCT_CUSTOMIZER,
    ];

    const customizerMetafield = product.metafields.edges.find(edge =>
      CUSTOMIZER_TYPES.includes(edge.node.key as ProductType),
    );

    return customizerMetafield
      ? (customizerMetafield.node.key as ProductType)
      : ProductType.PRODUCT_KEY_TYPE;
  }
}

export class ShopifyPdpCustomizerSku
  extends ShopifyTransformUtils
  implements ShopifyProductType
{
  transformProduct(product: Product): TransformedProduct[] {
    const customOptionsObject = product?.metafields.edges.find(
      field => field.node.key === 'pdp_customizer',
    );

    const nativeVariants = product.variants.edges.map(nv => nv?.node?.sku);
    const nativeVariantsWithOptions = product.variants.edges.reduce(
      (acc, nv) => {
        if (nv?.node?.sku) {
          acc[nv.node.sku] = {
            id: nv.node.id,
            selectedOptions: nv.node.selectedOptions,
          };
        }
        return acc;
      },
      {},
    );

    const uniqueNativeVariants = [
      ...new Set(nativeVariants.filter((v): v is string => v !== null)),
    ];

    const customOptions = JSON.parse(customOptionsObject?.node?.value ?? '');

    const customOptionsSteps = customOptions.steps.filter(
      co => co.native === false,
    );

    let allCustomOptions: string[][] = [];
    if (customOptionsSteps.length) {
      customOptionsSteps.forEach(co => {
        allCustomOptions = [
          ...allCustomOptions,
          ...co.swatches.map(swatch => ({
            heading: co.step_name,
            options: swatch?.options?.map(opt => opt.label) || [],
          })),
        ];
      });
    }

    const skuList = this.generateCombinationsFromAllVariants(
      [uniqueNativeVariants, ...allCustomOptions],
      nativeVariantsWithOptions,
    );

    const transformedProducts = skuList
      .filter(sku => sku != null)
      .map(sku => ({
        id: product.id,
        sku: sku.sku,
        title: product.title,
        handle: product.handle,
        status: product.status,
        description: product.description,
        productType: product.productType,
        metafields: product.metafields.edges,
        variantId: sku.nativeVariantId,
        variantNativeOptions: sku.nativeOptions,
        variantCustomOptions: sku.customOptions || [],
      }));

    return transformedProducts;
  }
}

export class ShopifyCustomOptionsSku
  extends ShopifyTransformUtils
  implements ShopifyProductType
{
  transformProduct(product: Product): TransformedProduct[] {
    const customOptionsObject = product?.metafields.edges.find(
      field => field.node.key === 'custom_options',
    );

    const nativeVariants = product.variants.edges.map(nv => nv?.node?.sku);
    const nativeVariantsWithOptions = product.variants.edges.reduce(
      (acc, nv) => {
        if (nv?.node?.sku) {
          acc[nv.node.sku] = {
            id: nv.node.id,
            selectedOptions: nv.node.selectedOptions,
          };
        }
        return acc;
      },
      {},
    );
    const uniqueNativeVariants = [
      ...new Set(nativeVariants.filter((v): v is string => v !== null)),
    ];

    const customOptions = JSON.parse(customOptionsObject?.node?.value ?? '');
    const allCustomOptions = customOptions.map(co => {
      return {
        heading: co.heading,
        options: co?.options?.map(opt => opt.sku || opt.name) || [],
      };
    });

    const skuList = this.generateCombinationsFromAllVariants(
      [uniqueNativeVariants, ...allCustomOptions],
      nativeVariantsWithOptions,
    );

    const transformedProducts = skuList
      .filter(sku => sku != null)
      .map(sku => ({
        id: product.id,
        sku: sku.sku,
        title: product.title,
        handle: product.handle,
        status: product.status,
        description: product.description,
        productType: product.productType,
        metafields: product.metafields.edges,
        variantId: sku.nativeVariantId,
        variantNativeOptions: sku.nativeOptions,
        variantCustomOptions: sku.customOptions || [],
      }));

    return transformedProducts;
  }
}

export class ShopifyProductCustomizerSku
  extends ShopifyTransformUtils
  implements ShopifyProductType
{
  transformProduct(product: Product): TransformedProduct[] {
    /*  
        Since there are many custom options, we are excluding them from the SKU key
        and only using the native variant for the SKU.
    */
    const skuValue = product?.variants?.edges
      ?.filter(v => v.node?.sku)
      .map(v => ({ sku: v.node.sku!, id: v.node.id }));

    const transformedProducts = skuValue
      .filter(sku => sku.sku != null)
      .map(sku => ({
        id: product.id,
        sku: sku.sku,
        title: product.title,
        handle: product.handle,
        status: product.status,
        description: product.description,
        productType: product.productType,
        metafields: product.metafields.edges,
        variantNativeOptions: [],
        variantCustomOptions: [],
      }));

    return transformedProducts;
  }
}

export class ShopifyProductKeyTypeSku
  extends ShopifyTransformUtils
  implements ShopifyProductType
{
  transformProduct(product: Product): TransformedProduct[] {
    const skuValue = product?.variants?.edges
      ?.filter(v => v.node?.sku)
      .map(v => ({ sku: v.node.sku!, id: v.node.id }));

    const nativeVariantsWithOptions = product.variants.edges.reduce(
      (acc, nv) => {
        if (nv?.node?.sku) {
          acc[nv.node.sku] = {
            id: nv.node.id,
            selectedOptions: nv.node.selectedOptions,
          };
        }
        return acc;
      },
      {},
    );

    const skuList = this.generateCombinationsFromAllVariants(
      [],
      nativeVariantsWithOptions,
    );

    // If skuList is not present or empty, get it from skuValue
    const effectiveSkuList = (skuList && skuList.length > 0)
      ? skuList
      : skuValue.map(sku => ({
          sku: sku.sku,
          nativeOptions: [],
          nativeVariantId: sku.id,
          customOptions: [],
        }));

    const transformedProducts = effectiveSkuList
      .filter(sku => sku.sku != null)
      .map(sku => ({
        id: product.id,
        sku: sku.sku,
        title: product.title,
        handle: product.handle,
        status: product.status,
        description: product.description,
        productType: product.productType,
        metafields: product.metafields.edges,
        variantId: sku.nativeVariantId,
        variantNativeOptions: sku.nativeOptions,
        variantCustomOptions: [],
      }));

    return transformedProducts;
  }
}

class ShopifyTransformService extends ShopifyTransformUtils {
  private transformer = new Map<ProductType, ShopifyProductType>();

  constructor() {
    super();
    this.transformer.set(
      ProductType.PDP_CUSTOMIZER,
      new ShopifyPdpCustomizerSku(),
    );
    this.transformer.set(
      ProductType.CUSTOM_OPTIONS,
      new ShopifyCustomOptionsSku(),
    );
    this.transformer.set(
      ProductType.PRODUCT_CUSTOMIZER,
      new ShopifyProductCustomizerSku(),
    );

    this.transformer.set(
      ProductType.PRODUCT_KEY_TYPE,
      new ShopifyProductKeyTypeSku(),
    );
  }

  transform(product: Product): TransformedProduct[] {
    if (JSON.parse(accessEnv('SKU_TRANSFORMER'))) {
      const nativeVariants = product.variants.edges.map(nv => nv?.node?.sku);
      const nativeVariantsWithOptions = product.variants.edges.reduce(
        (acc, nv) => {
          if (nv?.node?.sku) {
            acc[nv.node.sku] = {
              id: nv.node.id,
              selectedOptions: nv.node.selectedOptions,
            };
          }
          return acc;
        },
        {},
      );

      const uniqueNativeVariants = [...new Set(nativeVariants)];
      const skuList = this.generateCombinationsFromAllVariants(
        [uniqueNativeVariants.filter((v): v is string => v !== null)],
        nativeVariantsWithOptions,
      );
      const transformedProducts = skuList
        .filter(sku => sku != null)
        .map(sku => ({
          id: product.id,
          sku: sku.sku,
          title: product.title,
          handle: product.handle,
          status: product.status,
          description: product.description,
          productType: product.productType,
          metafields: product.metafields.edges,
          variantId: sku.nativeVariantId,
          variantNativeOptions: sku.nativeOptions,
          variantCustomOptions: [],
        }));

      return transformedProducts;
    } else {
      const productType: ProductType = this.categorizeProduct(product);

      if (!productType) {
        return [];
      }

      const transformer = this.transformer.get(productType);

      if (!transformer) {
        throw new Error(
          `No transformer found for product type: ${productType}`,
        );
      }

      return transformer.transformProduct(product);
    }
  }
}

export const shopifyTransformClient = new ShopifyTransformService();
