export enum CroppingMethod {
  MANUAL = 'MANUAL',
  CUTOUT_PRO = 'CUTOUT_PRO',
}

export enum CropType {
  FACE_CUTOUT = 'FACE_CUTOUT',
  BACKGROUND_REMOVAL = 'BACKGROUND_REMOVAL',
}

export enum FileUploadFormat {
  BMP = '.bmp',
  PNG = '.png',
  JPEG = '.jpeg',
}

export enum ImageInheritRule {
  AUTO_INHERIT = 'AUTO_INHERIT',
  REQUIRES_REVIEW = 'REQUIRES_REVIEW',
}

export enum ExceptionHandlingRule {
  QUEUE = 'Send to Image Needed Queue',
  FLAG = 'Auto-Assign from First Image on Order',
}

export enum ShippingMethod {
  STANDARD = 'STANDARD',
  EXPEDITED = 'EXPEDITED',
}

export enum RoutingMethod {
  OMS = 'OMS',
  CORS = 'CORS',
}

export enum VendorAssignmentRule {
  FASTEST_CYCLE_TIME = 'Fastest Cycle Time',
  LOWEST_COST = 'Lowest Cost',
}

export enum AddonLevel {
  LINE_ITEM = 'line_item',
  ORDER = 'order',
}

export enum RelationType {
  PARENT = 'parent',
  CHILD = 'child',
  ADDON = 'addon',
  UPSELL = 'upsell',
}

export enum WorkflowCategory {
  CROP_IMAGE_ONLY = 'Crop Image Only',
  ART_ONLY = 'Art Only',
  CUSTOMER_IMAGE_ONLY = 'Customer Image Only',
  CROP_IMAGE_AND_TEMPLATE = 'Crop Image+Template Placement',
  ART_AND_TEMPLATE = 'Art+Template Placement',
  ART_TEMPLATE_AND_APPROVAL = 'Art+Template Placement+Approval',
  PLUSH = 'Plush',
  ADDON = 'Add-on',
}
