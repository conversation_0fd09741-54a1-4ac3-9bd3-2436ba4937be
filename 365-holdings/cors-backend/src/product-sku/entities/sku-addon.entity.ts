import {
  Entity,
  PrimaryColumn,
  Column,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { RelationType } from '../enums/product-sku.enums';

@Entity('sku_addon')
export class SkuAddon {
  @PrimaryColumn('uuid')
  productSkuId: string;

  @PrimaryColumn('uuid')
  relationSkuId: string;

  @Column({ nullable: true, type: 'enum', enum: RelationType })
  @ApiProperty({ description: 'relation_type', enum: RelationType })
  relationType: RelationType ;
}