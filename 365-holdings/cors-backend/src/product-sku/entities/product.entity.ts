import { Column, Entity, JoinTable, ManyToMany, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';
import { ProductSku } from './product-sku.entity';
import { ProductCategory } from '../enums/product.enums';
import { LineItem } from 'src/orders/entities/line-item.entity';

@Entity({ name: 'products' })
export class Product extends BaseEntity {
  @Column()
  @ApiProperty({
    description: 'The name of the product',
    example: 'Custom Plush Toy',
  })
  name: string;

  @Column({
    type: 'enum',
    enum: ProductCategory,
  })
  @ApiProperty({
    description: 'The category of the product',
    example: 'Plush',
    enum: ProductCategory,
  })
  category: ProductCategory;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'A detailed description of the product',
    example: 'Soft, customizable plush toy.',
  })
  description?: string;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'The Shopify Product ID associated with this product',
    example: '1234567890',
  })
  shopifyProductId?: string;

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({
    description: 'Custom metadata for the product',
    type: Object,
    example: { season: 'Holiday', tags: ['Gift', 'Stuffed Animal'] },
  })
  metadata: Record<string, any>;

  @ManyToMany(() => ProductSku, productSku => productSku.products)
  @JoinTable({
    name: 'product_sku_product_pivot',
    joinColumn: {
      name: 'product_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'sku_id',
      referencedColumnName: 'id',
    },
  })
  @ApiProperty({ description: 'List of SKUs associated with this product' })
  productSku: ProductSku[];

  @OneToMany(() => LineItem, lineItem => lineItem.product, {nullable: true})
  @ApiProperty({
    description: 'List of Line Items having this Product',
  })
  lineItems: LineItem[];
}
