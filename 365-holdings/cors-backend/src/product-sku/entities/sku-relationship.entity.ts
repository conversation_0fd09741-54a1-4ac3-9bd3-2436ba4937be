import { Enti<PERSON>, ManyToOne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';
import { ProductSku } from './product-sku.entity';

@Entity({ name: 'sku_relationships' })
export class SkuRelationship extends BaseEntity {
  @ManyToOne(() => ProductSku, productSku => productSku.childSku)
  @JoinColumn({ name: 'parentSkuId' })
  @ApiProperty({ description: 'Parent SKU' })
  parentSku: ProductSku;

  @ManyToOne(() => ProductSku, productSku => productSku.parentSku)
  @JoinColumn({ name: 'childSkuId' })
  @ApiProperty({ description: 'Child SKU' })
  childSku: ProductSku;
}
