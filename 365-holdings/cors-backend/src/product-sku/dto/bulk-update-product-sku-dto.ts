import {
  IsBoolean,
  IsOptional,
  IsObject,
  IsNumber,
  IsEnum,
  IsString,
} from 'class-validator';
import {
  CroppingMethod,
  FileUploadFormat,
  ImageInheritRule,
  ExceptionHandlingRule,
} from '../enums/product-sku.enums';
export class BulkUpdateProductSkuDto {
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsBoolean()
  @IsOptional()
  hasRush?: boolean;

  @IsNumber()
  @IsOptional()
  rushDays?: number;

  @IsNumber()
  @IsOptional()
  imageInheritancePriority?: number;

  @IsBoolean()
  @IsOptional()
  requireImageUpload?: boolean;

  @IsBoolean()
  @IsOptional()
  requireCropping?: boolean;

  @IsBoolean()
  @IsOptional()
  requireArtwork?: boolean;

  @IsBoolean()
  @IsOptional()
  requireCustomerArtworkApproval?: boolean;

  @IsBoolean()
  @IsOptional()
  requireTemplate?: boolean;

  @IsEnum(CroppingMethod)
  @IsOptional()
  croppingMethod?: CroppingMethod;

  @IsBoolean()
  @IsOptional()
  croppingReviewRequired?: boolean;

  @IsString()
  @IsOptional()
  imageNamingConvention?: string;

  @IsEnum(FileUploadFormat)
  @IsOptional()
  fileUploadFormat?: FileUploadFormat;

  @IsBoolean()
  @IsOptional()
  canInheritImage?: boolean;

  @IsEnum(ImageInheritRule)
  @IsOptional()
  imageInheritRule?: ImageInheritRule;

  @IsBoolean()
  @IsOptional()
  canManualOverride?: boolean;

  @IsEnum(ExceptionHandlingRule)
  @IsOptional()
  exceptionHandlingRule?: ExceptionHandlingRule;

  @IsBoolean()
  @IsOptional()
  customerFollowupEnabled?: boolean;

  @IsString()
  @IsOptional()
  shipStationStore?: string;
}
