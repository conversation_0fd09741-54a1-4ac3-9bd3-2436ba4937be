import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, ILike, DataSource } from 'typeorm';
import { ProductSku } from './entities/product-sku.entity';
import { BaseService } from '../common/base.service';
import { Product } from './entities/product.entity';
import { ArtworkType } from '../artwork-types/entities/artwork-type.entity';
import { Vendor } from 'src/vendors/entities/vendor.entity';
import { SkuRelationship } from './entities/sku-relationship.entity';
import { VendorSupportedSku } from 'src/vendors/entities/vendor-supported-sku.entity';
import {
  shopifyTransformClient,
  TransformedProduct,
} from './shopify-etl/shopify.transform';
import { shopifyExtractClient } from './shopify-etl/shopify.extract';
import { ProductCategory } from './enums/product.enums';
// import { compareSync } from 'bcryptjs';
import { UpdateProductSkuDto } from './dto/update-product-sku.dto';
import { DBHelper } from 'src/helpers/db.helpers';
import { SkuAddon } from './entities/sku-addon.entity';
import { RelationType } from './enums/product-sku.enums';
import { FilterHelper } from '../helpers/filter.helper';
import { HistoryService } from 'src/common/history.service';
import { ClsService } from 'nestjs-cls';
import { WorkflowCategory } from './enums/product-sku.enums';

interface ProductData {
  shopifyProductId: string;
  name: string;
  description: string;
  category: ProductCategory;
  metadata: Record<string, any>;
}

interface ProductSkuData extends Partial<ProductSku> {
  sku: string;
  products: Product[];
}

@Injectable()
export class ProductSkuService extends BaseService<ProductSku> {
  constructor(
    @InjectRepository(ProductSku)
    private readonly productSkuRepository: Repository<ProductSku>,

    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,

    @InjectRepository(Vendor)
    private readonly vendorRepository: Repository<Vendor>,

    @InjectRepository(ArtworkType)
    private readonly artworkTypeRepository: Repository<ArtworkType>,

    @InjectRepository(SkuRelationship)
    private readonly skuRelationshipRepository: Repository<SkuRelationship>,

    @InjectRepository(VendorSupportedSku)
    private readonly vendorSupportedSkuRepository: Repository<VendorSupportedSku>,

    @InjectRepository(SkuAddon)
    private readonly skuAddonRepository: Repository<SkuAddon>,

    private readonly dataSource: DataSource,
    private readonly historyService: HistoryService,
    private readonly clsService: ClsService,
  ) {
    super(productSkuRepository);
  }

  private async createOrUpdateProduct(productData: ProductData) {
    const existingProduct = await this.productRepository.findOne({
      where: { shopifyProductId: productData.shopifyProductId },
    });

    if (existingProduct) {
      await this.productRepository.update(existingProduct.id, productData);
      return existingProduct;
    } else {
      return await this.productRepository.save(productData);
    }
  }

  private async createOrUpdateSku(
    skuData: ProductSkuData,
    existingProduct: Product,
  ) {
    const { products, ...updateData } = skuData;
    const existingSku = await this.productSkuRepository.findOne({
      where: { sku: skuData.sku },
    });

    if (existingSku) {
      await this.productSkuRepository.update(existingSku.id, updateData);
      const existingSkuWithProducts = await this.productSkuRepository.findOne({
        where: { id: existingSku.id },
        relations: ['products'],
      });

      if (existingSkuWithProducts) {
        if (
          !existingSkuWithProducts.products.some(
            p => p.id === existingProduct.id,
          )
        ) {
          existingSkuWithProducts.products.push(existingProduct);
          return await this.productSkuRepository.save(existingSkuWithProducts);
        }
        return existingSkuWithProducts;
      }
    }

    const newSku = this.productSkuRepository.create({
      ...updateData,
      products: [existingProduct],
    });
    return await this.productSkuRepository.save(newSku);
  }

  private async handleAddonRelationships(sku: ProductSku, addon: ProductSku) {
    const product = sku.products[0];
    const extraMetafield = product.metadata?.extras
      ? JSON.parse(product.metadata.extras)
      : null;
    const pdpCustomizerMetafield = product.metadata?.pdp_customizer
      ? JSON.parse(product.metadata.pdp_customizer)
      : null;
    const productCustomizerMetafield = product.metadata?.product_customizer
      ? JSON.parse(product.metadata.product_customizer)
      : null;

    const createAddonRelationship = async () => {
      try {
        const skuAddonEntities = this.skuAddonRepository.create({
          productSkuId: sku.id,
          relationSkuId: addon.id,
          relationType: RelationType.ADDON,
        });
        return await this.skuAddonRepository.save(skuAddonEntities);
      } catch (error) {
        console.error('Error saving SKU addon:', error);
        throw error;
      }
    };

    if (
      extraMetafield?.extras_products?.some(
        extra =>
          extra.id === addon.variantId || extra.id === Number(addon.variantId),
      )
    ) {
      return await createAddonRelationship();
    }

    if (pdpCustomizerMetafield) {
      const extrasStep = pdpCustomizerMetafield.steps?.find(
        step => step.step_name === 'EXTRAS',
      );
      const backEngraving = pdpCustomizerMetafield.steps?.find(
        step =>
          step.step_name === 'UPLOAD' &&
          (step.back_engraving?.id === addon.variantId ||
            step.back_engraving?.id === Number(addon.variantId)),
      );
      let relationships: SkuAddon[] = [];
      if (
        extrasStep?.extra_options?.some(
          extra =>
            extra.id === addon.variantId ||
            extra.id === Number(addon.variantId),
        )
      ) {
        relationships.push(await createAddonRelationship());
      }
      if (backEngraving) {
        relationships.push(await createAddonRelationship());
      }
      if (relationships.length > 0) {
        return relationships;
      }
    }

    if (productCustomizerMetafield) {
      const finalOptionSection = productCustomizerMetafield?.find(
        section => section.final_options,
      );
      const finalOptions = finalOptionSection?.final_options;
      const rushOptions = productCustomizerMetafield?.find(
        section => section.label_for === 'Rush Creation',
      );

      const hasMatchingFinalOption = finalOptions?.products?.some(pro => {
        const defaultIdMatch =
          pro.detail.default_variant_id === Number(addon.variantId);
        const variantMatch = pro.detail.variant?.some(
          v => v.value === Number(addon.variantId),
        );
        return defaultIdMatch || variantMatch;
      });

      const hasMatchingRushOption = rushOptions?.options?.some(
        option =>
          option.value === addon.variantId ||
          option.value === Number(addon.variantId),
      );

      if (hasMatchingFinalOption || hasMatchingRushOption) {
        return await createAddonRelationship();
      }
    }
  }

  async syncShopifyProductInformation() {
    const transformedProductSkuArray: TransformedProduct[] = [];
    const products = await shopifyExtractClient.fetchShopifyProducts(50, -1);

    for (const product of products) {
      try {
        transformedProductSkuArray.push(
          ...shopifyTransformClient.transform(product),
        );
      } catch (error) {
        console.error('Error while transforming product:', error);
      }
    }

    const productsByProductId = transformedProductSkuArray.reduce(
      (acc, product) => {
        if (!acc[product.id]) acc[product.id] = [];
        acc[product.id].push(product);
        return acc;
      },
      {} as Record<string, TransformedProduct[]>,
    );

    for (const [productId, products] of Object.entries(productsByProductId)) {
      const firstProduct = products[0];
      const metafields = firstProduct.metafields.reduce((acc, field) => {
        acc[field.node.key] = field.node.value;
        return acc;
      }, {});

      const insertedProduct = await this.createOrUpdateProduct({
        name: firstProduct.title,
        description: firstProduct.description,
        shopifyProductId: firstProduct.id,
        category: ProductCategory.UNCATEGORIZED,
        metadata: { ...metafields },
      });

      const isAddon = JSON.parse(metafields?.['is_addon'] || 'false');
      const addonLevel = metafields?.['addon_level'];

      const uniqueSkuList = products
        .map(item => ({
          sku: item.sku,
          isActive: true,
          hasRush: false,
          canUpSold: false,
          canCrossSold: false,
          requireCropping: false,
          croppingReviewRequired: false,
          artworkRequired: false,
          requireCustomerArtworkApproval: false,
          requireTemplate: false,
          canInheritImage: false,
          canManualOverride: false,
          customerFollowupEnabled: false,
          requirePreprocessing: false,
          hasWorkPaper: false,
          shopifyNativeVariant: item.variantNativeOptions || {},
          shopifyCustomVariant: item.variantCustomOptions || {},
          products: [insertedProduct],
          isAddon,
          addonLevel,
          variantId: isAddon ? item.variantId?.split('/').pop() : undefined,
        }))
        .filter(
          (sku, index, self) =>
            index === self.findIndex(s => s.sku === sku.sku),
        );

      await Promise.allSettled(
        uniqueSkuList.map(sku => this.createOrUpdateSku(sku, insertedProduct)),
      );
    }

    await this.syncAddonRelationships();
  }

  private async syncAddonRelationships() {
    const addonSku = await this.productSkuRepository.find({
      where: { isAddon: true },
    });
    const nonAddonSku = await this.productSkuRepository.find({
      where: { isAddon: false },
      relations: ['products'],
    });

    await this.skuAddonRepository.deleteAll();

    for (const sku of nonAddonSku) {
      await Promise.allSettled(
        addonSku.map(addon => this.handleAddonRelationships(sku, addon)),
      );
    }
  }

  async syncExistingProduct(products: TransformedProduct[]) {
    for (const product of products) {
      const metafields = product.metafields.reduce((acc, field) => {
        acc[field.key] = field.value;
        return acc;
      }, {});

      const existingProduct = await this.createOrUpdateProduct({
        name: product.title,
        description: product.description,
        shopifyProductId: product.id,
        category: ProductCategory.UNCATEGORIZED,
        metadata: { ...metafields },
      });

      const isAddon = JSON.parse(metafields?.['is_addon'] || 'false');
      const addonLevel = metafields?.['addon_level'];

      const existingSku = await this.createOrUpdateSku(
        {
          sku: product.sku,
          isActive: product.status === 'ACTIVE',
          hasRush: false,
          canUpSold: false,
          canCrossSold: false,
          requireCropping: false,
          croppingReviewRequired: false,
          artworkRequired: false,
          requireCustomerArtworkApproval: false,
          requireTemplate: false,
          canInheritImage: false,
          canManualOverride: false,
          customerFollowupEnabled: false,
          requirePreprocessing: false,
          hasWorkPaper: false,
          shopifyNativeVariant: product.variantNativeOptions || [],
          shopifyCustomVariant: product.variantCustomOptions || [],
          products: [existingProduct],
          isAddon,
          addonLevel,
          variantId:
            isAddon && product.variantId
              ? product.variantId.includes('/')
                ? product.variantId.split('/').pop()
                : product.variantId
              : undefined,
        },
        existingProduct,
      );

      if (existingSku.isAddon) {
        const nonAddonSku = await this.productSkuRepository.find({
          where: { isAddon: false },
          relations: ['products'],
        });

        for (const sku of nonAddonSku) {
          await this.handleAddonRelationships(sku, existingSku);
        }
      }
    }
  }

  async bulkUpdate(updateDto: any) {
    try {
      const { ids, attributes } = updateDto;
      const {
        primaryVendorId,
        artworkTypeId,
        productIds,
        childSkuIds,
        parentSkuIds,
        eligibleVendors,
        ...attributesToUpdate
      } = attributes;

      const notFoundSku: string[] = [];
      const skusWithProductUpdates: {
        sku: ProductSku;
        productChanges: { field: string; oldValue: any; newValue: any }[];
      }[] = [];

      for (const id of ids) {
        const sku = await this.productSkuRepository.findOne({
          where: { id },
          relations: ['products'],
        });

        if (sku) {
          let hasProductUpdates = false;
          const productChanges: {
            field: string;
            oldValue: any;
            newValue: any;
          }[] = [];

          for (const [key, value] of Object.entries(attributesToUpdate)) {
            if (key.includes('.') || key.includes('_')) {
              const [relation, field] = key.includes('.')
                ? key.split('.')
                : key.split('_');
              if (sku[relation] && Array.isArray(sku[relation])) {
                sku[relation].forEach(item => {
                  const oldValue = item[field];
                  item[field] = value;
                  if (oldValue !== value) {
                    productChanges.push({
                      field: `${relation}.${field}`,
                      oldValue,
                      newValue: value,
                    });
                  }
                });
                hasProductUpdates = true;
              } else if (sku[relation]) {
                const oldValue = sku[relation][field];
                sku[relation][field] = value;
                if (oldValue !== value) {
                  productChanges.push({
                    field: `${relation}.${field}`,
                    oldValue,
                    newValue: value,
                  });
                }
                hasProductUpdates = true;
              }
            } else {
              sku[key] = value;
            }
          }

          await this.productSkuRepository.save(sku);

          if (sku.products) {
            await this.productRepository.save(sku.products);
          }

          // Track SKUs that had product updates for history creation
          if (hasProductUpdates && productChanges.length > 0) {
            skusWithProductUpdates.push({ sku, productChanges });
          }
        } else {
          notFoundSku.push(id);
        }
      }

      // Create history records for ProductSku entities when their products were updated
      if (skusWithProductUpdates.length > 0) {
        for (const { sku, productChanges } of skusWithProductUpdates) {
          // Get user context for history creation
          let userId: string | null = null;
          try {
            userId = this.clsService.get('userId');
          } catch (error) {
            console.warn(
              'Could not get user context for history creation:',
              error,
            );
          }

          // Create history record for the ProductSku with the product changes
          await this.historyService.recordHistory(
            'ProductSku',
            sku.id,
            'UPDATE',
            null,
            null,
            productChanges,
            { userId },
          );
        }
      }

      if (notFoundSku.length > 0) {
        return {
          status: 'error',
          message: `Product SKUs not found with IDs: ${notFoundSku.join(
            ', ',
          )} and remaining sku are updated`,
        };
      }

      const updatedSkus = await this.productSkuRepository.findBy({
        id: In(ids),
      });

      if (primaryVendorId) {
        const vendor = await DBHelper.findByIdOrThrow(
          this.vendorRepository,
          primaryVendorId,
        );
        updatedSkus.forEach(sku => (sku.primaryVendor = vendor));
      }

      if (eligibleVendors) {
        const vendors = await DBHelper.findMany(this.vendorRepository, {
          where: { id: In(eligibleVendors) },
        });
        DBHelper.validateEntityIds(
          vendors,
          eligibleVendors,
          v => v.id,
          'Vendor',
        );

        await this.vendorSupportedSkuRepository.delete({
          productSku: { id: updatedSkus[0].id },
        });

        const supportedSkus = vendors.map(vendor =>
          this.vendorSupportedSkuRepository.create({
            vendor,
            productSku: updatedSkus[0],
          }),
        );

        await this.vendorSupportedSkuRepository.save(supportedSkus);
      }

      if (artworkTypeId) {
        const artworkType = await DBHelper.findByIdOrThrow(
          this.artworkTypeRepository,
          artworkTypeId,
        );
        updatedSkus.forEach(sku => (sku.artworkType = artworkType));
      }

      if (productIds) {
        const products = await DBHelper.findMany(this.productRepository, {
          where: { id: In(productIds) },
        });
        updatedSkus.forEach(sku => (sku.products = products));
      }

      // Track SKUs with relationship changes for history recording
      const skusWithRelationshipChanges: {
        sku: ProductSku;
        changes: { field: string; oldValue: any; newValue: any }[];
      }[] = [];

      if (childSkuIds) {
        const childSkus = await DBHelper.findMany(this.productSkuRepository, {
          where: { id: In(childSkuIds) },
        });

        for (const sku of updatedSkus) {
          // Skip if this SKU is in the childSkuIds (to avoid self-referencing)
          if (childSkuIds.includes(sku.id)) {
            continue;
          }
          // Get current child relationships before changes
          const currentChildRelationships =
            await this.skuRelationshipRepository.find({
              where: { parentSku: { id: sku.id } },
              relations: ['childSku'],
            });

          const oldChildSkus = currentChildRelationships
            .map(rel => rel.childSku?.sku || rel.childSku?.id)
            .filter(Boolean);

          const newChildSkus = childSkus.map(child => child.sku || child.id);

          // Check if there are actual changes
          if (
            JSON.stringify(oldChildSkus.sort()) !==
            JSON.stringify(newChildSkus.sort())
          ) {
            skusWithRelationshipChanges.push({
              sku,
              changes: [
                {
                  field: 'childSkuIds',
                  oldValue: oldChildSkus.join(', ') || 'None',
                  newValue: newChildSkus.join(', ') || 'None',
                },
              ],
            });
          }

          const relationships = childSkus.map(child => {
            const rel = this.skuRelationshipRepository.create();
            rel.parentSku = sku;
            rel.childSku = child;
            return rel;
          });
          await this.skuRelationshipRepository.save(relationships);
        }
      }

      if (parentSkuIds) {
        const parentSkus = await DBHelper.findMany(this.productSkuRepository, {
          where: { id: In(parentSkuIds) },
        });

        for (const sku of updatedSkus) {
          // Skip if this SKU is in the parentSkuIds (to avoid self-referencing)
          if (parentSkuIds.includes(sku.id)) {
            continue;
          }
          // Get current parent relationships before changes
          const currentParentRelationships =
            await this.skuRelationshipRepository.find({
              where: { childSku: { id: sku.id } },
              relations: ['parentSku'],
            });

          const oldParentSkus = currentParentRelationships
            .map(rel => rel.parentSku?.sku || rel.parentSku?.id)
            .filter(Boolean);

          const newParentSkus = parentSkus.map(
            parent => parent.sku || parent.id,
          );

          // Check if there are actual changes
          if (
            JSON.stringify(oldParentSkus.sort()) !==
            JSON.stringify(newParentSkus.sort())
          ) {
            const existingEntry = skusWithRelationshipChanges.find(
              entry => entry.sku.id === sku.id,
            );
            // Use a similar approach as in the update method to record all parentSkuIds changes in a single array for history
            if (existingEntry) {
              existingEntry.changes.push({
                field: 'parentSkuIds',
                oldValue: oldParentSkus.join(', ') || 'None',
                newValue: newParentSkus.join(', ') || 'None',
              });
            } else {
              skusWithRelationshipChanges.push({
                sku,
                changes: [
                  {
                    field: 'parentSkuIds',
                    oldValue: oldParentSkus.join(', ') || 'None',
                    newValue: newParentSkus.join(', ') || 'None',
                  },
                ],
              });
            }
          }

          const relationships = parentSkus.map(parent => {
            const rel = this.skuRelationshipRepository.create();
            rel.childSku = sku;
            rel.parentSku = parent;
            return rel;
          });
          await this.skuRelationshipRepository.save(relationships);
        }
      }

      // Record history for relationship changes
      if (skusWithRelationshipChanges.length > 0) {
        let userId: string | null = null;
        try {
          userId = this.clsService.get('userId');
        } catch (error) {
          console.warn(
            'Could not get user context for history creation:',
            error,
          );
        }

        for (const { sku, changes } of skusWithRelationshipChanges) {
          console.log(
            'Recording bulk relationship changes for SKU:',
            sku.id,
            'Changes:',
            changes,
          );
          await this.historyService.recordHistory(
            'ProductSku',
            sku.id,
            'UPDATE',
            null,
            null,
            changes,
            { userId },
          );
        }
      }

      const data = await this.productSkuRepository.save(updatedSkus);

      return {
        status: `Bulk update successful: ${data.length} SKUs updated.`,
        data,
      };
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }

  async update(id: string, dto: UpdateProductSkuDto): Promise<ProductSku> {
    if (
      dto.workflowCategory &&
      typeof dto.workflowCategory === 'string' &&
      (dto.workflowCategory === WorkflowCategory.ART_ONLY ||
        dto.workflowCategory === WorkflowCategory.ART_AND_TEMPLATE ||
        dto.workflowCategory === WorkflowCategory.ART_TEMPLATE_AND_APPROVAL)
    ) {
      const sku = await this.productSkuRepository.findOne({
        where: { id },
        relations: ['artworkType'],
      });

      if (!sku?.artworkType && !dto.artworkType) {
        throw new BadRequestException(
          `Please select an artwork type for this ${dto.workflowCategory}.`,
        );
      }
    }

    const sku = await this.findByIdOrThrow(id, ['primaryVendor']);
    let primaryVendorUpdated = false;

    if (dto.primaryVendorId) {
      const vendor = await DBHelper.findByIdOrThrow(
        this.vendorRepository,
        dto.primaryVendorId,
      );
      sku.primaryVendor = vendor;
      primaryVendorUpdated = true;
    }

    if (dto.eligibleVendors) {
      const vendors = await DBHelper.findMany(this.vendorRepository, {
        where: { id: In(dto.eligibleVendors) },
      });
      DBHelper.validateEntityIds(
        vendors,
        dto.eligibleVendors,
        v => v.id,
        'Vendor',
      );

      // First, remove all existing vendor relationships for this SKU
      await this.vendorSupportedSkuRepository.delete({
        productSku: { id: sku.id },
      });

      // Then create new relationships for the vendors in the array
      const supportedSkus = vendors.map(vendor =>
        this.vendorSupportedSkuRepository.create({
          vendor,
          productSku: sku,
        }),
      );

      await this.vendorSupportedSkuRepository.save(supportedSkus);
    }

    if (dto.removeEligibleVendors) {
      await this.vendorSupportedSkuRepository.delete({
        productSku: { id: sku.id },
        vendor: { id: In(dto.removeEligibleVendors) },
      });
    }

    if (dto.primaryVendorToUpdate && sku.primaryVendor) {
      const { vendorSku, supportedSku } = dto.primaryVendorToUpdate;

      if (vendorSku) {
        sku.primaryVendor.sku = vendorSku;
        primaryVendorUpdated = true;
      }
      await this.vendorRepository.save(sku.primaryVendor);

      if (supportedSku) {
        const supportedSkuToUpdate = await DBHelper.findOne(
          this.vendorSupportedSkuRepository,
          {
            where: {
              vendor: { id: sku.primaryVendor.id },
              productSku: { id: sku.id },
            },
          },
        );

        if (supportedSkuToUpdate) {
          if (supportedSku.productionTimeDays !== undefined) {
            supportedSkuToUpdate.productionTimeDays =
              supportedSku.productionTimeDays;
          }
          if (supportedSku.maxCapacityPerDay !== undefined) {
            supportedSkuToUpdate.maxCapacityPerDay =
              supportedSku.maxCapacityPerDay;
          }
          await this.vendorSupportedSkuRepository.save(supportedSkuToUpdate);
        }
      }
    }

    if (dto.artworkTypeId) {
      const artworkType = await DBHelper.findByIdOrThrow(
        this.artworkTypeRepository,
        dto.artworkTypeId,
      );
      sku.artworkType = artworkType;
    }

    if (dto.productIds) {
      const products = await DBHelper.findMany(this.productRepository, {
        where: { id: In(dto.productIds) },
      });
      DBHelper.validateEntityIds(
        products,
        dto.productIds,
        p => p.id,
        'Product',
      );
      sku.products = products;
    }

    if (dto.productsToUpdate?.length) {
      const updatedProducts: {
        productId: string;
        changes: { field: string; oldValue: any; newValue: any }[];
      }[] = [];

      for (const productUpdate of dto.productsToUpdate) {
        const product = await this.productRepository.findOne({
          where: { id: productUpdate.id },
        });
        if (product) {
          const updates: Partial<Product> = {};
          const changes: { field: string; oldValue: any; newValue: any }[] = [];

          if (
            productUpdate.name !== undefined &&
            productUpdate.name !== product.name
          ) {
            updates.name = productUpdate.name;
            changes.push({
              field: 'name',
              oldValue: product.name,
              newValue: productUpdate.name,
            });
          }

          if (
            productUpdate.category !== undefined &&
            productUpdate.category !== product.category
          ) {
            updates.category = productUpdate.category;
            changes.push({
              field: 'category',
              oldValue: product.category,
              newValue: productUpdate.category,
            });
          }

          if (
            productUpdate.description !== undefined &&
            productUpdate.description !== product.description
          ) {
            updates.description = productUpdate.description;
            changes.push({
              field: 'description',
              oldValue: product.description,
              newValue: productUpdate.description,
            });
          }

          if (Object.keys(updates).length > 0) {
            Object.assign(product, updates);
            await this.productRepository.save(product);

            updatedProducts.push({
              productId: product.id,
              changes: changes,
            });
          }
        }
      }

      if (updatedProducts.length > 0) {
        let userId: string | null = null;
        try {
          userId = this.clsService.get('userId');
        } catch (error) {
          console.warn(
            'Could not get user context for history creation:',
            error,
          );
        }

        const allChanges: { field: string; oldValue: any; newValue: any }[] =
          [];

        updatedProducts.forEach((product, index) => {
          const productField = `product ${index + 1}`;
          product.changes.forEach(change => {
            allChanges.push({
              field: productField,
              oldValue: `${change.oldValue} (${change.field})`,
              newValue: `${change.newValue} (${change.field})`,
            });
          });
        });

        await this.historyService.recordHistory(
          'ProductSku',
          sku.id,
          'UPDATE',
          null,
          null,
          allChanges,
          { userId },
        );
      }
    }

    const relationshipFields = [
      { ids: dto.childSkuIds, relation: 'parentSku', column: 'childSku' },
      { ids: dto.parentSkuIds, relation: 'childSku', column: 'parentSku' },
    ];

    // Check if there are any relationship changes
    const hasRelationshipChanges = relationshipFields.some(field => field.ids);

    if (hasRelationshipChanges) {
      let userId: string | null = null;
      try {
        userId = this.clsService.get('userId');
      } catch (error) {
        console.warn('Could not get user context for history creation:', error);
      }

      // Get current relationships before changes
      const currentRelationships = await this.skuRelationshipRepository.find({
        where: [{ parentSku: { id: sku.id } }, { childSku: { id: sku.id } }],
        relations: ['parentSku', 'childSku'],
      });

      // Get new related SKUs for history comparison
      const newChildSkus = dto.childSkuIds
        ? await DBHelper.findMany(this.productSkuRepository, {
            where: { id: In(dto.childSkuIds) },
          })
        : [];

      const newParentSkus = dto.parentSkuIds
        ? await DBHelper.findMany(this.productSkuRepository, {
            where: { id: In(dto.parentSkuIds) },
          })
        : [];

      await this.dataSource.transaction(async transactionalEntityManager => {
        for (const { ids, relation, column } of relationshipFields) {
          if (ids) {
            const relatedSkus = await DBHelper.findMany(
              this.productSkuRepository,
              { where: { id: In(ids) } },
            );
            DBHelper.validateEntityIds(
              relatedSkus,
              ids,
              s => s.id,
              'ProductSku',
            );

            await transactionalEntityManager.delete(SkuRelationship, {
              [relation]: id,
            });

            const newRelations = relatedSkus.map(related =>
              transactionalEntityManager.create(SkuRelationship, {
                [relation]: sku,
                [column]: related,
              }),
            );
            await transactionalEntityManager.save(
              SkuRelationship,
              newRelations,
            );
          }
        }
      });

      // Record history for relationship changes
      const relationshipChanges: {
        field: string;
        oldValue: any;
        newValue: any;
      }[] = [];

      // Handle child SKU changes
      if (dto.childSkuIds) {
        const oldChildSkus = currentRelationships
          .filter(rel => rel.parentSku?.id === sku.id)
          .map(rel => rel.childSku?.sku || rel.childSku?.id)
          .filter(Boolean);

        const newChildSkuValues = newChildSkus.map(
          related => related.sku || related.id,
        );

        if (
          JSON.stringify(oldChildSkus.sort()) !==
          JSON.stringify(newChildSkuValues.sort())
        ) {
          relationshipChanges.push({
            field: 'childSkuIds',
            oldValue: oldChildSkus.join(', ') || 'None',
            newValue: newChildSkuValues.join(', ') || 'None',
          });
        }
      }

      // Handle parent SKU changes
      if (dto.parentSkuIds) {
        const oldParentSkus = currentRelationships
          .filter(rel => rel.childSku?.id === sku.id)
          .map(rel => rel.parentSku?.sku || rel.parentSku?.id)
          .filter(Boolean);

        const newParentSkuValues = newParentSkus.map(
          related => related.sku || related.id,
        );

        if (
          JSON.stringify(oldParentSkus.sort()) !==
          JSON.stringify(newParentSkuValues.sort())
        ) {
          relationshipChanges.push({
            field: 'parentSkuIds',
            oldValue: oldParentSkus.join(', ') || 'None',
            newValue: newParentSkuValues.join(', ') || 'None',
          });
        }
      }

      // Record history if there are changes
      if (relationshipChanges.length > 0) {
        await this.historyService.recordHistory(
          'ProductSku',
          sku.id,
          'UPDATE',
          null,
          null,
          relationshipChanges,
          { userId },
        );
      }
    }

    const {
      productIds,
      primaryVendorId,
      artworkTypeId,
      parentSkuIds,
      childSkuIds,
      productsToUpdate,
      eligibleVendors,
      primaryVendorToUpdate,
      removeEligibleVendors,
      ...rest
    } = dto;

    // Check if there are actual changes to the ProductSku entity
    const hasSkuChanges = Object.keys(rest).length > 0;

    if (hasSkuChanges) {
      Object.assign(sku, rest);
      await this.productSkuRepository.save(sku);
    }

    // Only include primaryVendor in relations if it was updated
    const relations = [
      'artworkType',
      'products',
      'parentSku.parentSku',
      'childSku.childSku',
      'supportedSkus',
    ];
    if (primaryVendorUpdated) {
      relations.push('primaryVendor');
    }

    const result = await this.productSkuRepository.findOne({
      where: { id: sku.id },
      relations,
    });

    if (!result) {
      throw new BadRequestException(`ProductSku with id ${sku.id} not found`);
    }

    return result;
  }

  async getProducts(q?: string) {
    const where = q ? { name: ILike(`%${q}%`) } : {};
    const products = await this.productRepository.find({
      where,
      select: {
        id: true,
        name: true,
        category: true,
      },
    });
    return { data: products };
  }

  async getShopifyNativeVariantKeys() {
    const queryBuilder =
      this.productSkuRepository.createQueryBuilder('productSku');
    queryBuilder.select('productSku.shopifyNativeVariant');
    queryBuilder.where('productSku.shopifyNativeVariant IS NOT NULL');

    const results = await queryBuilder.getMany();

    const valuesByKey = new Map<string, Set<string>>();

    results.forEach(result => {
      if (
        result.shopifyNativeVariant &&
        Array.isArray(result.shopifyNativeVariant)
      ) {
        result.shopifyNativeVariant.forEach(variant => {
          if (variant.name && variant.value) {
            if (!valuesByKey.has(variant.name)) {
              valuesByKey.set(variant.name, new Set());
            }
            valuesByKey.get(variant.name)?.add(variant.value);
          }
        });
      }
    });

    return {
      data: Object.fromEntries(
        Array.from(valuesByKey.entries()).map(([key, values]) => [
          key,
          Array.from(values),
        ]),
      ),
    };
  }

  private mapAttributeToRelation(attribute: string): string {
    const attributeMap = {
      parentSku: 'parentSku',
      childSku: 'childSku',
      products_category: 'products.category',
      products_description: 'products.description',
    };
    return attributeMap[attribute] || attribute;
  }

  async findAllWithAdvancedFilters(sOptions: {
    filters: any[][];
    page?: number;
    limit?: number;
    relations?: string[];
  }): Promise<{
    data: (ProductSku & { product_category: ProductCategory[] })[];
    count: number;
    page: number;
    limit: number;
  }> {
    const processedFilters = sOptions.filters.map(filterGroup =>
      filterGroup.map(filter => {
        const processedFilter = {
          ...filter,
          attribute: this.mapAttributeToRelation(filter.attribute),
        };

        if (filter.attribute === 'shopifyNativeVariant') {
          processedFilter.keyField = 'name';
        }

        return processedFilter;
      }),
    );

    const result = await super.findAllWithAdvancedFilters({
      ...sOptions,
      filters: processedFilters,
    });

    const transformedData = result.data.map(item => {
      const transformedItem = { ...item } as ProductSku & {
        product_category: ProductCategory[];
      };
      if (transformedItem.products) {
        const productCategories = transformedItem.products.map(
          product => product.category,
        );
        transformedItem.product_category = productCategories;
      }
      return transformedItem;
    });

    return {
      data: transformedData,
      count: result.count,
      page: result.page,
      limit: result.limit,
    };
  }
}
