import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

export function IsStringBooleanNumber(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isStringBooleanNumber',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          return (
            typeof value === 'string' ||
            typeof value === 'boolean' ||
            typeof value === 'number' ||
            (Array.isArray(value) && value.every(v => typeof v === 'string'))
          );
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a string, boolean, or number`;
        },
      },
    });
  };
}
