import { Injectable, Logger } from '@nestjs/common';
import { accessEnv } from '../env.validation';
import * as twilio from 'twilio';

@Injectable()
export class SmsUtil {
  private readonly logger = new Logger(SmsUtil.name);
  private readonly twilioClient: twilio.Twilio | null;

  constructor() {
    const accountSid = accessEnv('TWILIO_ACCOUNT_SID');
    const authToken = accessEnv('TWILIO_AUTH_TOKEN');

    if (!accountSid || !authToken) {
      this.logger.warn(
        'Twilio credentials not found. SMS will be logged only.',
      );
      this.twilioClient = null;
    } else {
      this.twilioClient = twilio(accountSid, authToken);
      this.logger.log('Twilio client initialized successfully');
    }
  }

  async sendSMS(to: string, message: string): Promise<void> {
    try {
      // Validate phone number format
      if (!this.isValidPhoneNumber(to)) {
        throw new Error(`Invalid phone number format: ${to}`);
      }

      if (!this.twilioClient) {
        this.logger.log(`[SMS LOG] To: ${to} | Message: ${message}`);
        return;
      }

      const fromNumber = accessEnv('TWILIO_PHONE_NUMBER');
      if (!fromNumber) {
        throw new Error('TWILIO_PHONE_NUMBER environment variable is not set');
      }

      this.logger.log(`Sending SMS to ${to} via Twilio`);

      const result = await this.twilioClient.messages.create({
        body: message,
        from: fromNumber,
        to: to,
      });

      this.logger.log(`SMS sent successfully. SID: ${result.sid}`);
    } catch (error) {
      this.logger.error(
        `Failed to send SMS to ${to}: ${error.message}`,
        error.stack,
      );

      // Log the SMS content for debugging even if Twilio fails
      this.logger.log(`[SMS FAILED] To: ${to} | Message: ${message}`);

      throw error;
    }
  }

  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic phone number validation - should start with + and contain only digits
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }
}
