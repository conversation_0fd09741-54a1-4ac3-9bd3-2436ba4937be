// validators/is-unique.decorator.ts
import { registerDecorator, ValidationOptions } from 'class-validator';
import { IsUniqueConstraint } from './unique-validator';

export function IsUnique(
  entity: Function,
  column: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'IsUnique',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [entity, column],
      validator: IsUniqueConstraint,
    });
  };
}
