export const countryCodeToTimezone: Record<string, string> = {
  AF: 'Asia/Kabul',
  AL: 'Europe/Tirane',
  DZ: 'Africa/Algiers',
  AS: 'Pacific/Pago_Pago',
  AD: 'Europe/Andorra',
  AO: 'Africa/Luanda',
  AR: 'America/Argentina/Buenos_Aires',
  AM: 'Asia/Yerevan',
  AU: 'Australia/Sydney',
  AT: 'Europe/Vienna',
  AZ: 'Asia/Baku',
  BH: 'Asia/Bahrain',
  BD: 'Asia/Dhaka',
  BY: 'Europe/Minsk',
  BE: 'Europe/Brussels',
  BZ: 'America/Belize',
  BJ: 'Africa/Porto-Novo',
  BO: 'America/La_Paz',
  BA: 'Europe/Sarajevo',
  BW: 'Africa/Gaborone',
  BR: 'America/Sao_Paulo',
  BN: 'Asia/Brunei',
  BG: 'Europe/Sofia',
  BF: 'Africa/Ouagadougou',
  BI: 'Africa/Bujumbura',
  KH: 'Asia/Phnom_Penh',
  CM: 'Africa/Douala',
  CA: 'America/Toronto',
  CV: 'Atlantic/Cape_Verde',
  CF: 'Africa/Bangui',
  TD: 'Africa/Ndjamena',
  CL: 'America/Santiago',
  CN: 'Asia/Shanghai',
  CO: 'America/Bogota',
  KM: 'Indian/Comoro',
  CG: 'Africa/Brazzaville',
  CR: 'America/Costa_Rica',
  HR: 'Europe/Zagreb',
  CY: 'Asia/Nicosia',
  CZ: 'Europe/Prague',
  DK: 'Europe/Copenhagen',
  DJ: 'Africa/Djibouti',
  DO: 'America/Santo_Domingo',
  EC: 'America/Guayaquil',
  EG: 'Africa/Cairo',
  SV: 'America/El_Salvador',
  GQ: 'Africa/Malabo',
  EE: 'Europe/Tallinn',
  ET: 'Africa/Addis_Ababa',
  FJ: 'Pacific/Fiji',
  FI: 'Europe/Helsinki',
  FR: 'Europe/Paris',
  GA: 'Africa/Libreville',
  GM: 'Africa/Banjul',
  GE: 'Asia/Tbilisi',
  DE: 'Europe/Berlin',
  GH: 'Africa/Accra',
  GR: 'Europe/Athens',
  GT: 'America/Guatemala',
  GN: 'Africa/Conakry',
  GY: 'America/Guyana',
  HT: 'America/Port-au-Prince',
  HN: 'America/Tegucigalpa',
  HK: 'Asia/Hong_Kong',
  HU: 'Europe/Budapest',
  IS: 'Atlantic/Reykjavik',
  IN: 'Asia/Kolkata',
  ID: 'Asia/Jakarta',
  IR: 'Asia/Tehran',
  IQ: 'Asia/Baghdad',
  IE: 'Europe/Dublin',
  IL: 'Asia/Jerusalem',
  IT: 'Europe/Rome',
  CI: 'Africa/Abidjan',
  JM: 'America/Jamaica',
  JP: 'Asia/Tokyo',
  JO: 'Asia/Amman',
  KZ: 'Asia/Almaty',
  KE: 'Africa/Nairobi',
  KR: 'Asia/Seoul',
  KW: 'Asia/Kuwait',
  KG: 'Asia/Bishkek',
  LA: 'Asia/Vientiane',
  LV: 'Europe/Riga',
  LB: 'Asia/Beirut',
  LS: 'Africa/Maseru',
  LR: 'Africa/Monrovia',
  LY: 'Africa/Tripoli',
  LT: 'Europe/Vilnius',
  LU: 'Europe/Luxembourg',
  MO: 'Asia/Macau',
  MK: 'Europe/Skopje',
  MG: 'Indian/Antananarivo',
  MW: 'Africa/Blantyre',
  MY: 'Asia/Kuala_Lumpur',
  MV: 'Indian/Maldives',
  ML: 'Africa/Bamako',
  MT: 'Europe/Malta',
  MR: 'Africa/Nouakchott',
  MU: 'Indian/Mauritius',
  MX: 'America/Mexico_City',
  MD: 'Europe/Chisinau',
  MN: 'Asia/Ulaanbaatar',
  ME: 'Europe/Podgorica',
  MA: 'Africa/Casablanca',
  MZ: 'Africa/Maputo',
  MM: 'Asia/Yangon',
  NA: 'Africa/Windhoek',
  NP: 'Asia/Kathmandu',
  NL: 'Europe/Amsterdam',
  NZ: 'Pacific/Auckland',
  NI: 'America/Managua',
  NE: 'Africa/Niamey',
  NG: 'Africa/Lagos',
  NO: 'Europe/Oslo',
  OM: 'Asia/Muscat',
  PK: 'Asia/Karachi',
  PA: 'America/Panama',
  PG: 'Pacific/Port_Moresby',
  PY: 'America/Asuncion',
  PE: 'America/Lima',
  PH: 'Asia/Manila',
  PL: 'Europe/Warsaw',
  PT: 'Europe/Lisbon',
  QA: 'Asia/Qatar',
  RO: 'Europe/Bucharest',
  RU: 'Europe/Moscow',
  RW: 'Africa/Kigali',
  SA: 'Asia/Riyadh',
  SN: 'Africa/Dakar',
  RS: 'Europe/Belgrade',
  SG: 'Asia/Singapore',
  SK: 'Europe/Bratislava',
  SI: 'Europe/Ljubljana',
  SO: 'Africa/Mogadishu',
  ZA: 'Africa/Johannesburg',
  ES: 'Europe/Madrid',
  LK: 'Asia/Colombo',
  SD: 'Africa/Khartoum',
  SE: 'Europe/Stockholm',
  CH: 'Europe/Zurich',
  SY: 'Asia/Damascus',
  TW: 'Asia/Taipei',
  TJ: 'Asia/Dushanbe',
  TZ: 'Africa/Dar_es_Salaam',
  TH: 'Asia/Bangkok',
  TL: 'Asia/Dili',
  TG: 'Africa/Lome',
  TN: 'Africa/Tunis',
  TR: 'Europe/Istanbul',
  TM: 'Asia/Ashgabat',
  UG: 'Africa/Kampala',
  UA: 'Europe/Kyiv',
  AE: 'Asia/Dubai',
  GB: 'Europe/London',
  US: 'America/New_York',
  UY: 'America/Montevideo',
  UZ: 'Asia/Tashkent',
  VE: 'America/Caracas',
  VN: 'Asia/Ho_Chi_Minh',
  YE: 'Asia/Aden',
  ZM: 'Africa/Lusaka',
  ZW: 'Africa/Harare',
};
