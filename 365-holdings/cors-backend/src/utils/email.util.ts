import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { RECIPIENT_EMAILS } from 'src/constants/emails.constant';

@Injectable()
export class EmailUtil {
  private transporter: nodemailer.Transporter;
  private readonly logger = new Logger(EmailUtil.name);

  constructor(private configService: ConfigService) {
    this.transporter = nodemailer.createTransport({
      host: this.configService.get('SMTP_HOST'),
      port: this.configService.get('SMTP_PORT'),
      auth: {
        user: this.configService.get('SMTP_USERNAME'),
        pass: this.configService.get('SMTP_PASSWORD'),
      },
    });
  }

  async sendPasswordResetEmail(
    email: string,
    resetToken: string,
  ): Promise<void> {
    const resetUrl = `${this.configService.get('FRONTEND_URL')}/reset-password?token=${resetToken}`;

    const mailOptions = {
      from: `"Cuddle Clones - CORS" <${this.configService.get('SMTP_FROM_EMAIL')}>`,
      to: email,
      subject: 'Password Reset Request',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Password Reset Request</h2>
          <p>You have requested to reset your password. Click the button below to reset your password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p>If you did not request this password reset, please ignore this email.</p>
          <p>This link will expire in 1 hour.</p>
          <hr style="border: 1px solid #eee; margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">
            If the button above doesn't work, copy and paste this link into your browser:<br>
            ${resetUrl}
          </p>
        </div>
      `,
    };

    try {
      await this.transporter.sendMail(mailOptions);
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw new Error('Failed to send password reset email');
    }
  }

  async sendQueueCreationEmail(artworkQueueName: string): Promise<void> {
    this.logger.log(
      `Attempting to send email for artwork queue: ${artworkQueueName}`,
    );

    const fromEmail = this.configService.get('SMTP_FROM_EMAIL');
    if (!fromEmail) {
      throw new Error('SMTP_FROM_EMAIL not configured');
    }

    const mailOptions = {
      from: `"Cuddle Clones - CORS" <${fromEmail}>`,
      to: RECIPIENT_EMAILS.ARTWORK_QUEUE,
      subject: 'Artwork Queue Created',
      html: `
        <p>
          Hi Team,
        </p>
        <p>
          The following artwork queue has been created. Kindly add the relevant permissions and ensure they are assigned to the appropriate users.
        </p>
        <p>
          <strong>${artworkQueueName}</strong>
        </p>
        <p>
          Thank You,<br>
          The Cuddle Clones Team
        </p>
      `,
    };

    try {
      this.logger.log('Sending email...');
      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully: ${JSON.stringify(info)}`);
    } catch (error) {
      this.logger.error('Error sending email:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async sendOrderResyncEmail(
    syncedOrders: number[],
    notSyncedOrders: number[],
    shopName: string,
  ): Promise<void> {
    this.logger.log(`Attempting to send email for shop: ${shopName}`);
    this.logger.log(
      `Synced orders: ${syncedOrders.length}, Not synced orders: ${notSyncedOrders.length}`,
    );

    const fromEmail = this.configService.get('SMTP_FROM_EMAIL');
    if (!fromEmail) {
      throw new Error('SMTP_FROM_EMAIL not configured');
    }

    const mailOptions = {
      from: `"Cuddle Clones - CORS" <${fromEmail}>`,
      to: RECIPIENT_EMAILS.ORDER_RESYNC,
      subject: 'CORS Missing Orders',
      html: `
        <p>
          Hi Customer Care,
        </p>
        ${
          syncedOrders.length > 0
            ? `
          <p>
            The following orders have been resync to OMS from store ${shopName}:
          </p>
          <ul>
            ${syncedOrders.map(order => `<li>${order}</li>`).join('')}
          </ul>
        `
            : ''
        }
        ${
          notSyncedOrders.length > 0
            ? `
          <p>
            The following orders have not been resync to CORS by cron:
          </p>
          <ul>
            ${notSyncedOrders.map(order => `<li>${order}</li>`).join('')}
          </ul>
        `
            : ''
        }
        <p>
          Thank You,<br>
          The Cuddle Clones Team
        </p>
      `,
    };

    try {
      this.logger.log('Sending email...');
      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully: ${JSON.stringify(info)}`);
    } catch (error) {
      this.logger.error('Error sending email:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async sendCustomEmail(
    to: string,
    subject: string,
    html: string,
  ): Promise<void> {
    const mailOptions = {
      from: `"Cuddle Clones - CORS" <${this.configService.get('SMTP_FROM_EMAIL')}>`,
      to,
      subject,
      html,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      this.logger.log(
        `Custom email sent successfully to ${to} with subject: ${subject}`,
      );
    } catch (error) {
      console.error('Error sending custom email:', error);
      throw new Error('Failed to send custom email');
    }
  }
}
