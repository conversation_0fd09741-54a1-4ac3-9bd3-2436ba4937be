// validators/is-unique.validator.ts
import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';

@ValidatorConstraint({ name: 'IsUnique', async: true })
@Injectable()
export class IsUniqueConstraint implements ValidatorConstraintInterface {
  constructor(private dataSource: DataSource) {}

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    const [EntityClass, column] = args.constraints;
    if (!EntityClass || !column) return false;

    const repository = this.dataSource.getRepository(EntityClass);
    const record = await repository
      .createQueryBuilder('entity')
      .where(`LOWER(entity.${column}) = LOWER(:value)`, { value })
      .getOne();
    return !record;
  }

  defaultMessage(args: ValidationArguments) {
    const [, column] = args.constraints;
    return `"${args.value}" for field "${column}" already exists. Must be unique.`;
  }
}
