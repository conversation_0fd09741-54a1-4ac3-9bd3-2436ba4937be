import {
  CroppingMethod,
  WorkflowCategory,
} from 'src/product-sku/enums/product-sku.enums';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { checkAttachmentStatusTransition } from './workflow.utils';
import { checkStatusTransition } from './workflow.utils';
import { DBHelper } from '../helpers/db.helpers';
import { WorkflowService } from 'src/common/workflow.service';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { Repository } from 'typeorm';
import { Order } from 'src/orders/entities/order.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { AttachmentStatus } from 'src/orders/enums/attachment-status.enum';
import { HttpException } from '@nestjs/common';
import { HttpStatus } from '@nestjs/common';

export class DuplicateCutoutImagesUtil {
  constructor(
    private readonly workflowService: WorkflowService,
    @InjectRepository(Attachment)
    private readonly attachmentRepository: Repository<Attachment>,
    @InjectRepository(LineItem)
    private readonly lineItemRepository: Repository<LineItem>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {}
  async checkCutoutProLineItems(lineItems: LineItem[]) {
    return lineItems.filter(
      item =>
        item.productSku?.croppingMethod === CroppingMethod.CUTOUT_PRO &&
        (item.productSku?.workflowCategory ===
          WorkflowCategory.CROP_IMAGE_ONLY ||
          item.productSku?.workflowCategory ===
            WorkflowCategory.CROP_IMAGE_AND_TEMPLATE),
    );
  }

  async getDuplicateAttachments(lineItems: LineItem[]) {
    const hashMap: Record<
      string,
      { attachments: Set<any>; lineItemIds: Set<string> }
    > = {};

    const allAttachments: { attachment: any; lineItemId: string }[] = [];
    for (const lineItem of lineItems) {
      if (lineItem.attachments && Array.isArray(lineItem.attachments)) {
        for (const attachment of lineItem.attachments) {
          if (attachment.url && attachment.id) {
            allAttachments.push({ attachment, lineItemId: lineItem.id });
          }
        }
      }
    }

    for (const { attachment, lineItemId } of allAttachments) {
      const url = attachment.url;
      try {
        const buffer = await this.getImageBuffer(url);
        const hash = await this.hashBuffer(buffer);

        if (!hashMap[hash]) {
          hashMap[hash] = {
            attachments: new Set(),
            lineItemIds: new Set(),
          };
        }
        hashMap[hash].attachments.add(attachment);
        hashMap[hash].lineItemIds.add(lineItemId);
      } catch (error) {
        // Optionally log or handle failed downloads
      }
    }

    // Group attachments by their hash (duplicate images will be in the same group)
    const groups = Object.values(hashMap).map(group => ({
      attachments: Array.from(group.attachments),
      lineItemIds: Array.from(group.lineItemIds),
    }));

    // Return all groups, including both duplicates and unique attachments
    // (each group is either a set of duplicates or a single unique attachment)
    return groups;
  }

  async getImageBuffer(url: string): Promise<Buffer> {
    const axios = require('axios');
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    return Buffer.from(response.data);
  }

  async hashBuffer(buffer: Buffer): Promise<string> {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(buffer).digest('hex');
  }

  async processDuplicateImagesForCutoutPro(
    duplicateImages: any[],
    cutoutProLineItems: LineItem[],
  ) {
    for (const duplicateGroup of duplicateImages) {
      const { attachments, lineItemIds } = duplicateGroup;

      // Only process if there is at least one attachment and one lineItemId
      if (!attachments?.length || !lineItemIds?.length) continue;

      // Find the first lineItem and its first attachment in this group
      const firstLineItemId = lineItemIds[0];
      const firstAttachment = attachments[0];

      // Find the actual lineItem object from cutoutProLineItems
      const firstLineItem = cutoutProLineItems.find(
        li => li.id === firstLineItemId,
      );

      if (firstLineItem && firstAttachment) {
        // Send the first lineItem and its attachment to the workflow service
        await this.workflowService.processLineItem(
          firstLineItem,
          firstLineItem.productSku,
          firstAttachment,
        );

        // Get the updated attachment after processing
        const updatedAttachment = await DBHelper.findOne(
          this.attachmentRepository,
          {
            where: { id: firstAttachment.id },
            relations: ['queue'],
          },
        );

        // Update the remaining attachments in this group with the cutoutProImageUrl and status
        for (let i = 0; i < attachments.length; i++) {
          const attachment = attachments[i];
          if (attachment && updatedAttachment?.cutoutProImageUrl) {
            attachment.cutoutProImageUrl = updatedAttachment.cutoutProImageUrl;
            attachment.queue = updatedAttachment.queue;
            await checkAttachmentStatusTransition(
              attachment.status,
              'Ready for Review',
            );
            attachment.status = 'Ready for Review';
            await this.attachmentRepository.save(attachment);
          } else {
            await checkAttachmentStatusTransition(
              attachment.status,
              'Cutout Pro Failed',
            );
            attachment.status = 'Cutout Pro Failed';
            await this.attachmentRepository.save(attachment);
          }
        }

        for (const lineItemId of lineItemIds) {
          const lineItem = await DBHelper.findOne(this.lineItemRepository, {
            where: { id: lineItemId },
            relations: ['attachments', 'order'],
          });
          const allReadyForReview = lineItem?.attachments?.every(
            att => att.cutoutProImageUrl && att.status === 'Ready for Review',
          );
          const cutoutProFailed = lineItem?.attachments?.some(
            att => att.status === 'Cutout Pro Failed',
          );

          if (lineItem && allReadyForReview) {
            await checkStatusTransition(lineItem.status, 'Crop Review');
            lineItem.status = 'Crop Review';
            await this.lineItemRepository.save(lineItem);
          }
          if (lineItem && cutoutProFailed) {
            lineItem.flagged = true;
            lineItem.flagReason = 'Cutout Pro Failed';
            const order = await DBHelper.findOne(this.orderRepository, {
              where: {
                id: lineItem.order.id,
              },
            });
            if (order) {
              order.flagged = true;
              order.flaggedAt = new Date();
              await this.orderRepository.save(order);
            }
            await this.lineItemRepository.save(lineItem);
          }
        }
      }
    }
  }

  async checkLineItemWithoutCutoutPro(lineItems: LineItem[]) {
    return lineItems.filter(
      item =>
        item.productSku?.croppingMethod !== CroppingMethod.CUTOUT_PRO &&
        item.productSku?.workflowCategory !==
          WorkflowCategory.CROP_IMAGE_ONLY &&
        item.productSku?.workflowCategory !==
          WorkflowCategory.CROP_IMAGE_AND_TEMPLATE,
    );
  }

  async processLineItemWithoutCutoutPro(lineItems: LineItem[]) {
    for (const lineItem of lineItems) {
      const getLineItem = await this.lineItemRepository.findOne({
        where: { id: lineItem.id },
        relations: [
          'attachments',
          'productSku',
          'productSku.artworkType',
          'order',
        ],
      });
      if (getLineItem) {
        await this.workflowService.processLineItem(
          getLineItem,
          getLineItem.productSku,
          getLineItem.attachments[0],
        );
      }
    }
  }

  async processSingleLineItemAttachments(
    attachments: Record<string, any>[],
    lineItem: LineItem,
  ) {
    for (const attachment of attachments) {
      const attachmentEntity = await DBHelper.findOne(
        this.attachmentRepository,
        {
          where: { id: attachment.attachment_id },
        },
      );
      if (!attachmentEntity) {
        throw new HttpException('Attachment not found', HttpStatus.NOT_FOUND);
      }
      attachmentEntity.status = AttachmentStatus.PENDING;
      attachmentEntity.url = attachment.new_url;
      attachmentEntity.lineItem = lineItem;
      await this.attachmentRepository.save(attachmentEntity);
    }
    const updatedLineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: { id: lineItem.id },
      relations: ['attachments', 'productSku'],
    });
    if (!updatedLineItem) {
      throw new HttpException('Line item not found', HttpStatus.NOT_FOUND);
    }
    const checkCutoutPro = await this.checkCutoutProLineItems([
      updatedLineItem,
    ]);
    const checkLineItemWithoutCutoutPro =
      await this.checkLineItemWithoutCutoutPro([updatedLineItem]);
    if (
      checkCutoutPro &&
      checkCutoutPro.length > 0 &&
      updatedLineItem.attachments
    ) {
      updatedLineItem.attachments = updatedLineItem.attachments.filter(
        att => att.status === AttachmentStatus.PENDING,
      );
      const duplicateAttachments = await this.getDuplicateAttachments([
        updatedLineItem,
      ]);
      await this.processDuplicateImagesForCutoutPro(duplicateAttachments, [
        updatedLineItem,
      ]);
    } else if (
      checkLineItemWithoutCutoutPro &&
      checkLineItemWithoutCutoutPro.length > 0
    ) {
      await this.processLineItemWithoutCutoutPro([updatedLineItem]);
    }
  }
}
