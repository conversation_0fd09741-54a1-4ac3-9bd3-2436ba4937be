import { countryCodeToTimezone } from './country-code-timezones';

export interface BusinessHours {
  startHour: number;
  endHour: number;
}

export class TimezoneUtil {
  static getTimezoneForCountry(countryCode: string): string | null {
    if (!countryCode) {
      return null;
    }

    const upperCountryCode = countryCode.toUpperCase();
    return countryCodeToTimezone[upperCountryCode] || null;
  }

  static isWithinBusinessHours(
    timezone: string,
    businessHours: BusinessHours = { startHour: 9, endHour: 19 },
  ): boolean {
    if (!timezone) {
      return false;
    }

    try {
      const now = new Date();
      const customerTime = new Date(
        now.toLocaleString('en-US', { timeZone: timezone }),
      );
      const currentHour = customerTime.getHours();
      console.log(businessHours, 'businessHours');
      console.log(currentHour, 'currentHour');
      return (
        currentHour >= businessHours.startHour &&
        currentHour < businessHours.endHour
      );
    } catch (error) {
      console.error(
        `Error checking business hours for timezone ${timezone}:`,
        error,
      );
      return false;
    }
  }

  static shouldSendSms(
    countryCode: string,
    businessHours: BusinessHours = { startHour: 9, endHour: 19 },
  ): boolean {
    if (!countryCode) {
      return false;
    }

    const timezone = this.getTimezoneForCountry(countryCode);
    if (!timezone) {
      return false;
    }

    const isWithinHours = this.isWithinBusinessHours(timezone, businessHours);
    console.log(isWithinHours, 'isWithinHours');
    return isWithinHours;
  }

  static getCustomerLocalTime(countryCode: string): string | null {
    if (!countryCode) {
      return null;
    }

    const timezone = this.getTimezoneForCountry(countryCode);
    if (!timezone) {
      return null;
    }

    try {
      const now = new Date();
      return now.toLocaleString('en-US', { timeZone: timezone });
    } catch (error) {
      console.error(
        `Error getting local time for timezone ${timezone}:`,
        error,
      );
      return null;
    }
  }
}
