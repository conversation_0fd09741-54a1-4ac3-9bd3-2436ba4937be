import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsArray,
  IsObject,
  IsEnum,
  IsOptional,
  IsBoolean,
} from 'class-validator';
import { IsUnique } from 'src/utils/unique-decorator';
import { Role } from '../entities/role.entity';
interface Permission {
  resource: string;
  actions: string[];
}

export class CreateRoleDto {
  @ApiProperty({ description: 'The name of the role' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'The isActive of the role' })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @ApiProperty({
    description: 'The permissions of the role',
    type: [Object],
    example: [
      {
        resource: 'Product Management',
        actions: ['view', 'edit', 'delete'],
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @IsObject({ each: true })
  rolePermissions: Permission[];
}
