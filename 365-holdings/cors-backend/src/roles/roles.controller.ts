import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtGuard } from 'src/auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '../permissions/permissions.guards';
import { Permissions } from '../permissions/permissions.decorator';
import { FieldPermissionsGuard } from '../permissions/field-permissions.guard';
import { FieldPermissions } from '../permissions/field-permissions.decorator';
import { PermissionResources } from '../constants/permission-resources';
import { getFieldPermissions } from 'src/permissions/field-permissions.config';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { ILike, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@UseGuards(JwtGuard, PermissionsGuard, FieldPermissionsGuard)
@ApiBearerAuth()
@ApiTags('Roles')
@Controller('roles')
export class RolesController {
  constructor(
    private readonly rolesService: RolesService,
    @InjectRepository(Queue)
    private queueRepo: Repository<Queue>,
  ) {}

  // @Permissions(PermissionResources.ROLES, 'Create Role')
  @Get('permissions')
  @ApiOperation({ summary: 'Get all permissions' })
  @ApiResponse({ status: 200, description: 'Returns all permissions' })
  async getPermissions() {
    const permissions = this.rolesService.getPermissions();
    const queues = await this.queueRepo.find({
      where: {
        name: ILike('%Ready for Artwork%'),
      },
    });
    const dynamicPermissions = queues.map(queue => ({
      resource: queue.name as PermissionResources,
      actions: [
        `View ${queue.name}`,
        `Start/Stop ${queue.name}`,
        `Add Request ${queue.name}`,
      ],
      dependencies: {
        [`Start/Stop ${queue.name}`]: [`View ${queue.name}`],
        [`Add Request ${queue.name}`]: [
          `Start/Stop ${queue.name}`,
          `View ${queue.name}`,
        ],
      },
    }));

    return [...permissions, ...dynamicPermissions];
  }
  @Permissions(PermissionResources.ROLES, 'Create Role')
  @Post()
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({ status: 201, description: 'Returns the created role' })
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.createRole(createRoleDto);
  }

  @Permissions(PermissionResources.ROLES, 'View Roles Listing Page')
  @Get()
  @ApiOperation({ summary: 'Get all roles' })
  @ApiResponse({ status: 200, description: 'Returns all roles' })
  @ApiQuery({
    name: 'q',
    required: false,
    type: String,
    example: 'firstName:like:test',
  })
  @ApiQuery({
    name: 'fq',
    required: false,
    type: String,
    example: 'isActive:eq:true',
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    type: String,
    example: 'name:asc;firstName:desc',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  async findAll(
    @Query('q') q?: string,
    @Query('fq') fq?: string,
    @Query('sort') sort?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.rolesService.findAll({
      q,
      fq,
      sort,
      page,
      limit,
      relations: ['users'],
    });
  }

  @Permissions(PermissionResources.ROLES, 'View Role Detail')
  @Get(':id')
  @ApiOperation({ summary: 'Get a role by ID' })
  @ApiResponse({ status: 200, description: 'Returns the role' })
  findOne(@Param('id') id: string) {
    return this.rolesService.findByIdOrThrow(id, ['users']);
  }

  @Permissions(PermissionResources.ROLES, 'Edit Role')
  @FieldPermissions(getFieldPermissions('roles', 'updateRole'))
  @Patch(':id')
  @ApiOperation({ summary: 'Update a role by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated role' })
  patch(@Param('id') id: string, @Body() updateRole: UpdateRoleDto) {
    return this.rolesService.updateRole(id, updateRole);
  }

  @Permissions(PermissionResources.ROLES, 'Edit Role')
  @Put(':id')
  @ApiOperation({ summary: 'Update a role by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated role' })
  put(@Param('id') id: string, @Body() updateRole: UpdateRoleDto) {
    return this.rolesService.updateRole(id, updateRole);
  }

  @Permissions(PermissionResources.ROLES, 'Delete Role')
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a role by ID' })
  @ApiResponse({ status: 200, description: 'Returns the deleted role' })
  remove(@Param('id') id: string) {
    return this.rolesService.deleteById(id);
  }
}
