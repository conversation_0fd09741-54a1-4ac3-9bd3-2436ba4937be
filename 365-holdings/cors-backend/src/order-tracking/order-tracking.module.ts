import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { OrderTrackingController } from './order-tracking.controller';
import { OrderTrackingService } from './order-tracking.service';
import { Order } from '../orders/entities/order.entity';
import { LineItem } from '../orders/entities/line-item.entity';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { Attachment } from '../attachments/entities/attachment.entity';
import { EmailUtil } from '../utils/email.util';
import { CustomerCommunicationModule } from '../jobs/customer-communication/customer-communication.module';
import { AttachmentModule } from '../attachments/attachment.module';
import { WorkflowService } from 'src/common/workflow.service';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { CutoutProService } from 'src/common/cutout-pro.service';
import { DuplicateCutoutImagesUtil } from 'src/utils/duplicate-cutout-images.util';

@Module({
  imports: [
    TypeOrmModule.forFeature([Order, LineItem, ProductSku, Attachment, Queue]),
    CustomerCommunicationModule,
    AttachmentModule,
    CacheModule.register(),
  ],
  controllers: [OrderTrackingController],
  providers: [
    OrderTrackingService,
    EmailUtil,
    WorkflowService,
    CutoutProService,
    DuplicateCutoutImagesUtil,
  ],
  exports: [OrderTrackingService],
})
export class OrderTrackingModule {}
