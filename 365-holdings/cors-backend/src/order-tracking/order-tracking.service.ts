import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Like } from 'typeorm';
import { Order } from '../orders/entities/order.entity';
import { LineItem } from '../orders/entities/line-item.entity';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { Attachment } from '../attachments/entities/attachment.entity';
import { EmailUtil } from '../utils/email.util';
import { accessEnv } from '../env.validation';
import { DBHelper } from '../helpers/db.helpers';
import { NewImageRequestWorkflow } from '../jobs/customer-communication/workflows/new-image-request/new-image-request.workflow';
import { AttachmentStatus } from '../orders/enums/attachment-status.enum';
import { LineItemStatus } from '../orders/enums/line-item-status.enum';
import {
  hasAnyNewImageRequestStatus,
  WORKFLOW_TAGS,
} from '../orders/enums/workflow-tags.enum';
import { WorkflowService } from 'src/common/workflow.service';
import { DuplicateCutoutImagesUtil } from 'src/utils/duplicate-cutout-images.util';

@Injectable()
export class OrderTrackingService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(LineItem)
    private readonly lineItemRepository: Repository<LineItem>,
    @InjectRepository(Attachment)
    private readonly attachmentRepository: Repository<Attachment>,
    private readonly newImageRequestQueue: NewImageRequestWorkflow,
    private readonly workflowService: WorkflowService,
    private readonly duplicateCutoutImagesUtil: DuplicateCutoutImagesUtil,
  ) {}

  async getOrderDetails(
    orderNumber: string,
    customerEmail: string,
  ): Promise<any> {
    const order = await DBHelper.findOne(this.orderRepository, {
      where: {
        shopifyOrderNumber: orderNumber,
        customerEmail: customerEmail,
      },
      relations: ['lineItems'],
    });

    if (!order) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Order not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const transformedOrder = {
      shopifyOrderNumber: order.shopifyOrderNumber,
      orderDate: order.orderDate,
      orderStatus: order.orderStatus,
      statusUpdatedAt: order.statusUpdatedAt,
      customerFirstName: order.customerFirstName,
      customerLastName: order.customerLastName,
      customerEmail: order.customerEmail,
      customerPhoneNumber: order.customerPhoneNumber,
      itemCount: order.itemCount,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      paymentInformation: order.paymentInformation,
      lineItems: order.lineItems?.map(item => ({
        itemNumber: item.itemNumber,
        quantity: item.quantity,
        status: item.status,
        priority: item.priority,
      })),
      orderStatusUrl: `${accessEnv('FRONTEND_URL')}/order-status?shopifyOrderNumber=${order.shopifyOrderNumber}&customerEmail=${encodeURIComponent(order.customerEmail)}`,
    };

    return transformedOrder;
  }

  async getArtfileByLineItem(
    orderNumber: string,
    email: string,
    itemNumber: string,
  ) {
    const order = await DBHelper.findOne(this.orderRepository, {
      where: {
        shopifyOrderNumber: orderNumber,
        customerEmail: email,
      },
      relations: ['lineItems'],
    });
    if (!order) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Order not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const lineItem = order.lineItems?.find(
      item => item.itemNumber === itemNumber,
    );
    if (!lineItem) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Line item not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const artfileAttachments = await DBHelper.findOne(
      this.attachmentRepository,
      {
        where: {
          lineItem: { id: lineItem.id },
          filename: Like('%Art File%'),
          status: AttachmentStatus.PENDING,
        },
      },
    );

    if (!artfileAttachments) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Artfile not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      orderNumber,
      itemNumber,
      artfileUrl: artfileAttachments.url,
    };
  }

  async getRejectedImages(
    orderNumber: string,
    email: string,
    itemNumber: string,
  ) {
    // Validate order and customer
    const order = await DBHelper.findOne(this.orderRepository, {
      where: {
        shopifyOrderNumber: orderNumber,
        customerEmail: email,
      },
      relations: ['lineItems'],
    });
    if (!order) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Order not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    const lineItem = order.lineItems.find(
      item => item.itemNumber === itemNumber,
    );
    if (!lineItem) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Line item not found in order',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const rejectedImages = await this.attachmentRepository.find({
      where: {
        lineItem: { id: lineItem.id },
        status: In([
          AttachmentStatus.NEW_IMAGE_REQUESTED,
          AttachmentStatus.REQUESTED_IMAGE_NOT_PROVIDED,
        ]),
      },
      order: { createdAt: 'DESC' },
    });
    if (!rejectedImages.length) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'No rejected images found for this item',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      orderNumber: orderNumber,
      itemNumber: itemNumber,
      images: rejectedImages.map(img => ({
        id: img.id,
        filename: img.filename,
        url: img.url,
        status: img.status,
        uploadedAt: img.createdAt,
      })),
    };
  }

  async uploadImagesToReplaceRejected(
    orderNumber: string,
    customerEmail: string,
    itemNumber: string,
    uploads: Array<{
      rejectedAttachmentId: string;
      imageUrl: string;
      size: number;
    }>,
  ) {
    // First validate the order and customer email
    const customer_response = true;
    const order = await DBHelper.findOne(this.orderRepository, {
      where: {
        shopifyOrderNumber: orderNumber,
        customerEmail: customerEmail,
      },
      relations: ['lineItems'],
    });

    if (!order) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Order not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const targetLineItem = await DBHelper.findOne(this.lineItemRepository, {
      where: {
        itemNumber: itemNumber,
        order: { id: order.id },
      },
      relations: ['attachments', 'productSku'],
    });

    if (!targetLineItem) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: `Line item with number ${itemNumber} not found in order`,
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    // Group uploads by line item and validate completeness
    const uploadsByLineItem = new Map<
      string,
      Array<{
        rejectedAttachmentId: string;
        imageUrl: string;
        size: number;
      }>
    >();

    // First pass: validate all attachments exist and belong to the target line item
    for (const upload of uploads) {
      const { rejectedAttachmentId } = upload;

      // Find the rejected attachment
      const rejectedAttachment = await DBHelper.findOne(
        this.attachmentRepository,
        {
          where: { id: rejectedAttachmentId },
          relations: ['lineItem'],
        },
      );

      if (!rejectedAttachment) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: `Rejected attachment with ID ${rejectedAttachmentId} not found`,
            error: 'Not Found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // Verify the attachment belongs to the target line item
      if (rejectedAttachment.lineItem.id !== targetLineItem.id) {
        throw new HttpException(
          {
            statusCode: HttpStatus.FORBIDDEN,
            message: `Attachment ${rejectedAttachmentId} does not belong to line item ${itemNumber}`,
            error: 'Forbidden',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      if (
        ![
          AttachmentStatus.NEW_IMAGE_REQUESTED,
          AttachmentStatus.REQUESTED_IMAGE_NOT_PROVIDED,
        ].includes(rejectedAttachment.status as AttachmentStatus)
      ) {
        throw new HttpException(
          {
            statusCode: HttpStatus.BAD_REQUEST,
            message: `Attachment ${rejectedAttachmentId} is not in "New Image Requested" status`,
            error: 'Bad Request',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Group by line item (should only be the target line item)
      const lineItemId = targetLineItem.id;
      if (!uploadsByLineItem.has(lineItemId)) {
        uploadsByLineItem.set(lineItemId, []);
      }
      uploadsByLineItem.get(lineItemId)!.push(upload);
    }

    // Second pass: validate completeness for the target line item
    const lineItemUploads = uploadsByLineItem.get(targetLineItem.id);
    if (!lineItemUploads) {
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'No valid uploads found for the specified line item',
          error: 'Bad Request',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // Get all attachments for this line item that need new images
    const attachmentsNeedingImages = await this.attachmentRepository.find({
      where: {
        lineItem: { id: targetLineItem.id },
        status: In([
          AttachmentStatus.NEW_IMAGE_REQUESTED,
          AttachmentStatus.REQUESTED_IMAGE_NOT_PROVIDED,
        ]),
      },
    });

    const providedAttachmentIds = new Set(
      lineItemUploads.map(upload => upload.rejectedAttachmentId),
    );

    const missingAttachmentIds = attachmentsNeedingImages
      .filter(att => !providedAttachmentIds.has(att.id))
      .map(att => att.id);

    if (missingAttachmentIds.length > 0) {
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: `Incomplete new image uploads for line item ${itemNumber}. Missing replacements for attachments: ${missingAttachmentIds.join(', ')}. All attachments requiring new images must be provided together.`,
          error: 'Bad Request',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    console.log(
      `Line item ${itemNumber} validation passed - all ${attachmentsNeedingImages.length} attachments requiring new images are provided`,
    );

    const uploadResults: Array<{
      rejectedAttachmentId: string;
      newAttachmentId: string;
      imageUrl: string;
    }> = [];

    // Third pass: process all uploads (now validated as complete)
    for (const upload of uploads) {
      const { rejectedAttachmentId, imageUrl, size } = upload;

      // Find the rejected attachment (already validated above)
      const rejectedAttachment = await DBHelper.findOne(
        this.attachmentRepository,
        {
          where: { id: rejectedAttachmentId },
          relations: ['lineItem'],
        },
      );

      // Update the rejected attachment status to indicate it has been replaced
      rejectedAttachment!.status = AttachmentStatus.IMAGE_REPLACED;
      rejectedAttachment!.url = imageUrl;
      await this.attachmentRepository.save(rejectedAttachment!);

      uploadResults.push({
        rejectedAttachmentId: rejectedAttachmentId,
        newAttachmentId: rejectedAttachment!.id,
        imageUrl: rejectedAttachment!.url,
      });
    }

    // Update line item status and workflow tags
    // targetLineItem.status = LineItemStatus.CUTOUT_PRO_REQUESTED;
    // await this.lineItemRepository.save(targetLineItem);

    // Get the line item with attachments where attachment status is IMAGE_REPLACED
    const lineItemWithAttachments = await this.lineItemRepository.findOne({
      where: { id: targetLineItem.id },
      relations: ['attachments', 'productSku'],
    });

    if (lineItemWithAttachments && lineItemWithAttachments.attachments) {
      lineItemWithAttachments.attachments =
        lineItemWithAttachments.attachments.filter(
          att => att.status === AttachmentStatus.IMAGE_REPLACED,
        );
    }

    if (lineItemWithAttachments) {
      const duplicateAttachments =
        await this.duplicateCutoutImagesUtil.getDuplicateAttachments([
          lineItemWithAttachments,
        ]);
      await this.duplicateCutoutImagesUtil.processDuplicateImagesForCutoutPro(
        duplicateAttachments,
        [lineItemWithAttachments],
      );
    }
    const hasRemainingNewImageRequests = hasAnyNewImageRequestStatus(
      order.lineItems,
    );

    // Remove workflow tag if no line items are in new image request workflow
    if (
      !hasRemainingNewImageRequests &&
      order.workflowTags.includes(WORKFLOW_TAGS.NEW_IMAGE_REQUEST)
    ) {
      order.workflowTags = order.workflowTags.filter(
        tag => tag !== WORKFLOW_TAGS.NEW_IMAGE_REQUEST,
      );
      await this.orderRepository.save(order);
      console.log(
        `Removed workflow tag '${WORKFLOW_TAGS.NEW_IMAGE_REQUEST}' from order ${orderNumber} - no more new image requests`,
      );
    }

    // Cancel communication reminders for the target line item
    try {
      await this.newImageRequestQueue.cancelAllCommunicationsForLineItem(
        targetLineItem.id,
      );
    } catch (error) {
      console.error('Failed to cancel communication reminders:', error);
    }

    return {
      message: 'Images uploaded successfully',
      uploads: uploadResults,
    };
  }
}
