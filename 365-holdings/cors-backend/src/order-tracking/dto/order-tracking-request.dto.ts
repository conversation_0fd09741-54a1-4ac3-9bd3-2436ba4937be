import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

export class OrderTrackingRequestDto {
  @ApiProperty({
    description: 'The shopify order number',
    example: '12345',
  })
  @IsString()
  @IsNotEmpty()
  orderNumber: string;

  @ApiProperty({
    description: 'Customer email address for validation',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}
