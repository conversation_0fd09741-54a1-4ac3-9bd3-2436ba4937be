import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(private readonly clsService: ClsService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    // Start a new CLS context
    await this.clsService.run(async () => {
      // Assuming you have the user ID in the request object after authentication
      const userId = (req as any).user?.id;

      if (userId) {
        this.clsService.set('userId', userId);
      }

      next();
    });
  }
}
