import {
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  CreateDateColumn,
} from 'typeorm';

@Entity('database_history')
@Index(['entityType', 'entityId']) // Composite index for faster lookups
@Index(['createdAt']) // Index for timestamp-based queries
export class DatabaseHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  entityId: string;

  @Column()
  entityType: string;

  @Column({ type: 'jsonb', nullable: true })
  oldValue: any;

  @Column({ type: 'jsonb', nullable: true })
  newValue: any;

  @Column({ type: 'jsonb', nullable: true })
  changes: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];

  @Column()
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'RESTORE' | 'WORK_NOTES';

  @Column({ nullable: true })
  userId: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;
}
