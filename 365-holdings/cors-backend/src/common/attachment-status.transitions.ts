import { AttachmentStatus } from 'src/orders/enums/attachment-status.enum';

export const ATTACHMENT_STATUS_TRANSITIONS: Partial<
  Record<AttachmentStatus, AttachmentStatus[]>
> = {
  [AttachmentStatus.PENDING]: [
    AttachmentStatus.MANUAL_CROP_NEEDED,
    AttachmentStatus.CROP_APPROVED,
    AttachmentStatus.CUTOUT_PRO_FAILED,
    AttachmentStatus.READY_FOR_REVIEW,
    AttachmentStatus.CUSTOMER_REJECTED,
    AttachmentStatus.CUSTOMER_APPROVED,
  ],
  [AttachmentStatus.READY_FOR_REVIEW]: [
    AttachmentStatus.CROP_APPROVED,
    AttachmentStatus.CROP_DENIED,
    AttachmentStatus.NEW_IMAGE_REQUESTED,
  ],
  [AttachmentStatus.CROP_DENIED]: [AttachmentStatus.CROP_APPROVED],
  [AttachmentStatus.NEW_IMAGE_REQUESTED]: [
    AttachmentStatus.REQUESTED_IMAGE_NOT_PROVIDED,
    AttachmentStatus.IMAGE_REPLACED,
  ],
  [AttachmentStatus.IMAGE_REPLACED]: [
    AttachmentStatus.READY_FOR_REVIEW,
    AttachmentStatus.MANUAL_CROP_NEEDED,
    AttachmentStatus.CUTOUT_PRO_FAILED,
  ],
  [AttachmentStatus.MANUAL_CROP_NEEDED]: [AttachmentStatus.CROP_APPROVED],
  [AttachmentStatus.REQUESTED_IMAGE_NOT_PROVIDED]: [
    AttachmentStatus.CS_CANCEL,
    AttachmentStatus.CROP_APPROVED,
    AttachmentStatus.CROP_DENIED,
  ],
};
