import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatabaseHistory } from './history.entity';
import { HistoryService } from './history.service';
import { HistorySubscriber } from './history.subscriber';
import { ClsModule } from 'nestjs-cls';

@Module({
  imports: [
    TypeOrmModule.forFeature([DatabaseHistory]),
    ClsModule.forRoot({
      global: true,
      middleware: { mount: true },
    }),
  ],
  providers: [HistoryService, HistorySubscriber],
  exports: [HistoryService],
})
export class HistoryModule {}
