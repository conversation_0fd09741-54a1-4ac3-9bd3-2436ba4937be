import {
  Injectable,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { Role } from 'src/roles/entities/role.entity';
import { User } from './entities/user.entity';
import * as bcrypt from 'bcryptjs';
import { BaseService } from 'src/common/base.service';
import { DBHelper } from 'src/helpers/db.helpers';

@Injectable()
export class UsersService extends BaseService<User> {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
  ) {
    super(usersRepository);
  }

  private async validateUniqueEmail(
    email: string,
    currentUserId?: string,
  ): Promise<void> {
    if (!email) return;

    const query = this.usersRepository
      .createQueryBuilder('user')
      .where('LOWER(user.email) = LOWER(:email)', { email });

    if (currentUserId) {
      query.andWhere('user.id != :userId', { userId: currentUserId });
    }

    const existingUser = await query.getOne();

    if (existingUser) {
      throw new BadRequestException('Email must be unique');
    }
  }

  async create(userData: CreateUserDto): Promise<User> {
    await this.validateUniqueEmail(userData.email);

    const roles = await DBHelper.findMany(this.roleRepository, {
      where: { id: In(userData.roleIds) },
    });
    DBHelper.validateEntityIds(roles, userData.roleIds, r => r.id, 'Role');

    const isAssigningOwnerRole = roles.some(role => role.name === 'Owner');
    if (isAssigningOwnerRole) {
      const ownerRole = roles.find(role => role.name === 'Owner');
      if (!ownerRole) {
        throw new NotFoundException('Owner role not found');
      }
      const ownerCount = await this.usersRepository
        .createQueryBuilder('user')
        .innerJoin('user.roles', 'role')
        .where('role.id = :roleId', { roleId: ownerRole.id })
        .getCount();

      if (ownerCount >= 2) {
        throw new BadRequestException(
          'Maximum limit of 2 users with Owner role reached',
        );
      }
    }
    const { roleIds, password, ...rest } = userData;
    const hashedPassword = await bcrypt.hash(password, 10);
    const user = this.usersRepository.create({
      ...rest,
      roles,
      password: hashedPassword,
    });
    const savedUser = await this.usersRepository.save(user);
    const { password: _, ...result } = savedUser;
    return result as User;
  }

  async update(id: string, userData: UpdateUserDto): Promise<User> {
    const user = await this.findByIdOrThrow(id);

    if (userData.email) {
      await this.validateUniqueEmail(userData.email, id);
    }

    let roles: Role[] | undefined;
    if (userData.roleIds) {
      roles = await DBHelper.findMany(this.roleRepository, {
        where: { id: In(userData.roleIds) },
      });
      DBHelper.validateEntityIds(roles, userData.roleIds, r => r.id, 'Role');
      const isAssigningOwnerRole = roles.some(role => role.name === 'Owner');
      if (isAssigningOwnerRole) {
        const ownerRole = roles.find(role => role.name === 'Owner');
        if (!ownerRole) {
          throw new NotFoundException('Owner role not found');
        }
        const ownerCount = await this.usersRepository
          .createQueryBuilder('user')
          .innerJoin('user.roles', 'role')
          .where('role.id = :roleId', { roleId: ownerRole.id })
          .andWhere('user.id != :userId', { userId: id })
          .getCount();

        if (ownerCount >= 2) {
          throw new BadRequestException(
            'Maximum limit of 2 users with Owner role reached',
          );
        }
      }
    }
    const { roleIds, password, ...rest } = userData;
    const updateData: UpdateUserDto = { ...rest };
    if (password) {
      updateData.password = await bcrypt.hash(password, 10);
    }
    Object.assign(user, updateData);
    if (roles) {
      user.roles = roles;
    }
    const savedUser = await this.usersRepository.save(user);
    const { password: _, ...result } = savedUser;
    return result as User;
  }

  async transferOwnership(
    currentUser: User,
    newOwnerId: string,
  ): Promise<User> {
    const userId = (currentUser as any).user;
    const hasOwnerRole = currentUser.roles.some(role => role.name === 'Owner');
    if (!hasOwnerRole) {
      throw new ForbiddenException(
        'You are not authorized to transfer ownership',
      );
    }

    const newOwner = await this.findByIdOrThrow(newOwnerId);
    if (!newOwner) {
      throw new NotFoundException(`User with id ${newOwnerId} not found`);
    }

    if (newOwner.id === currentUser.id) {
      throw new BadRequestException(
        'You cannot transfer ownership to yourself',
      );
    }

    const ownerRole = await this.roleRepository.findOne({
      where: { name: 'Owner' },
    });
    if (!ownerRole) {
      throw new NotFoundException('Owner role not found');
    }

    const existingUser = await this.usersRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });
    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    existingUser.roles = existingUser.roles.filter(
      role => role.name !== 'Owner',
    );
    await this.usersRepository.save(existingUser);
    const newOwnerWithRoles = await this.usersRepository.findOne({
      where: { id: newOwnerId },
      relations: ['roles'],
    });

    if (!newOwnerWithRoles) {
      throw new NotFoundException('New owner user not found');
    }

    if (!newOwnerWithRoles.roles) {
      newOwnerWithRoles.roles = [ownerRole];
    } else if (!newOwnerWithRoles.roles.some(role => role.name === 'Owner')) {
      newOwnerWithRoles.roles = [ownerRole];
    } else {
      throw new BadRequestException(
        `User with id ${newOwnerId} already has the Owner role`,
      );
    }
    await this.usersRepository.save(newOwnerWithRoles);
    return newOwnerWithRoles;
  }

  async findByIdWithRolesAndPermissions(id: string): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { id },
      relations: ['roles'],
    });
    if (!user) throw new NotFoundException('User not found');
    return user;
  }
}
