import axios from 'axios';
import { accessEnv } from '../env.validation';

const SHOPIFY_STORE_URL = accessEnv('SHOPIFY_STORE_URL');
const SHOPIFY_ACCESS_TOKEN = accessEnv('SHOPIFY_ACCESS_TOKEN');
const NGROK_URL = accessEnv('NGROK_URL');

const ORDER_UPDATE_WEB_HOOK_TOPIC = 'orders/updated';
const ORDER_UPDATE_WEB_HOOK_URL = `${NGROK_URL}/shopify/web_hooks/orders`;

const PRODUCT_UPDATE_WEB_HOOK_TOPIC = 'products/update';
const PRODUCT_UPDATE_WEB_HOOK_URL = `${NGROK_URL}/product-sku/webhook/update`;
const META_FIELDS = ['global', 'cuddleclones'];

async function createWebhook(
  topic: string,
  url: string,
  metaFields: string[] = [],
) {
  try {
    const response = await axios.post(
      `${SHOPIFY_STORE_URL}/webhooks.json`,
      {
        webhook: {
          topic: topic,
          address: url,
          format: 'json',
          metafield_namespaces: metaFields,
        },
      },
      {
        headers: {
          'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          'Content-Type': 'application/json',
        },
      },
    );
    console.log('Webhook created:', response.data.webhook);
  } catch (error) {
    console.error('Error creating webhook:', error.message);
  }
}

async function getAllWebhooks() {
  try {
    console.log(`${SHOPIFY_STORE_URL}/webhooks.json`);
    const response = await axios.get(`${SHOPIFY_STORE_URL}/webhooks.json`, {
      headers: {
        'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
        'Content-Type': 'application/json',
      },
    });
    console.log(response.data);
    return response.data.webhooks;
  } catch (error) {
    console.error('Error getting webhooks:', error.message);
    return [];
  }
}

async function deleteWebhook(id: number) {
  try {
    await axios.delete(`${SHOPIFY_STORE_URL}/webhooks/${id}.json`, {
      headers: {
        'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
        'Content-Type': 'application/json',
      },
    });
    console.log(`Webhook ${id} deleted successfully`);
  } catch (error) {
    console.error('Error deleting webhook:', error.message);
  }
}

async function destroyAllWebhooks() {
  const webhooks = await getAllWebhooks();
  for (const webhook of webhooks) {
    await deleteWebhook(webhook.id);
  }
}

// Command runner
(async () => {
  const command = process.argv[2];

  switch (command) {
    case 'create:order':
      await createWebhook(
        ORDER_UPDATE_WEB_HOOK_TOPIC,
        ORDER_UPDATE_WEB_HOOK_URL,
        META_FIELDS,
      );
      break;
    case 'create:product':
      await createWebhook(
        PRODUCT_UPDATE_WEB_HOOK_TOPIC,
        PRODUCT_UPDATE_WEB_HOOK_URL,
        META_FIELDS,
      );
      break;
    case 'destroy:all':
      await destroyAllWebhooks();
      break;
    case 'get:all':
      const webhooks = await getAllWebhooks();
      console.log('All webhooks:', webhooks);
      break;
    default:
      console.log(`Unknown command: ${command}`);
  }

  process.exit();
})();
