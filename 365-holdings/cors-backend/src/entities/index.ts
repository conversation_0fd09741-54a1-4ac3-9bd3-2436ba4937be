import { User } from '../users/entities/user.entity';
import { Role } from '../roles/entities/role.entity';
import { Product } from '../product-sku/entities/product.entity';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { ArtworkType } from '../artwork-types/entities/artwork-type.entity';
import { Vendor } from '../vendors/entities/vendor.entity';
import { SkuRelationship } from '../product-sku/entities/sku-relationship.entity';
import { Order } from '../orders/entities/order.entity';
import { LineItem } from '../orders/entities/line-item.entity';
import { DatabaseHistory } from '../common/history.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { VendorSupportedSku } from 'src/vendors/entities/vendor-supported-sku.entity';
import { VendorApiDetail } from 'src/vendors/entities/vendor-api-detail.entity';
import { LineItemRequest } from 'src/orders/entities/line-item-request.entity';
import { SkuAddon } from 'src/product-sku/entities/sku-addon.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';

export const entities = [
  User,
  Role,
  Product,
  ProductSku,
  ArtworkType,
  Vendor,
  SkuRelationship,
  Order,
  LineItem,
  DatabaseHistory,
  Attachment,
  VendorSupportedSku,
  VendorApiDetail,
  LineItemRequest,
  SkuAddon,
  Queue,
];
