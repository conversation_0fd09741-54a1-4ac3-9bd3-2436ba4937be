import { LineItemStatus } from "./line-item-status.enum";

const NewImageRequestStatuses = [
    LineItemStatus.AWAITING_CUSTOMER_RESPONSE,
    LineItemStatus.REQUESTED_IMAGE_NOT_PROVIDED,
];
  
  export function isNewImageRequestStatus(status: string): boolean {
  return NewImageRequestStatuses.includes(status as LineItemStatus);
}

export function getNewImageRequestStatuses(): LineItemStatus[] {
  return NewImageRequestStatuses;
}

export function hasAnyNewImageRequestStatus(lineItems: Array<{ status: string }>): boolean {
  return lineItems.some(item => isNewImageRequestStatus(item.status));
  }

  export const WORKFLOW_TAGS = {
    NEW_IMAGE_REQUEST: 'New Image Request',
    CUSTOMER_APPROVAL: 'Customer Approval',
  } as const;

export type WorkflowTag = typeof WORKFLOW_TAGS[keyof typeof WORKFLOW_TAGS];
  