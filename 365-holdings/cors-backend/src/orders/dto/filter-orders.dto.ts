import { IsArray, IsInt, IsOptional, IsString, ValidateNested, IsPositive, IsEnum, IsObject, IsDateString, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';
import { IsStringBooleanNumber } from 'src/utils/validation.decorator';
import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { OrderStatus } from '../enums/order.enums';

function IsArrayOfArraysContainingObjects(objectType: any, validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IsArrayOfArraysContainingObjects',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!Array.isArray(value)) {
            return false;
          }

          return value.every(item => {
            return (
              Array.isArray(item) &&
              item.every(
                subItem =>
                  typeof subItem === 'object' &&
                  subItem.hasOwnProperty('attribute') &&
                  subItem.hasOwnProperty('operator') &&
                  subItem.hasOwnProperty('value'),
              )
            );
          });
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} should be an array of arrays, with each inner array containing valid FilterItem objects`;
        },
      },
    });
  };
}

class DateRange {
  @IsDateString()
  start: string;

  @IsDateString()
  end: string;
}

class FilterItem {
  @IsString()
  attribute: string;

  @IsString()
  operator: string;

  @ValidateIf(o => o.operator === 'between')
  @ValidateNested()
  @Type(() => DateRange)
  dateRangeValue?: DateRange;

  @ValidateIf(o => o.operator !== 'between')
  @IsStringBooleanNumber({
    message: 'Value must be a string, boolean, or number',
  })
  value?: string | boolean | number | string[];

  get effectiveValue() {
    if (this.operator === 'between') {
      // Handle both value and dateRangeValue formats
      const dateRange = this.dateRangeValue || (this.value as any);
      if (dateRange && typeof dateRange === 'object' && 'start' in dateRange && 'end' in dateRange) {
        return dateRange;
      }
      return null;
    }
    return this.value;
  }

  validate() {
    // Check if the attribute is an enum field
    if (this.attribute === 'orderDate') {
      // Validate date range format
      if (this.operator === 'between') {
        const dateRange = this.effectiveValue;
        if (!dateRange || !('start' in dateRange) || !('end' in dateRange)) {
          throw new Error('For date range filtering, value must be an object with start and end dates');
        }
        // Validate date format
        const startDate = new Date(dateRange.start);
        const endDate = new Date(dateRange.end);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          throw new Error('Invalid date format. Use ISO 8601 format (YYYY-MM-DD HH:mm:ss)');
        }
        if (startDate > endDate) {
          throw new Error('Start date must be before or equal to end date');
        }
      } else {
        throw new Error('For orderDate field, only "between" operator is allowed');
      }
    } else if (this.attribute === 'orderStatus') {
      // Validate the value is a valid enum value
      const values = Array.isArray(this.value) ? this.value : [this.value];
      const invalidValues = values.filter(value => !Object.values(OrderStatus).includes(value as OrderStatus));
      if (invalidValues.length > 0) {
        throw new Error(`Invalid orderStatus value(s): ${invalidValues.join(', ')}. Must be one of: ${Object.values(OrderStatus).join(', ')}`);
      }
      // Allow both 'eq' and 'ne' operators for enum fields
      if (this.operator !== 'eq' && this.operator !== 'ne') {
        throw new Error('For orderStatus field, only "eq" or "ne" operators are allowed');
      }
    }
  }
}

export class FilterOrdersDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterItem)
  @IsArrayOfArraysContainingObjects(FilterItem, {
    message: 'Filters should be an array of arrays, with each inner array containing valid FilterItem objects',
  })
  filters: FilterItem[][];

  @IsOptional()
  @IsInt()
  @IsPositive()
  page: number;

  @IsOptional()
  @IsInt()
  @IsPositive()
  limit: number;

  validate() {
    // Validate each filter item
    this.filters.forEach(filterGroup => {
      filterGroup.forEach(filter => {
        filter.validate();
      });
    });
  }
} 