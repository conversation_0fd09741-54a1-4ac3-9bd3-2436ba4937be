import { Module } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Order } from './entities/order.entity';
import { LineItem } from './entities/line-item.entity';
import { Attachment } from '../attachments/entities/attachment.entity';
import { AttachmentModule } from '../attachments/attachment.module';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { User } from 'src/users/entities/user.entity';
import { BullModule } from '@nestjs/bullmq';
import { CutoutProService } from 'src/common/cutout-pro.service';
import { WorkflowService } from 'src/common/workflow.service';
import { CacheModule } from '@nestjs/cache-manager';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { EmailUtil } from 'src/utils/email.util';
import { PermissionsModule } from '../permissions/permissions.module';
import { Product } from 'src/product-sku/entities/product.entity';
import { DuplicateCutoutImagesUtil } from 'src/utils/duplicate-cutout-images.util';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Order,
      LineItem,
      Attachment,
      ProductSku,
      User,
      Queue,
      Product,
    ]),
    AttachmentModule,
    BullModule.registerQueue({
      name: 'order-sync',
    }),
    BullModule.registerQueue({
      name: 'shopify-order',
    }),
    CacheModule.register(),
    PermissionsModule,
  ],
  controllers: [OrdersController],
  providers: [
    OrdersService,
    CutoutProService,
    WorkflowService,
    EmailUtil,
    DuplicateCutoutImagesUtil,
  ],
})
export class OrdersModule {}
