import { PermissionResources } from '../constants/permission-resources';
import { FieldPermission } from './field-permissions.decorator';

export const FIELD_PERMISSIONS_CONFIG = {
  orders: {
    updateOrder: [
      {
        field: 'orderStatus',
        permission: 'Update Order Status',
        resource: PermissionResources.ORDERS,
      },
      {
        field: 'flagged',
        permission: 'Flag Order',
        resource: PermissionResources.ORDERS,
      },
      {
        field: 'priorities',
        permission: 'Update Order Priority',
        resource: PermissionResources.ORDERS,
      },
    ] as FieldPermission[],
  },

  lineItems: {
    updateLineItem: [
      {
        field: 'status',
        permission: 'Cancel Line Item',
        resource: PermissionResources.LINE_ITEMS,
      },
      {
        field: 'quantity',
        permission: 'Edit Line Item',
        resource: PermissionResources.LINE_ITEMS,
      },
      {
        field: 'productSkuId',
        permission: 'Edit Line Item',
        resource: PermissionResources.LINE_ITEMS,
      },
      {
        field: 'flagged',
        permission: 'Flag Line Item',
        resource: PermissionResources.LINE_ITEMS,
      },
      {
        field: 'cancelReason',
        permission: 'Cancel Line Item',
        resource: PermissionResources.LINE_ITEMS,
      },
      {
        field: 'flagReason',
        permission: 'Flag Line Item',
        resource: PermissionResources.LINE_ITEMS,
      },
    ] as FieldPermission[],
  },

  users: {
    updateUser: [
      {
        field: 'isActive',
        permission: 'Activate User',
        resource: PermissionResources.USERS,
      },
      {
        field: 'isActive',
        permission: 'Deactivate User',
        resource: PermissionResources.USERS,
      },
    ] as FieldPermission[],
  },

  roles: {
    updateRole: [
      {
        field: 'isActive',
        permission: 'Activate Role',
        resource: PermissionResources.ROLES,
      },
      {
        field: 'isActive',
        permission: 'Deactivate Role',
        resource: PermissionResources.ROLES,
      },
    ] as FieldPermission[],
  },

  vendors: {
    updateVendor: [
      {
        field: 'isActive',
        permission: 'Activate Vendor',
        resource: PermissionResources.VENDORS,
      },
      {
        field: 'isActive',
        permission: 'Deactivate Vendor',
        resource: PermissionResources.VENDORS,
      },
    ] as FieldPermission[],
  },

  productSku: {
    updateProductSku: [
      {
        field: 'isActive',
        permission: 'Activate/Deactivate SKU',
        resource: PermissionResources.PIMS,
      },
    ] as FieldPermission[],
  },
};

export function getFieldPermissions(
  module: string,
  endpoint: string,
): FieldPermission[] {
  return FIELD_PERMISSIONS_CONFIG[module]?.[endpoint] || [];
}

export function getModuleFieldPermissions(
  module: string,
): Record<string, FieldPermission[]> {
  return FIELD_PERMISSIONS_CONFIG[module] || {};
}
