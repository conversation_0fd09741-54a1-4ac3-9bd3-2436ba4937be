import { Module, forwardRef } from '@nestjs/common';
import { CaslAbilityFactory } from './casl-ability.factory';
import { PermissionsGuard } from './permissions.guards';
import { FieldPermissionsGuard } from './field-permissions.guard';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [forwardRef(() => UsersModule)],
  providers: [CaslAbilityFactory, PermissionsGuard, FieldPermissionsGuard],
  exports: [CaslAbilityFactory, PermissionsGuard, FieldPermissionsGuard],
})
export class PermissionsModule {}
