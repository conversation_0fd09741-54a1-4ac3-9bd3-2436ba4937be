import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import {
  FIELD_PERMISSIONS_KEY,
  FieldPermission,
} from './field-permissions.decorator';

@Injectable()
export class FieldPermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const fieldPermissions = this.reflector.getAllAndOverride<
      FieldPermission[]
    >(FIELD_PERMISSIONS_KEY, [context.getHandler(), context.getClass()]);

    if (!fieldPermissions) return true;

    const request = context.switchToHttp().getRequest();
    const { body } = request;
    const ability = request.ability; // Use the ability from PermissionsGuard

    if (!ability) {
      throw new ForbiddenException('Permissions not available');
    }

    // Check each field that's being updated
    for (const fieldPermission of fieldPermissions) {
      if (body[fieldPermission.field] !== undefined) {
        const fieldValue = body[fieldPermission.field];
        
        // Special handling for isActive field
        if (fieldPermission.field === 'isActive') {
          const hasPermission = this.checkIsActivePermission(
            ability,
            fieldPermission,
            fieldValue,
            fieldPermissions
          );
          
          if (!hasPermission) {
            const action = fieldValue ? 'activate' : 'deactivate';
            throw new ForbiddenException(
              `You do not have permission to ${action} this ${fieldPermission.resource.toLowerCase().replace(/\s+/g, ' ').trim()}`
            );
          }
        } else {
          // Regular field permission check
          const hasPermission = ability.can(
            fieldPermission.permission.toLowerCase(),
            fieldPermission.resource,
          );

          if (!hasPermission) {
            throw new ForbiddenException(
              `You do not have permission to update the '${fieldPermission.field}' field`,
            );
          }
        }
      }
    }

    return true;
  }

  private checkIsActivePermission(
    ability: any,
    currentPermission: FieldPermission,
    fieldValue: boolean,
    allFieldPermissions: FieldPermission[]
  ): boolean {
    // Find all permissions for the isActive field
    const isActivePermissions = allFieldPermissions.filter(
      fp => fp.field === 'isActive' && fp.resource === currentPermission.resource
    );

    // If there's only one permission for isActive, check if user has it
    if (isActivePermissions.length === 1) {
      return ability.can(
        currentPermission.permission.toLowerCase(),
        currentPermission.resource,
      );
    }

    // If there are multiple permissions (activate/deactivate), check based on the value
    if (isActivePermissions.length === 2) {
      const activatePermission = isActivePermissions.find(
        p => p.permission.toLowerCase().includes('activate') && !p.permission.toLowerCase().includes('deactivate')
      );
      const deactivatePermission = isActivePermissions.find(
        p => p.permission.toLowerCase().includes('deactivate')
      );

      if (fieldValue === true && activatePermission) {
        // Trying to activate - check activate permission
        return ability.can(
          activatePermission.permission.toLowerCase(),
          activatePermission.resource,
        );
      } else if (fieldValue === false && deactivatePermission) {
        // Trying to deactivate - check deactivate permission
        return ability.can(
          deactivatePermission.permission.toLowerCase(),
          deactivatePermission.resource,
        );
      }
    }

    // Fallback: check the current permission
    return ability.can(
      currentPermission.permission.toLowerCase(),
      currentPermission.resource,
    );
  }
}
