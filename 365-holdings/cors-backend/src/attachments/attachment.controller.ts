import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Get,
  Param,
  Delete,
  UseGuards,
  Headers,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Multer } from 'multer';
import { AttachmentService } from './attachment.service';
import { SkipAuth } from 'src/auth/decorators/public.decorator';
import { verifyShopifyToken } from 'src/utils/workflow.utils';
import { JwtGuard } from 'src/auth/guards/jwt-auth.guard';

const heicConvert = require('heic-convert');

@UseGuards(JwtGuard)
@Controller('attachments')
export class AttachmentController {
  constructor(private readonly attachmentService: AttachmentService) {}

  @Post('upload-image')
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(@UploadedFile() file: Multer.File) {
    const processedFile = await this.convertHeicToJpgIfNeeded(file);
    return this.attachmentService.uploadImage(processedFile);
    // return this.attachmentService.uploadImage(file);
  }

  @SkipAuth()
  @Post('upload')
  @UseInterceptors(FileInterceptor('image'))
  async upload(
    @UploadedFile() image: Multer.File,
    @Headers() headers: Record<string, any>,
  ) {
    verifyShopifyToken(headers);
    const processedFile = await this.convertHeicToJpgIfNeeded(image);
    return this.attachmentService.uploadImage(processedFile);
    // return this.attachmentService.uploadImage(image);
  }

  @Get()
  async getAttachments() {
    return this.attachmentService.getAttachments();
  }

  @Delete(':id')
  async deleteAttachment(@Param('id') id: string) {
    return this.attachmentService.delete(id);
  }

  private async convertHeicToJpgIfNeeded(
    file: Multer.File,
  ): Promise<Multer.File> {
    if (!file.mimetype.includes('heic') && !file.mimetype.includes('heif')) {
      return file;
    }

    try {
      const outputBuffer = await heicConvert({
        buffer: file.buffer,
        format: 'JPEG',
        quality: 0.9,
      });

      return {
        ...file,
        buffer: outputBuffer,
        originalname: file.originalname.replace(/\.(heic|heif)$/i, '.jpg'),
        mimetype: 'image/jpeg',
      };
    } catch (error) {
      console.error('HEIC conversion failed:', error);
      return file;
    }
  }
}
