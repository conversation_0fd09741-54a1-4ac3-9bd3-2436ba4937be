import { Injectable } from '@nestjs/common';
import { StorageStrategy } from './storage.strategy';
import { v2 as cloudinary } from 'cloudinary';
import { Multer } from 'multer';
import { accessEnv } from '../../env.validation';

@Injectable()
export class CloudinaryStorageStrategy implements StorageStrategy {
  constructor() {
    // Initialize cloudinary configuration
    cloudinary.config({
      cloud_name: accessEnv('CLOUDINARY_CLOUD_NAME') || '',
      api_key: accessEnv('CLOUDINARY_API_KEY') || '',
      api_secret: accessEnv('CLOUDINARY_API_SECRET') || '',
    });
  }

  async uploadFile(file: Multer['File']) {
    // Convert buffer to base64
    const base64File = file.buffer.toString('base64');
    const dataURI = `data:${file.mimetype};base64,${base64File}`;

    // Upload to cloudinary
    const result = await cloudinary.uploader.upload(dataURI, {
      resource_type: 'auto', // Automatically detect the resource type
    });

    return {
      url: result.secure_url,
      filename: result.public_id,
      mimetype: file.mimetype,
      size: file.size
    };
  }

  async deleteFile(filename: string) {
    try {
      await cloudinary.uploader.destroy(filename);
    } catch (error) {
      console.error('Error deleting file from Cloudinary:', error);
    }
  }
} 