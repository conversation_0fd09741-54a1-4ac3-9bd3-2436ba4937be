# Instructions for developers

## Development Guide Extending Entities with Base Entity (src/common/base.entity.ts)

The `BaseEntity` class serves as a foundation class that should be extended by all entities in the system.

### BaseEntity Overview

This class provides the following common fields that are useful across all entities:

| Field | Type | Description |
|-------|------|-------------|
| id | UUID | Primary key identifier |
| createdAt | Timestamp | When the entity was created |
| updatedAt | Timestamp | When the entity was last updated | 
| deletedAt | Timestamp | Soft delete timestamp (null if not deleted) |

### Example Usage

To use the BaseEntity in your own entities, extend it like this:

```typescript
import { BaseEntity } from '../common/base.entity';

export class MyEntity extends BaseEntity {
    name: string
}
```

## Development Guide Extending Services with BaseService (src/common/base.service.ts)

This guide explains how to extend your services with the `BaseService` to leverage common CRUD operations and maintain consistency across the application.

### BaseService Overview

The `BaseService` provides a foundation for CRUD operations with the following features:
- Generic type support for any entity extending `BaseEntity`
- Common CRUD operations (Create, Read, Update, Delete)
- Soft delete support
- Error handling
- Type safety with TypeORM's `DeepPartial` and `FindOptionsWhere`

### Example Usage

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../common/base.service';
import { YourEntity } from './entities/your-entity.entity';

@Injectable()
export class YourService extends BaseService<YourEntity> {
  constructor(
    @InjectRepository(YourEntity)
    private readonly yourRepository: Repository<YourEntity>,
  ) {
    super(yourRepository);
  }
}
```

## Development Guide Using DBHelper for Database Operations (src/helpers/db.helpers.ts)

The `DBHelper` class provides a set of static methods to handle common database operations with proper error handling and type safety. It's designed to work with entities that extend `BaseEntity`.

### Features
- Type-safe database operations
- Built-in soft delete handling
- Standardized error handling
- Support for TypeORM's query options

### Available Methods

| Method | Description |
|--------|-------------|
| findMany | Retrieves multiple entities with optional query options |
| findByIdOrThrow | Finds an entity by ID or throws NotFoundException |
| findOne | Finds a single entity with optional query options |

### Example Usage

```typescript
import { DBHelper, IOptions } from 'src/helpers/db.helpers';
import { YourEntity } from './your-entity.entity';

// In your service:
async findEntities() {
  const options: IOptions<YourEntity> = {
    where: { /* your conditions */ },
    select: { /* fields to select */ }
  };
  
  return await DBHelper.findMany(this.repository, options);
}

// Find by ID with error handling
async getEntityById(id: string) {
  return await DBHelper.findByIdOrThrow(this.repository, id);
}

// Find one with custom options
async findSpecificEntity() {
  const options: IOptions<YourEntity> = {
    where: { /* your conditions */ }
  };
  
  return await DBHelper.findOne(this.repository, options);
}
```

### Options Interface

The `IOptions` interface allows you to specify:
- `where`: Conditions for filtering entities
- `select`: Fields to include in the result
- More options coming soon (orderBy, limit, offset, filters)

### Soft Delete Handling

By default, all find operations exclude soft-deleted records. You can include them by setting the `excludeDeleted` parameter to `false`:

```typescript
const includeDeleted = await DBHelper.findMany(repository, options, false);
```
