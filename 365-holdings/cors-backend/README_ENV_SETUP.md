# Environment Variables Setup Guide

This guide explains how to add new environment variables to the project. When adding a new environment variable, you need to update it in three places:

1. `.env.example` file
2. GitHub Actions workflow file (`.github/workflows/deploy.yml`)
3. GitHub repository secrets/variables

## Step-by-Step Guide

### 1. Add to `.env.example`

Add your new variable to the `.env.example` file with a descriptive comment:

```env
# Description of what this variable does
NEW_VARIABLE=default_value
```

### 2. Update GitHub Actions Workflow

Add the variable to the `.github/workflows/deploy.yml` file in the "Create .env file" step:

```yaml
- name: Create .env file
  run: |
    cat > .env << EOL
    # ... existing variables ...
    NEW_VARIABLE=${{ secrets.NEW_VARIABLE }}  # If it's a secret
    # OR
    NEW_VARIABLE=${{ vars.NEW_VARIABLE }}     # If it's a variable
    EOL
```

### 3. Add to GitHub Repository

#### For Secrets (sensitive data):

1. Go to your GitHub repository
2. Navigate to Settings > Secrets and variables > Actions
3. Click "New repository secret"
4. Add your secret:
   - Name: `NEW_VARIABLE`
   - Value: Your secret value

#### For Variables (non-sensitive data):

1. Go to your GitHub repository
2. Navigate to Settings > Secrets and variables > Actions
3. Click "New repository variable"
4. Add your variable:
   - Name: `NEW_VARIABLE`
   - Value: Your variable value

## Best Practices

1. **Naming Convention**:

   - Use UPPERCASE for all environment variables
   - Use underscores to separate words
   - Be descriptive but concise

2. **Security**:

   - Use `secrets` for sensitive data (passwords, API keys, tokens)
   - Use `vars` for non-sensitive data (URLs, feature flags, configuration)

3. **Documentation**:
   - Always add a comment in `.env.example` explaining the variable's purpose
   - Include any constraints or expected format
   - Document if the variable is required or optional

## Example

Let's say you want to add a new API key for a service:

1. Add to `.env.example`:

```env
# API key for External Service
EXTERNAL_SERVICE_API_KEY=your_api_key_here
```

2. Add to `.github/workflows/deploy.yml`:

```yaml
- name: Create .env file
  run: |
    cat > .env << EOL
    # ... existing variables ...
    EXTERNAL_SERVICE_API_KEY=${{ secrets.EXTERNAL_SERVICE_API_KEY }}
    EOL
```

3. Add to GitHub:
   - Go to repository Settings > Secrets and variables > Actions
   - Add new repository secret:
     - Name: `EXTERNAL_SERVICE_API_KEY`
     - Value: Your actual API key

## Testing

After adding a new environment variable:

1. Test it locally by adding it to your `.env` file
2. Test the deployment by triggering the GitHub Actions workflow
3. Verify the variable is correctly set in your deployed application
