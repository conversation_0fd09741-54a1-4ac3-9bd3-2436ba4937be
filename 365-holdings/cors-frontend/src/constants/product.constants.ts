export enum ProductCategory {
  RUSH_ADD_ONS = 'Rush & Add Ons',
  PAJAMAS = 'Pajamas',
  PLUSH = 'Plush',
  JEWELRY = 'Jewelry',
  SOCKS = 'Socks',
  SWEATERS = 'Sweaters',
  APPAREL = 'Apparel',
  PORTRAITS = 'Portraits',
  ACCESSORIES = 'Accessories',
}

// Keep the array for backward compatibility
export const PRODUCT_CATEGORIES = Object.values(ProductCategory);

// Remake reason constants
export const REMAKE_REASONS = [
  'Product Quality',
  'Production Error',
  'Shipping Damage',
  'Customer Dissatisfaction',
  'Wrong Item Sent',
  'Color Issues',
  'Size Issues',
  'Design Issues'
];

export const DETAILED_REMAKE_REASONS: Record<string, string[]> = {
  'Product Quality': ['Poor material quality', 'Defective product', 'Inconsistent quality'],
  'Production Error': ['Wrong customization', 'Missing elements', 'Printing issues'],
  'Shipping Damage': ['Crushed in transit', 'Water damage', 'Torn packaging'],
  'Customer Dissatisfaction': ['Not as expected', 'Changed mind', 'Better option available'],
  'Wrong Item Sent': ['Incorrect SKU', 'Wrong color', 'Wrong size'],
  'Color Issues': ['Color mismatch', 'Faded colors', 'Wrong color combination'],
  'Size Issues': ['Too small', 'Too large', 'Incorrect dimensions'],
  'Design Issues': ['Design misalignment', 'Missing design elements', 'Blurry design']
};

export enum CroppingMethod {
  MANUAL = 'MANUAL',
  CUTOUT_PRO = 'CUTOUT_PRO',
}

export enum CropType {
  FACE_CUTOUT = 'FACE_CUTOUT',
  BACKGROUND_REMOVAL = 'BACKGROUND_REMOVAL',
}

export enum FileUploadFormat {
  BMP = '.bmp',
  PNG = '.png',
  JPEG = '.jpeg',
}

export enum ImageInheritRule {
  NONE = '',
  AUTO_INHERIT = 'AUTO_INHERIT',
  REQUIRES_REVIEW = 'REQUIRES_REVIEW',
}

export enum ExceptionHandlingRule {
  NONE = '',
  QUEUE = 'Send to Image Needed Queue',
  FLAG = 'Auto-Assign from First Image on Order',
}

export enum RoutingMethod {
  OMS = 'OMS',
  CORS = 'CORS',
}

export enum ShippingMethod {
  STANDARD = 'STANDARD',
  EXPEDITED = 'EXPEDITED',
}


export enum CancelReason {
  CUSTOMER_REQUESTED = 'Customer requested cancellation',
  OUT_OF_STOCK = 'Item out of stock',
  PRICING_ERROR = 'Pricing error',
  DUPLICATE_ORDER = 'Duplicate order',
  SHIPPING_ADDRESS_ISSUE = 'Shipping address issue',
  PAYMENT_ISSUE = 'Payment issue',
  OTHER = 'Other',
}

// Helper to get all cancel reasons as an array
export const CANCEL_REASONS = Object.values(CancelReason);

export enum VendorAssignmentRule {
  FASTEST_CYCLE_TIME = 'Fastest Cycle Time',
  LOWEST_COST = 'Lowest Cost',
}
