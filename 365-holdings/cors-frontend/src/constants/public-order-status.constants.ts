export type OrderStatusType = 'unfulfilled' | 'partially fulfilled' | 'fulfilled';

export const ORDER_STATUS_LABELS: Record<OrderStatusType, string> = {
  'unfulfilled': 'Processing',
  'partially fulfilled': 'Partially Shipped',
  'fulfilled': 'Completed'
};

export const ORDER_STATUS_COLORS: Record<OrderStatusType, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
  'unfulfilled': 'warning',
  'partially fulfilled': 'info',
  'fulfilled': 'success'
};

export const LINE_ITEM_STATUS_LABELS: Record<string, string> = {
  // Statuses actually used in PublicOrderStatus components
  'Awaiting Customer Response': 'Awaiting Customer Response',
  'Customer Approval Pending': 'Customer Approval Pending',
  'Customer Contact Needed': 'Customer Contact Needed',
  // Fallback labels for other statuses that might be used
  'Line Item Received': 'Line Item Received',
  'CutOut Pro Requested': 'CutOut Pro Requested',
  'Requested Image Not Provided': 'Requested Image Not Provided',
  'Crop Review': 'Crop Review',
  'Crop Needed': 'Crop Needed',
  'Template Placement': 'Template Placement',
  'Artwork Needed': 'Artwork Needed',
  'Revision Requested': 'Revision Requested',
  'Ready for Vendor': 'Ready for Vendor',
};

// Status mapping for display labels - only includes statuses used in PublicOrderStatus components
export const LINE_ITEM_STATUS_DISPLAY_MAP: Record<string, string> = {
  'Line Item Received': 'ORDER RECEIVED',
  'CutOut Pro Requested': 'ORDER RECEIVED',
  'Requested Image Not Provided': 'CUSTOMER ACTION NEEDED',
  'Awaiting Customer Response': 'CUSTOMER ACTION NEEDED',
  'Crop Review': 'ART IN PROGRESS',
  'Crop Needed': 'ART IN PROGRESS',
  'Template Placement': 'ART IN PROGRESS',
  'Artwork Needed': 'ART IN PROGRESS',
  'Revision Requested': 'ART IN PROGRESS',
  'Customer Approval Pending': 'CUSTOMER ACTION NEEDED',
  'Ready for Vendor': 'READY FOR PRODUCTION',
};

// Helper function to get display label for line item status
export const getLineItemStatusDisplayLabel = (status: string): string => {
  return LINE_ITEM_STATUS_DISPLAY_MAP[status] || status;
}; 