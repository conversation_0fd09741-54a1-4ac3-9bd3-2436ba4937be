import { TVendor } from '@/types/vendors.types';

export const allVendors: TVendor[] = [
  {
    id: 'V001',
    name: 'Premium Print',
    email: '<EMAIL>',
    phone: '******-0101',
    address: '123 Print Street, Los Angeles, CA 90210',
    supportedSkus: ['PLUSH-001', 'PLUSH-002', 'PLUSH-003', 'PLUSH-004'],
    costPerSku: {
      'PLUSH-001': 12.5,
      'PLUSH-002': 15.75,
      'PLUSH-003': 18.25,
      'PLUSH-004': 22.0,
    },
    averageCycleTime: 5,
    maximumDailyCapacity: 500,
    rushOrderSupport: true,
    supportedManufacturingTypes: ['print', 'embroidery'],
    shippingPreferences: {
      usesShipStation: true,
      preferredCarrier: 'FedEx',
    },
    supportedArtworkTypes: ['Line Art', 'Cutout', 'Vector Graphics'],
    apiIntegrationEnabled: true,
    flatFileFormat: 'CSV',
  },
  {
    id: 'V002',
    name: 'Quick Stitch Express',
    email: '<EMAIL>',
    phone: '******-0102',
    address: '456 Embroidery Lane, Miami, FL 33101',
    supportedSkus: ['PLUSH-002', 'PLUSH-005', 'PLUSH-006'],
    costPerSku: {
      'PLUSH-002': 18.5,
      'PLUSH-005': 25.0,
      'PLUSH-006': 30.75,
    },
    averageCycleTime: 3,
    maximumDailyCapacity: 300,
    rushOrderSupport: true,
    supportedManufacturingTypes: ['embroidery', 'sewing'],
    shippingPreferences: {
      usesShipStation: false,
      preferredCarrier: 'UPS',
    },
    supportedArtworkTypes: ['Embroidery Files', 'Vector Graphics'],
    apiIntegrationEnabled: false,
    flatFileFormat: 'XML',
  },
  {
    id: 'V003',
    name: 'Standard Manufacturing Co.',
    email: '<EMAIL>',
    phone: '******-0103',
    address: '789 Industrial Blvd, Chicago, IL 60601',
    supportedSkus: ['PLUSH-001', 'PLUSH-003', 'PLUSH-007', 'PLUSH-008'],
    costPerSku: {
      'PLUSH-001': 10.25,
      'PLUSH-003': 16.5,
      'PLUSH-007': 20.0,
      'PLUSH-008': 24.75,
    },
    averageCycleTime: 7,
    maximumDailyCapacity: 800,
    rushOrderSupport: false,
    supportedManufacturingTypes: ['print', 'knitting'],
    shippingPreferences: {
      usesShipStation: true,
      preferredCarrier: 'USPS',
    },
    supportedArtworkTypes: ['Line Art', 'Knitted Patterns', 'Cutout'],
    apiIntegrationEnabled: true,
    flatFileFormat: 'JSON',
  },
  {
    id: 'V004',
    name: 'Artisan Crafters',
    email: '<EMAIL>',
    phone: '******-0104',
    address: '321 Craft Avenue, Portland, OR 97201',
    supportedSkus: ['PLUSH-004', 'PLUSH-009', 'PLUSH-010'],
    costPerSku: {
      'PLUSH-004': 28.0,
      'PLUSH-009': 35.5,
      'PLUSH-010': 42.25,
    },
    averageCycleTime: 10,
    maximumDailyCapacity: 150,
    rushOrderSupport: false,
    supportedManufacturingTypes: ['handcrafted', 'embroidery'],
    shippingPreferences: {
      usesShipStation: false,
      preferredCarrier: 'DHL',
    },
    supportedArtworkTypes: ['Hand Drawn', 'Embroidery Files', 'Custom Designs'],
    apiIntegrationEnabled: false,
    flatFileFormat: 'PDF',
  },
  {
    id: 'V005',
    name: 'Rapid Production Inc.',
    email: '<EMAIL>',
    phone: '******-0105',
    address: '654 Speed Way, Dallas, TX 75201',
    supportedSkus: ['PLUSH-001', 'PLUSH-002', 'PLUSH-003', 'PLUSH-004', 'PLUSH-005'],
    costPerSku: {
      'PLUSH-001': 14.0,
      'PLUSH-002': 17.25,
      'PLUSH-003': 19.5,
      'PLUSH-004': 26.0,
      'PLUSH-005': 28.75,
    },
    averageCycleTime: 2,
    maximumDailyCapacity: 1000,
    rushOrderSupport: true,
    supportedManufacturingTypes: ['print', 'embroidery', 'knitting'],
    shippingPreferences: {
      usesShipStation: true,
      preferredCarrier: 'FedEx',
    },
    supportedArtworkTypes: ['Line Art', 'Cutout', 'Vector Graphics', 'Knitted Patterns'],
    apiIntegrationEnabled: true,
    flatFileFormat: 'CSV',
  },
  {
    id: 'V006',
    name: 'Eco-Friendly Manufacturing',
    email: '<EMAIL>',
    phone: '******-0106',
    address: '987 Green Street, Seattle, WA 98101',
    supportedSkus: ['PLUSH-006', 'PLUSH-011', 'PLUSH-012'],
    costPerSku: {
      'PLUSH-006': 32.0,
      'PLUSH-011': 38.5,
      'PLUSH-012': 45.25,
    },
    averageCycleTime: 8,
    maximumDailyCapacity: 200,
    rushOrderSupport: true,
    supportedManufacturingTypes: ['sustainable', 'print'],
    shippingPreferences: {
      usesShipStation: true,
      preferredCarrier: 'USPS',
    },
    supportedArtworkTypes: ['Eco-Friendly Designs', 'Line Art', 'Natural Patterns'],
    apiIntegrationEnabled: false,
    flatFileFormat: 'XML',
  },
  {
    id: 'V007',
    name: 'Bulk Order Specialists',
    email: '<EMAIL>',
    phone: '******-0107',
    address: '147 Bulk Avenue, Atlanta, GA 30301',
    supportedSkus: ['PLUSH-001', 'PLUSH-002', 'PLUSH-003', 'PLUSH-007', 'PLUSH-008'],
    costPerSku: {
      'PLUSH-001': 8.75,
      'PLUSH-002': 12.0,
      'PLUSH-003': 14.25,
      'PLUSH-007': 18.5,
      'PLUSH-008': 22.0,
    },
    averageCycleTime: 12,
    maximumDailyCapacity: 2000,
    rushOrderSupport: false,
    supportedManufacturingTypes: ['print', 'mass production'],
    shippingPreferences: {
      usesShipStation: false,
      preferredCarrier: 'UPS',
    },
    supportedArtworkTypes: ['Line Art', 'Cutout', 'Bulk Designs'],
    apiIntegrationEnabled: true,
    flatFileFormat: 'JSON',
  },
  {
    id: 'V008',
    name: 'Luxury Custom Creations',
    email: '<EMAIL>',
    phone: '******-0108',
    address: '258 Luxury Lane, Beverly Hills, CA 90210',
    supportedSkus: ['PLUSH-009', 'PLUSH-010', 'PLUSH-013'],
    costPerSku: {
      'PLUSH-009': 45.0,
      'PLUSH-010': 55.75,
      'PLUSH-013': 75.5,
    },
    averageCycleTime: 15,
    maximumDailyCapacity: 50,
    rushOrderSupport: true,
    supportedManufacturingTypes: ['handcrafted', 'embroidery', 'custom'],
    shippingPreferences: {
      usesShipStation: false,
      preferredCarrier: 'DHL',
    },
    supportedArtworkTypes: ['Custom Designs', 'Luxury Patterns', 'Hand Drawn'],
    apiIntegrationEnabled: false,
    flatFileFormat: 'PDF',
  },
];
