'use server';

import apiClient from '@/utils/axios';

const accessToken = process.env.NEXT_PUBLIC_CUSTOMER_VALIDATION_TOKEN;
const shopifyToken = process.env.SHOPIFY_TOKEN;

// Headers for different client types
const getHeadersForClient = (client: 'public' | 'customer' | 'system') => {
  switch (client) {
    case 'public':
      return {
        Authorization: `Bearer ${accessToken || ''}`,
      };
    case 'customer':
      return {
        'api-token': shopifyToken || '',
      };
    default:
      return undefined;
  }
};

export async function apiCall<T>(
  method: string,
  endpoint: string,
  data?: any, 
  client?: 'public' | 'customer' | 'system',
): Promise<T> {
  try {
    const config: any = {
      method,
      url: endpoint,
      data,
   
    };
    if (client) {
      // Get headers based on client type
      const customHeaders = getHeadersForClient(client || 'system');
      if (customHeaders) {
        config.headers = customHeaders;
      }
    }

    const response = await apiClient(config);
    return response?.data;
  } catch (error: any) {
    console.error(`API Error (${endpoint}):`, error?.message);
    throw error;
  }
}
