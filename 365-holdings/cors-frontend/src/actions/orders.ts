'user Server'
import apiClient from "@/utils/axios";
import {PaginationQueryParams } from "@/utils/paginationParamHelper";
import { Filter } from "@/utils/validation";

interface FiltersType{
    filters : Omit<Filter,'required'>[][], 
    page : number, 
    limit : number
}

export function normalizeFilterValues(filters: any[][]) {
  if (!filters || !Array.isArray(filters)) return [];
  
  return filters.map(group => 
    group.filter(filter => {
      // Filter out empty filters
      if (!filter.attribute || !filter.operator) return false;
      
      // Special handling for date range
      if (filter.attribute === 'orderDate' && filter.operator === 'between') {
        return filter.value && 
               typeof filter.value === 'object' && 
               filter.value.start && 
               filter.value.end;
      }
      
      // Regular filter validation
      return filter.value !== undefined && 
             filter.value !== null && 
             filter.value !== '';
    }).map(filter => {
      if (filter.attribute === 'priorities') {
        if (Array.isArray(filter.value)) {
          return {
            ...filter,
          };
        }
        return {
          ...filter,
          value: [filter.value],
        };
      }
      
 
      
      // Handle orderDate with between operator
      if (filter.attribute === 'orderDate' && filter.operator === 'between') {
        // Ensure the date range has both start and end dates
        if (filter.value && typeof filter.value === 'object' && filter.value.start && filter.value.end) {
          // Validate that start date is not after end date
          const startDate = new Date(filter.value.start);
          const endDate = new Date(filter.value.end);
          
          if (startDate > endDate) {
            console.error('Invalid date range: start date is after end date');
            // Return a valid filter with corrected dates (swap them)
            return {
              ...filter,
              operator: 'between',
              value: {
                start: filter.value.end,
                end: filter.value.start
              }
            };
          }
          
          return {
            ...filter,
            operator: 'between',
            value: {
              start: filter.value.start,
              end: filter.value.end
            }
          };
        }
      }
      
      return filter;
    })
  ).filter(group => group.length > 0); 
}

export async function fetchOrders({ page , limit , q, fq = 'flagged:eq:false' }: PaginationQueryParams) {
  if(!page || !limit) return;
  const validPage = Math.max(1, page); // Ensure page is at least 1
  
  try {
    const res = await apiClient.get(`/orders?page=${validPage}&limit=${limit}${q ? `&q=${q}` :  ''}${fq ? `&fq=${fq}` : ''}`);
    
    if (!res.data) {
      console.error('Fetch orders failed: No data returned');
      throw new Error('Failed to fetch orders data');
    }
    
    return res.data;
  } catch (error) {
    console.error('Error in fetchOrders:', error);
    throw error;
  }
};

export async function filterOrder(filters: FiltersType){
    try {
        // Ensure we're sending a properly formatted request
        const payload = {
            filters: filters.filters || [],
            page: filters.page || 1,
            limit: filters.limit || 25
        };
        
        const res = await apiClient.post(`/orders/advanced-filter`, payload);
        
        if (!res.data) {
            console.error('Filter request failed: No data returned');
            throw new Error('Failed to fetch filtered data');
        }
        
        return res.data;
    } catch (error) {
        console.error('Error in filterOrder:', error);
        throw error;
    }
};
