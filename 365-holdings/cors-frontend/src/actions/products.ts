import apiClient from "@/utils/axios";
import { Product } from '@/types/productTypes';
import { handleApiError } from "@/utils/errorHandler";

export interface UpdateProductsParams {
  id: string;
  value: Record<string, unknown>;
}

export async function updateProduct({ id, value }: UpdateProductsParams): Promise<Product> {
  try {
    const response = await apiClient.patch(`/product-sku/${id}`, value);
    return response.data;
  } catch (error: unknown) {
    console.error(`Error in updateProduct (ID: ${id}):`, error);
    throw handleApiError(error, `Failed to update product (ID: ${id})`);
  }
}

export async function fetchFilteredProductsSkusAdvanced({ filters, page, limit }: { filters: any; page: number; limit: number; }): Promise<{data: Product[], count: number, page: number, limit: number}> {
  try {
    const response = await apiClient.post('/product-sku/advanced-filter', {
      filters,
      page,
      limit,
    });
    return response.data;
  } catch (error: unknown) {
    console.error('Error in fetchFilteredProductsSkusAdvanced:', error);
    throw handleApiError(error, 'Failed to fetch filtered products');
  }
}
