'use server';
import apiClient from '@/utils/axios';

export const fetchFilteredProductsSkus = async (filters: any) => {
  const res = await apiClient.post(`/product-sku/advanced-filter`, filters);
  if (!res.status) throw new Error('Failed to fetch filtered data');
  return res.data;
};

export const fetchHistorydata = async (id: string, page?: number, pageSize?: number) => {
  try {
    const queryParams = new URLSearchParams();

    if (page !== undefined) queryParams.append('page', page.toString());
    if (pageSize !== undefined) queryParams.append('limit', pageSize.toString());

    const url = `/product-sku/${id}/history${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    const res = await apiClient.get(url);

    if (!res.data.data) {
      throw new Error('Failed to fetch history data');
    }

    return res.data;
  } catch (error) {
    console.error('Error fetching history data:', error);
    throw error;
  }
};

export const fetchArtworkTypes = async () => {
  try {
    const response = await apiClient.get('/artwork-types');
    if (!response.data.data) {
      throw new Error('Failed to fetch artwork types');
    }
    return response.data.data;
  } catch (error) {
    console.error('Error fetching artwork types:', error);
    throw error;
  }
};

export const fetchShopifyVariants = async () => {
  try {
    const response = await apiClient.get('/product-sku/shopify-native-variants');
    if (!response.data.data) {
      throw new Error('Failed to fetch shopify variants');
    }
    return response.data.data;
  } catch (error) {
    console.error('Error fetching shopify variants:', error);
    throw error;
  }
};

export const fetchSKU = async () => {
  try {
    const response = await apiClient.get('/product-sku/shopify-native-variants');
    if (response.data.data) {
      throw new Error('Failed to fetch shopify variants');
    }
    return response.data.data;
  } catch (error) {
    console.error('Error fetching shopify variants:', error);
    throw error;
  }
};
