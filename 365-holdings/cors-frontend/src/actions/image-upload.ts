'use server';

import apiClient from '@/utils/axios';

export async function UploadImage(formData: FormData): Promise<string> {
  try {
    const response = await apiClient.post('/attachments/upload-image', formData);
    if (!response.data.url) {
      throw new Error('Failed to upload image');
    }
    return response?.data?.url;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw new Error('Failed to upload image');
  }
}
