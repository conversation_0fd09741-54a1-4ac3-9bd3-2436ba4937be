'use client';
import { ActionsTarget, ActionsType, AppAbility } from '@/libs/casl/ability';
import { useAbility } from '@/libs/casl/AbilityContext';
import { LockOutlined } from '@mui/icons-material';
import { Container, Button, Typography } from '@mui/material';
import { Box } from '@mui/material';
import { useRouter } from 'nextjs-toploader/app';
import React from 'react';

const PermissionsBoundary = ({
  children,
  action,
  actionTarget,
}: {
  children: React.ReactNode;
  action: ActionsType;
  actionTarget: ActionsTarget;
}) => {
  const ability: AppAbility | null = useAbility();
  const router = useRouter();
  const allowed = action && actionTarget ? ability?.can(action, actionTarget) : false;

  if (allowed) {
    return <>{children}</>;
  }

  return (
    <Container maxWidth="sm" className="h-full flex items-center justify-center">
      <Box className=" p-8 rounded-lg shadow-lg text-center" sx={{ maxWidth: 600, width: '100%' }}>
        <LockOutlined sx={{ fontSize: 60, mb: 2 }} />
        <Typography variant="h5" color="text.primary" gutterBottom>
          Access Denied
        </Typography>
        <Typography variant="body1" color="text.secondary">
          You are not authorized to access this page.
        </Typography>
        <Button
          variant="contained"
          color="primary"
          className="mt-4"
          onClick={() => router.push('/')}
        >
          Return to Home
        </Button>
      </Box>
    </Container>
  );
};

export default PermissionsBoundary;
