import { ArtFile, CustomerImage } from './customer-contact.types';

export interface OrderDetailsTabProps {
  orderData: {
    id: string;
    shopifyOrderNumber: string;
    orderDate: string;
    orderStatus: string;
    priorities: string[];
    itemCount: number;
    customerFirstName: string;
    customerLastName: string;
    customerEmail: string;
    lineItems: OrderLineItemProps[];
  };
  fetchOrderData: () => void;
  lineItemParam?: string | null;
}

export interface OrderLineItemProps {
  id: string;
  itemNumber: string;
  productSku: { sku: string; workflowCategory: string; isAddon: boolean };
  quantity: number;
  status: string;
  isRemake?: boolean;
  priority?: string;
  flagged?: boolean;
  flagReason?: string;
  metadata?: Record<string, any>;
  artworkRequired?: boolean;
  artworkRequests?: any[];
  type?: string;
  addonType?: string;
  remakeReason?: string[];
  remakeReasons?: string[];
  detailedRemakeReason?: string[];
  attachments?: LineItemAttachmentProps[];
  customOptions?: Record<string, any>;
  shopifyVariant?: string;
  customerImages?: CustomerImage[];
  artFiles?: ArtFile[];
}

export interface CustomAttachmentProps {
  images: {
    url: string | null;
    baseName?: string;
    cutoutProImageUrl: string | null;
    cutoutProImageName: string | null;
  }[];
  artFiles: {
    url: string | null;
    baseName?: string;
    status: string | null;
  }[];
  showArtfile: boolean;
  showCustomerImages: boolean;
  showCutoutProImage?: boolean;
}

export interface LineItemAttachmentProps {
  id: string;
  url: string;
  filename: string;
  mimetype: string;
  size: number;
  status: string;
  completedArtFileUrl: string | null;
  cutoutProImageUrl: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
