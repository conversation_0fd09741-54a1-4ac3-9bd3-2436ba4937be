export interface CustomerImage {
  id: string;
  fileName: string;
  thumbnailUrl: string;
}

export interface ArtFile {
  id: string;
  fileName: string;
  thumbnailUrl: string;
}

export interface Question {
  id: string;
  issueCategory: string;
  selectedImages: string[];
  referenceImages: File[];
  message: string;
}

export interface CustomerContactDialogProps {
  open: boolean;
  onClose: () => void;
  onSend: (questions: Question[]) => void;
  customerImages: CustomerImage[];
  artFiles: ArtFile[];
  itemId: string;
  updating?: boolean;
}

export interface  CutomerContactSweaterThreadColor {
  name: string;
  hex: string;
}


export interface CustomerContactEmailTemplate {
  content: string;
  subject: string;
}



export interface CustomerContactReason {
  reason: string;      
  label: string;
  emailTemplate: CustomerContactEmailTemplate;
  actions: string[];
  sweaterColors?: CutomerContactSweaterThreadColor[];
}

export interface CustomerContactReasonsResponse {
  reasons: CustomerContactReason[];
  total: number;
}

export type CustomerContactFormType = {
  reason: string;
  message: string;
  selectedThreadColors: string[];
};