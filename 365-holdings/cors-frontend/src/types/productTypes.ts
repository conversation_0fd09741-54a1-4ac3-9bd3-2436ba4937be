
export type Product = {
  id: string
  sku: string
  isActive: boolean
  productName: string
  productCategory: string
  product_category?: string[]
  products?: any[]
  parentSku: string | null
  childSkus?: string[]
  shopifyNativeVariant: string
  rushDays: number | null
  hasRush: boolean
  metadata?: any
}

export type PaginationState = {
  data: Product[]
  count: number
  page: number
  limit: number
}
