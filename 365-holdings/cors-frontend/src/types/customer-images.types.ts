export interface ProcessedImage {
  id: string;
  fileName: string;
  fileUrl: string;
  thumbnailUrl: string;
  uploadedAt: string;
  isApproved: boolean;
  queue?: string;
}

export interface CustomerImage {
  id: string;
  fileName: string;
  fileUrl: string;
  thumbnailUrl: string;
  uploadedAt: string;
  processedImage?: ProcessedImage;
}

export interface CustomerImagesSectionProps {
  // Accept metadata object instead of direct images
  metadata?: Record<string, any> | null;
  formatDate: (dateString: string) => string;
}
