import { FieldsType, Options } from '@/redux-store/stores/common.store';


export interface LocalFieldsType extends Omit<FieldsType, 'type'> {
    type: 'select' | 'number' | 'text' | 'multi_select' | 'date' | 'shopify_native_variant';
    operators?: { label: string; value: string }[];
  }


export interface ParentChildOption {
    label: string;
    value: string;
  }
  

export type FilterValueFieldValue =
  | string
  | number
  | boolean
  | (string | number | boolean)[]
  | { start?: string; end?: string }
  | { key: string; value: string }
  | ParentChildOption[];

export type FilterValueFieldSelectValue =
  | string
  | number
  | boolean
  | (string | number | boolean)[];



  export interface FilterValueFieldProps {
    data: LocalFieldsType;
    value: FilterValueFieldValue;
    onChange: (value: FilterValueFieldValue) => void;
    label?: string;
    asyncOptions?: { label: string; value: string }[];
    isLoading?: boolean;
    onSearch?: (term: string) => void;
  }