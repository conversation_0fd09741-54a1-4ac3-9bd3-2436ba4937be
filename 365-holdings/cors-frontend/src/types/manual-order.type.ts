import { ExceptionHandlingRule, ImageInheritRule } from '@/constants/product.constants';

export interface AddonData {
  id: string;
  isAddon: boolean;
  addonLevel: string;
  variantId: string;
  products: string[];
}
export interface SingleSKU {
  accountingWorkPaperMapping: boolean;
  eligibleVendors: any[];
  vendorAssignmentRule: string;
  productionCycleTime: string;
  maxCapacityPerDay: string;
  id: string;
  sku: string;
  isActive: boolean;
  hasRush: boolean;
  rushDays: number | null;
  requireImageUpload: boolean | null;
  requireCropping: boolean;
  croppingMethod: 'MANUAL' | 'CUTOUT_PRO' | null;
  cropType: 'FACE_CUTOUT' | 'BACKGROUND_REMOVAL' | null;
  croppingReviewRequired: boolean;
  artworkRequired: boolean;
  requireCustomerArtworkApproval: boolean;
  imageNamingConvention: string | null;
  requireTemplate: boolean;
  fileUploadFormat: '.bmp' | '.png' | '.jpeg' | null;
  workflowCategory: string | null;
  isAddon: boolean;
  // SKU Mapping & Image Inheritance attributes
  canInheritImage: boolean;
  imageInheritRule: ImageInheritRule | null;
  imageInheritancePriority: number | null;
  canManualOverride: boolean;
  exceptionHandlingRule: ExceptionHandlingRule | null;
  customerFollowupEnabled: boolean;
  followupTiming: number | null;

  // Order Processing & Routing attributes
  routingMethod: 'OMS' | 'CORS' | null;
  requirePreprocessing: boolean;
  processingPriority: number | null;
  shipStationStore: string | null;

  // Shipping & Fulfillment attributes
  shippingWeight: number | null;
  productLength: number | null;
  productWidth: number | null;
  productHeight: number | null;
  shippingMethod: 'STANDARD' | 'EXPEDITED' | null;

  // Pricing & Accounting attributes
  chinaWOFEPrice: number | string | null;
  hasWorkPaper: boolean;

  // Upsell & Cross-Sell Configuration
  canUpSold: boolean;
  canCrossSold: boolean;

  // Manufacturing & Vendor Assignment attributes
  primaryVendor: any;
  vendorSkuMapping: string | null;

  // Additional properties
  metadata?: Record<string, any>;

  // Existing relationships
  product?: {
    id: string;
    name: string;
    category: string;
    description: string | null;
  };
  products?: Array<{
    id?: string;
    name: string;
    category: string;
    description: string | null;
    shopifyProductId?: string;
    metadata?: Record<string, any> | null;
  }>;
  artworkType?: {
    id: string;
    name: string;
  } | null;
  parentSku?: Array<{
    parentSku: {
      id: {
        key: string;
        value: string;
      };
      sku: string;
    };
  }>;
  childSku?: Array<{
    childSku: {
      id: {
        key: string;
        value: string;
      };
      sku: string;
    };
  }>;
  upSellParentSku?: Array<{
    upSellParentSku: {
      id: string;
      sku: string;
    };
  }>;
  shopifyNativeVariant?: Array<{
    name: string;
    value: string;
  }>;
  shopifyCustomVariant?: Array<{
    name: string;
    value: string;
  }>;
  addonData?: Array<AddonData>;
}
export interface SingleProduct {
  id?: string;
  name?: string;
  category?: string;
  description: string | null;
  shopifyProductId?: string;
  metadata?: Record<string, any> | null;
  timestampkey?: string;
  sku?: string;
}
export interface AddonType {
  display: boolean;
  identifier: string;
  name: string;
  label: string;
  id: number;
  imageUrl: string;
  description: string;
  price: number;
  rush_key_value: string;
  show_international_rush: boolean;
  timestampkey?: string;
  quantity?: number;
  sku?: string;
}
export interface UniqueCharacteristic {
  description: string;
  image: string[];
}
export interface OrderItemType {
  product: SingleProduct;
  sku: SingleSKU;
  finalOptions: FinalOption[];
  identifier: AddonType[];
  petImages: string[];
  petType: string;
  petBreed: string;
  uniqueCharacteristics: UniqueCharacteristic[];
  pdp_extras: any[];
}
export interface FinalOption {
  name: string;
  variant: string;
  _unique_key: string;
}
export interface CustomizerFinalOption {
  display: boolean;
  available_us_only: boolean;
  name: string;
  detail: {
    heading: string;
    price: string;
    description: string;
    image: string;
    variant: Array<CustomizerFinalOptionVarient>;
  };
}
export interface CustomizerFinalOptionVarient {
  display: boolean;
  value: number;
  name?: string;
  label?: string;
}
