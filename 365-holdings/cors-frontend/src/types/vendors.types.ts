export type TVendor = {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  // Additional fields for comprehensive vendor information
  supportedSkus?: string[];
  costPerSku?: Record<string, number>;
  averageCycleTime?: number; // in days
  maximumDailyCapacity?: number;
  rushOrderSupport?: boolean;
  supportedManufacturingTypes?: string[]; // e.g., ['print', 'embroidery']
  shippingPreferences?: {
    usesShipStation?: boolean;
    preferredCarrier?: string;
  };
  supportedArtworkTypes?: string[]; // e.g., ['Line Art', 'Cutout', 'Knitted']
  apiIntegrationEnabled?: boolean;
  flatFileFormat?: string; // if no API
};
