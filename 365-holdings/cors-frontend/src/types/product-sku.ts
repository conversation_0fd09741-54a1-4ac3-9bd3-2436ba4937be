import { ImageInheritRule, ExceptionHandlingRule } from '@/constants/product.constants';
interface Vendor {
  id: string;
  name: string;
}

// Define the interface for the SKU relationship structure
interface SkuRelationship {
  id?: {
    key: string;
    value: string;
  };
  sku: string | null;
}

// Define separate interfaces for different relationship structures
export interface ProductSkuBase {
  // All properties from ProductSku except parentSku and childSku
  accountingWorkPaperMapping: boolean;
  eligibleVendors: any[];
  vendorAssignmentRule: string;
  productionCycleTime: string;
  maxCapacityPerDay: string;
  id: string;
  sku: string;
  isActive: boolean;
  hasRush: boolean;
  rushDays: number | null;
  requireImageUpload: boolean | null;
  requireCropping: boolean;
  croppingMethod: 'MANUAL' | 'CUTOUT_PRO' | null;
  cropType: 'FACE_CUTOUT' | 'BACKGROUND_REMOVAL' | null;
  croppingReviewRequired: boolean;
  artworkRequired: boolean;
  requireCustomerArtworkApproval: boolean;
  imageNamingConvention: string | null;
  requireTemplate: boolean;
  fileUploadFormat: '.bmp' | '.png' | '.jpeg' | null;
  workflowCategory: string | null;

  // SKU Mapping & Image Inheritance attributes
  canInheritImage: boolean;
  imageInheritRule: ImageInheritRule | null;
  imageInheritancePriority: number | null;
  canManualOverride: boolean;
  exceptionHandlingRule: ExceptionHandlingRule | null;
  customerFollowupEnabled: boolean;
  followupTiming: number | null;

  // Order Processing & Routing attributes
  routingMethod: 'OMS' | 'CORS' | null;
  requirePreprocessing: boolean;
  processingPriority: number | null;
  shipStationStore: string | null;

  // Shipping & Fulfillment attributes
  shippingWeight: number | null;
  productLength: number | null;
  productWidth: number | null;
  productHeight: number | null;
  shippingMethod: 'STANDARD' | 'EXPEDITED' | null;

  // Pricing & Accounting attributes
  chinaWOFEPrice: number | string | null;
  hasWorkPaper: boolean;

  // Upsell & Cross-Sell Configuration
  canUpSold: boolean;
  canCrossSold: boolean;

  // Manufacturing & Vendor Assignment attributes
  primaryVendor: Vendor | null;
  vendorSkuMapping: string | null;

  // Additional properties
  metadata?: Record<string, any>;

  // Existing relationships
  product?: {
    id: string;
    name: string;
    category: string;
    description: string | null;
  };
  products?: Array<{
    id?: string;
    name: string;
    category: string;
    description: string | null;
    shopifyProductId?: string;
    metadata?: Record<string, any> | null;
  }>;
  artworkType?: {
    id: string;
    name: string;
  } | null;
  upSellParentSku?: Array<{
    upSellParentSku: {
      id: string;
      sku: string;
    };
  }>;
  shopifyCustomVariant?: Array<{
    name: string;
    value: string;
  }>;
  shopifyNativeVariant?: Array<{
    name: string;
    value: string;
  }>;
  parentSkuIds?: string[] | Array<{
    id?: {
      key: string;
      value: string;
    };
  }>;
  childSkuIds?: string[] | Array<{
    id?: {
      key: string;
      value: string;
    };
  }>;
}

// Standard ProductSku with array relationships
export interface ProductSku extends ProductSkuBase {
  parentSku?: Array<{
    parentSku: SkuRelationship;
  }>;
  childSku?: Array<{
    childSku: SkuRelationship;
  }>;
}

// ProductSku with single parent relationship
export interface ProductSkuWithParentSku extends ProductSkuBase {
  parentSku: SkuRelationship;
}

// ProductSku with single child relationship
export interface ProductSkuWithChildSku extends ProductSkuBase {
  childSku: SkuRelationship;
}
