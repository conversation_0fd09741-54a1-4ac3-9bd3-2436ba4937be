'use client';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { AppAbility, defineAbilityFor, Role } from './ability';
import { useSession } from 'next-auth/react';
import LoadingView from '@/components/LoadingView';
import { useRouter } from 'nextjs-toploader/app';
import { usePathname } from 'next/navigation';
import apiClient from '@/utils/axios';

export const AbilityContext = createContext<AppAbility | null>(null);

export const AbilityProvider = ({ children }: { children: ReactNode }) => {
  const { status } = useSession();
  const [ability, setAbility] = useState<AppAbility | null>(null);
  const [roles, setRoles] = useState<Role[]>([]);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const fetchUserRoles = async () => {
      try {
        const response = await apiClient.get('/auth/me');
        const userRoles = response.data.roles;
        setRoles(userRoles);
        const userAbility = defineAbilityFor({ roles: userRoles });
        setAbility(userAbility);
      } catch (error) {
        console.error('Error fetching user roles:', error);
      }
    };

    if (status === 'authenticated') {
      fetchUserRoles();
    }
    if (status === 'unauthenticated' && pathname !== '/login') {
      router.push('/login');
    }
  }, [status, pathname]);

  if (!ability) {
    return (
      <AbilityContext.Provider value={ability}>
        <LoadingView />
      </AbilityContext.Provider>
    );
  }

  return <AbilityContext.Provider value={ability}>{children}</AbilityContext.Provider>;
};

export const useAbility = () => useContext(AbilityContext);
