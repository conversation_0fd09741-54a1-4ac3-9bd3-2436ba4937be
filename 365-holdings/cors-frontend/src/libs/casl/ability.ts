import { AbilityBuilder, MongoAbility, PureAbility } from '@casl/ability';

export type ActionsType = `${Actions}`;
export enum ActionsTarget {
  UserManagment = 'Users',
  RoleManagement = 'Roles',
  PIMS = 'Product Information Management System',
  ORDERS = 'Orders',
  LINE_ITEMS = 'Line Items',
  VENDORS = 'Vendors',
  QUEUES = 'Queues',
  CROP_REVIEW_QUEUE = 'Crop Review Queue',
  CROP_NEEDED_QUEUE = 'Crop Needed Queue',
  TEMPLATE_PLACEMENT_QUEUE = 'Template Placement Queue',
  ARTWORK_READY_QUEUE = 'Artwork Ready Queue',
  ARTWORK_REVISION_QUEUE = 'Artwork Revision',
  ALL = 'all',
}

export enum Actions {
  // Users
  EditUser = 'Update User',
  ViewUsers = 'View Users',
  CreateUsers = 'Create Users',
  ViewUserDetail = 'View User Detail',
  ViewUsersListingPage = 'View Users Listing Page',
  DeactivateUser = 'Deactivate User',
  ActivateUser = 'Activate User',

  // Roles
  ViewRoleDetail = 'View Role Detail',
  DeactivateRole = 'Deactivate Role',
  ActivateRole = 'Activate Role',
  ViewRolesListingPage = 'View Roles Listing Page',
  EditRole = 'Edit Role',
  CreateRole = 'Create Role',
  DeleteRole = 'Delete Role',

  // Product Information Management System (PIMS)
  ViewPIMSListingPage = 'View PIMS Listing Page',
  ActivateDeactivateSKU = 'Activate/Deactivate SKU',
  ViewDetailPage = 'View Detail Page',
  EditDetailPage = 'Edit Detail Page',
  AddArtwork = 'Add Artwork Type',

  // Orders
  ViewOrderListingPage = 'View Order Listing Page',
  CreateOrder = 'Create Order',
  EditOrderDetialPage = 'Order Edit/Detail Page',

  // Queues
  ViewQueuesListingPage = 'View Queue Listing Page',
  ManageQueuesSettings = 'Manage Queues Settings',

  // Crop Review Queue
  ViewCropReviewQueue = 'View Crop Review',
  StartStopCropReview = 'Start/Stop Crop Review',

  // Crop Needed Queue
  ViewCropNeededQueue = 'View Crop Needed',
  StartStopCropNeeded = 'Start/Stop Crop Needed',

  // Template Placement Queue
  ViewTemplatePlacementQueue = 'View Template Placement',
  StartStopTemplatePlacement = 'Start/Stop Template Placement',

  // Artwork Ready Queue
  ViewArtworkReadyQueue = 'View Artwork Ready',
  StartStopArtworkReady = 'Start/Stop Artwork Ready',

  // Artwork Revision Queue
  ViewArtworkRevisionQueue = 'View Artwork Revision',
  StartStopArtworkRevision = 'Start/Stop Artwork Revision',
  AddArtworkRevisionRequest = 'Add Request Artwork Revision',
  // Line Items
  EditLineItem = 'Edit Line Item',
  CancelLineItem = 'Cancel Line Item',
  CreateLineItemRemake = 'Create Line Item Remake',
  FlagLineItem = 'Flag Line Item',
  UploadLineItemCompletedArtfile = 'Upload Line Item Completed Artfile',
  InitiateLineItemCustomerContactRequest = 'Initiate Line Item Customer Contact Request',
  AddLineItemRevisionRequest = 'Add Line Item Revision Request',

  // Vendors
  ViewVendorsListingPage = 'View Vendors Listing Page',
  ViewVendorDetail = 'View Vendor Detail',
  CreateVendor = 'Create Vendor',
  EditVendor = 'Edit Vendor',
  ActivateVendor = 'Activate Vendor',
  DeactivateVendor = 'Deactivate Vendor',
  DeleteVendor = 'Delete Vendor',

  Manage = 'manage',
}

export type AppAbility = MongoAbility<[ActionsType, any]>;

export type RolePermission = {
  actions: string[];
  resource: string;
};

export type Role = {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  name: string;
  status: 'active' | 'inactive';
  rolePermissions: RolePermission[];
};

interface RolesType {
  actions: string[];
  key: any;
}

export function defineAbilityFor({ roles }: { roles: Role[] }) {
  const { can, cannot, build } = new AbilityBuilder<AppAbility>(PureAbility as any);
  roles?.forEach(role => {
    if (role.name === 'Owner') {
      can(Actions.Manage, ActionsTarget.ALL);
    } else {
      role?.rolePermissions?.map((val: RolePermission) => {
        val.actions.forEach(action => {
          can(action as ActionsType, val.resource);
        });
      });
    }
  });

  return build({
    detectSubjectType: item => item?.type || 'all',
  });
}
