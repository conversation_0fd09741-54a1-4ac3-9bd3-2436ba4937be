import Cred<PERSON><PERSON>rovider from 'next-auth/providers/credentials';
import type { NextAuthOptions } from 'next-auth';
import { jwtDecode } from 'jwt-decode';
import apiClient from '@/utils/axios';
import { Role } from './casl/ability';

interface DecodedToken {
  exp: number;
  [key: string]: any;
}

let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET || process.env.AUTH_SECRET,

  providers: [
    CredentialProvider({
      name: 'Credentials',
      type: 'credentials',

      credentials: {},
      async authorize(credentials) {
        // Type guard for credentials
        if (credentials && (credentials as any).accessToken && (credentials as any).refreshToken) {
          // Token refresh flow
          const tokenPayload = jwtDecode<DecodedToken>((credentials as any).accessToken);
          return {
            id: tokenPayload.sub,
            name: tokenPayload.name || tokenPayload.email,
            email: tokenPayload.email,
            token: (credentials as any).accessToken,
            refreshToken: (credentials as any).refreshToken,
          };
        }

        // Normal login flow
        const { email, password } = credentials as { email: string; password: string };
        try {
          const data = await apiClient.post('/auth/login', { email, password });

          if (data?.data.access_token && data?.data.refresh_token) {
            const tokenPayload = jwtDecode<DecodedToken>(data.data.access_token);

            // Store tokens in localStorage for immediate access
            if (typeof window !== 'undefined') {
              localStorage.setItem('auth-token', data.data.access_token);
              localStorage.setItem('refresh-token', data.data.refresh_token);
            }

            return {
              id: tokenPayload.sub,
              name: tokenPayload.name || tokenPayload.email,
              email: tokenPayload.email,
              token: data.data.access_token,
              refreshToken: data.data.refresh_token,
            };
          }

          return null;
        } catch (e: any) {
          console.error('Auth error:', e);
          if (e.response && e.response.data) {
            let errorMessage = 'Authentication failed';

            if (e.response.data.message) {
              if (Array.isArray(e.response.data.message)) {
                errorMessage = e.response.data.message[0];
              } else if (typeof e.response.data.message === 'string') {
                errorMessage = e.response.data.message;
              }
            } else if (e.response.data.error) {
              errorMessage = e.response.data.error;
            }
            throw new Error(JSON.stringify({ message: [errorMessage] }));
          }
          throw new Error(JSON.stringify({ message: [e.message || 'Authentication failed'] }));
        }
      },
    }),
  ],

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60,
  },

  pages: {
    signIn: '/login',
    signOut: '/login',
  },

  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (user && account) {
        const decodedToken = jwtDecode<DecodedToken>((user as any).token);
        return {
          ...token,
          id: user.id,
          email: user.email,
          token: (user as any).token,
          refreshToken: (user as any).refreshToken,
          accessTokenExpires: decodedToken.exp * 1000,
        };
      }

      return token;
    },

    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        // Ensure session.user exists
        if (!session.user) {
          session.user = {} as any;
        }

        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.token = token.token as string;
        session.user.refreshToken = token.refreshToken as string;
      }

      // Pass error to session if token refresh failed
      if (token.error) {
        (session as any).error = token.error;
        console.error('Session callback - token error:', token.error);
      }

      return session;
    },
  },
};
