import { authOptions } from '@/libs/auth';
import axios from 'axios';
import { getServerSession } from 'next-auth/next';
import { getSession, signOut, signIn } from 'next-auth/react';
import { toast } from 'react-toastify';

const API_URL = process.env.NEXT_PUBLIC_API_URL;

// Axios without interceptors for internal use
const baseAxios = axios.create({
  baseURL: API_URL,
  withCredentials: false,
});

// Logout client-side
const logout = async () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth-token');
    localStorage.removeItem('refresh-token');
    document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    document.cookie = 'next-auth.session-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    await signOut({ redirect: true });
  }
};

// Refresh token call
const refreshToken = async (refreshToken: string) => {
  try {
    const response = await baseAxios.post('/auth/refresh', {
      refresh: refreshToken,
    });
    return response?.data;
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};

// Get session (client/server aware)
const getActiveSession = async () => {
  if (typeof window !== 'undefined') {
    return await getSession();
  } else {
    return await getServerSession(authOptions);
  }
};

// Server-side API client
export const serverApiClient = axios.create({
  baseURL: API_URL,
  withCredentials: false,
});

serverApiClient.interceptors.request.use(
  async config => {

    const session = await getServerSession(authOptions);
    // Only add session token if no custom Authorization header is provided
    if (session?.user?.token && !config.headers['Authorization']&& !config.headers['api-token']) {
      config.headers['Authorization'] = `Bearer ${session.user.token}`;
    }
    config.headers['ngrok-skip-browser-warning'] = `69420`;
    // @ts-ignore
    return config;
  },
  error => Promise.reject(error),
);

serverApiClient.interceptors.response.use(
  response => response,
  async err => {
    const originalRequest = err.config;

    const isRefreshRequest = originalRequest?.url?.includes('/auth/refresh');

    if (err.response?.status === 401 && !originalRequest._retry && !isRefreshRequest) {
      originalRequest._retry = true;
      const session = await getServerSession(authOptions);

      try {
        const refreshTokenVal = session?.user?.refreshToken;

        if (refreshTokenVal) {
          const refreshed = await refreshToken(refreshTokenVal);
          if (refreshed?.access_token) {
            // Update request headers with new token
            originalRequest.headers['Authorization'] = `Bearer ${refreshed.access_token}`;

            // Retry the original request with new token
            return clientApiClient(originalRequest);
          }
        }

        return Promise.reject(err); // Can't refresh
      } catch (refreshErr) {
        console.error('Server token refresh failed:', refreshErr);
        return Promise.reject(refreshErr);
      }
    }

    if (err.response?.status === 403) {
      console.error('You are not allowed to perform this action');
    }

    return Promise.reject(err);
  },
);

// Client-side API client
export const clientApiClient = axios.create({
  baseURL: API_URL,
  withCredentials: false,
});

clientApiClient.interceptors.request.use(
  async config => {
    const session = await getActiveSession();

    // Only add session token if no custom Authorization header is provided
    if (session?.user?.token  && !config.headers['Authorization']&& !config.headers['api-token']) {
      config.headers['Authorization'] = `Bearer ${session.user.token}`;
    }
    config.headers['ngrok-skip-browser-warning'] = `69420`;
    return config;
  },
  error => Promise.reject(error),
);

clientApiClient.interceptors.response.use(
  response => response,
  async err => {
    const originalRequest = err.config;
    const isRefreshRequest = originalRequest?.url?.includes('/auth/refresh');

    if (err.response?.status === 401 && !originalRequest._retry && !isRefreshRequest) {
      originalRequest._retry = true;

      try {
        const session = await getActiveSession();
        const refreshTokenVal = session?.user?.refreshToken;

        if (refreshTokenVal) {
          const refreshed = await refreshToken(refreshTokenVal);
          if (refreshed?.access_token) {
            const signInResult = await signIn('credentials', {
              redirect: false,
              accessToken: refreshed.access_token,
              refreshToken: refreshed.refresh_token || refreshTokenVal,
            });

            if (signInResult?.ok) {
              originalRequest.headers['Authorization'] = `Bearer ${refreshed.access_token}`;
              return clientApiClient(originalRequest);
            }
          }
        }

        await logout();
        return Promise.reject(err);
      } catch (refreshErr) {
        console.error('Client token refresh failed:', refreshErr);
        if (typeof window !== 'undefined') {
          toast.error('Session expired. Please log in again.');
        }
        await logout();
        return Promise.reject(refreshErr);
      }
    }

    if (err.response?.status === 403) {
      if (typeof window !== 'undefined') {
        toast.error('You are not allowed to perform this action');
      }
    }

    return Promise.reject(err);
  },
);

// Smart switch: use client or server API based on environment
const apiClient = typeof window !== 'undefined' ? clientApiClient : serverApiClient;

export default apiClient;
