import { Actions, ActionsTarget } from '@/libs/casl/ability';
import { useAbility } from '@/libs/casl/AbilityContext';
import {
  LineItemAttachmentProps,
  OrderDetailsTabProps,
  OrderLineItemProps,
} from '@/types/orderDetailsTab.types';

export const isItemActionable = (item: { status?: string }, includeCancelled: boolean = false) => {
  const nonActionableStatuses = ['shipped', 'ready for vendors', 'cancelled'];
  const statuses = includeCancelled
    ? [...nonActionableStatuses, 'cancelled']
    : nonActionableStatuses;
  if (!item.status) return true;
  const itemStatus = item.status.toLowerCase();
  return !statuses.includes(itemStatus);
};

// Modify these functions to call the hook inside them
export const canEditLineItem = () => {
  const ability = useAbility();
  return ability?.can(Actions.EditLineItem, ActionsTarget.LINE_ITEMS);
};

export const canCancelLineItem = () => {
  const ability = useAbility();
  return ability?.can(Actions.CancelLineItem, ActionsTarget.LINE_ITEMS);
};

export const canCreateRemake = () => {
  const ability = useAbility();
  return ability?.can(Actions.CreateLineItemRemake, ActionsTarget.LINE_ITEMS);
};

export const canFlagLineItem = () => {
  const ability = useAbility();
  return ability?.can(Actions.FlagLineItem, ActionsTarget.LINE_ITEMS);
};

export const canInitiateCustomerContact = () => {
  const ability = useAbility();
  return ability?.can(Actions.InitiateLineItemCustomerContactRequest, ActionsTarget.LINE_ITEMS);
};
export const isItemEditable = (item: OrderDetailsTabProps['orderData']['lineItems'][0]) =>
  isItemActionable(item) && canEditLineItem() && !item.isRemake;
export const isItemCancellable = (item: OrderDetailsTabProps['orderData']['lineItems'][0]) =>
  isItemActionable(item, true) && canCancelLineItem() && !item.isRemake;
export const isItemRemakeable = (item: OrderDetailsTabProps['orderData']['lineItems'][0]) =>
  isItemActionable(item) && canCreateRemake() && !item.isRemake;

const getFormattedImages = (attachments: LineItemAttachmentProps[] | undefined) => {
  if (!attachments) return [];
  return attachments
    .filter((attachment: LineItemAttachmentProps) => !attachment?.completedArtFileUrl)
    .map((attachment: LineItemAttachmentProps) => {
      return {
        url: attachment?.url,
        baseName: attachment?.url?.split('/').pop(),
        cutoutProImageUrl: attachment?.cutoutProImageUrl || '',
        cutoutProImageName: attachment?.cutoutProImageUrl?.split('/').pop() || '',
      };
    });
};
const getCompletedArtFiles = (
  attachments: LineItemAttachmentProps[] | undefined,
  isCustomerReview: boolean = false,
) => {
  if (!attachments) return [];
  return attachments
    .filter((attachment: LineItemAttachmentProps) => attachment?.completedArtFileUrl)
    .map((attachment: LineItemAttachmentProps) => {
      return {
        url: attachment?.completedArtFileUrl,
        baseName: attachment?.completedArtFileUrl?.split('/').pop(),
        status: attachment?.status,
      };
    });
};

export const checkWorkflowCategory = (item: OrderLineItemProps) => {
  switch (item?.productSku?.workflowCategory) {
    case 'Crop Image Only':
      return {
        images: getFormattedImages(item?.attachments),
        artFiles: getCompletedArtFiles(item?.attachments),
        showArtfile: false,
        showCustomerImages: true,
        showCutoutProImage: true,
      };
    case 'Art Only':
      return {
        images: getFormattedImages(item?.attachments),
        artFiles: getCompletedArtFiles(item?.attachments),
        showArtfile: true,
        showCustomerImages: true,
        showCutoutProImage: false,
      };
    case 'Customer Image Only':
      return {
        images: getFormattedImages(item?.attachments),
        artFiles: getCompletedArtFiles(item?.attachments),
        showArtfile: false,
        showCustomerImages: true,
        showCutoutProImage: false,
      };
    case 'Crop Image+Template Placement':
      return {
        images: getFormattedImages(item?.attachments),
        artFiles: getCompletedArtFiles(item?.attachments),
        showArtfile: true,
        showCustomerImages: true,
        showCutoutProImage: true,
      };
    case 'Art+Template Placement':
      return {
        images: getFormattedImages(item?.attachments),
        artFiles: getCompletedArtFiles(item?.attachments),
        showArtfile: true,
        showCustomerImages: true,
        showCutoutProImage: false,
      };
    case 'Art+Template Placement+Approval':
      return {
        images: getFormattedImages(item?.attachments),
        artFiles: getCompletedArtFiles(item?.attachments),
        showArtfile: true,
        showCustomerImages: true,
        showCutoutProImage: false,
      };
    case 'Plush':
      return {
        images: getFormattedImages(item?.attachments),
        artFiles: getCompletedArtFiles(item?.attachments),
        showArtfile: false,
        showCustomerImages: true,
        showCutoutProImage: false,
      };
    default:
      return {
        images: getFormattedImages(item?.attachments),
        artFiles: [],
        showArtfile: false,
        showCustomerImages: true,
        showCutoutProImage: false,
      };
  }
};
