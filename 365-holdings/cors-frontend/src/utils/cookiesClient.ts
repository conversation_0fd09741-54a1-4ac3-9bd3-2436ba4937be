export const allCookies = document.cookie; // e.g., "token=abc123; theme=dark"

// Get a specific cookie value
export const setCookie = (
  name: string,
  value: string,
  options: {
    days?: number;
    path?: string;
    secure?: boolean;
    sameSite?: 'Lax' | 'Strict' | 'None';
  } = {},
) => {
  const { days = 1, path = '/', secure = true, sameSite = 'Lax' } = options;

  const expires = new Date(Date.now() + days * 864e5).toUTCString();
  document.cookie = `${name}=${encodeURIComponent(value)}; expires=${expires}; path=${path};${secure ? ' secure;' : ''} SameSite=${sameSite}`;
};

export function getCookie(name: string): string | null{
  const value = document.cookie.match('(^|;)\\s*' + name + '\\s*=\\s*([^;]+)');
  return value ? decodeURIComponent(value.pop() as string) : null;
};

export function removeCookie(name: string, path = '/'){
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path};`;
};
