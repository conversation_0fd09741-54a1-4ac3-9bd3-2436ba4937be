/**
 * Utility functions for generating and handling order status URLs
 */

/**
 * Generates the public order status URL for a given order
 * @param shopifyOrderNumber - The Shopify order number
 * @param customerEmail - The customer's email address
 * @returns The complete order status URL or empty string if required data is missing
 */
export function generateOrderStatusUrl(
  shopifyOrderNumber?: string,
  customerEmail?: string
): string {
  if (!shopifyOrderNumber || !customerEmail) {
    return '';
  }

  const baseUrl =
    typeof window !== 'undefined'
      ? window.location.origin
      : process.env.NEXT_PUBLIC_FRONTEND_URL ||
        (process.env.NODE_ENV === 'production'
          ? 'https://cors-dev.cuddleclones.com'
          : 'http://localhost:3000');

  return `${baseUrl}/order-status?shopifyOrderNumber=${shopifyOrderNumber}&customerEmail=${encodeURIComponent(customerEmail)}`;
}

/**
 * Checks if the required data is available to generate an order status URL
 * @param shopifyOrderNumber - The Shopify order number
 * @param customerEmail - The customer's email address
 * @returns True if both required fields are available
 */
export function canGenerateOrderStatusUrl(
  shopifyOrderNumber?: string,
  customerEmail?: string
): boolean {
  return Boolean(shopifyOrderNumber && customerEmail);
}
