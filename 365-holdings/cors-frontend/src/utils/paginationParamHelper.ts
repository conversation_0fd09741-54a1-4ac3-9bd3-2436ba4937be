export interface PaginationQueryParams {
  page?: number;
  limit?: number;
  q?: string;
  fq?: string;
}

export function buildPaginationParams({ page, limit, q, fq }: PaginationQueryParams) {
    const params = new URLSearchParams();
    if (page) params.append('page', page.toString());
    if (limit) params.append('limit', limit.toString());
    if (q) params.append('q', q);
    if (fq) params.append('fq', fq);
    return params;
}

export function extractPaginationParams(searchParams: URLSearchParams): PaginationQueryParams {
  return {
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 25,
    q: searchParams.get('q') || undefined,
    fq: searchParams.get('fq') || undefined,
  };
}