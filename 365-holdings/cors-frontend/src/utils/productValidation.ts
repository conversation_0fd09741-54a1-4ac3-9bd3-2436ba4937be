import { ExceptionHandlingRule } from "@/constants/product.constants";
import { ProductSku } from "@/types/product-sku";

/**
 * Validates SKU mapping and image inheritance fields
 * @param productSku The product SKU to validate
 * @param isEdit Whether the form is in edit mode
 * @returns Object containing validation errors
 */
export const validateSkuMapping = (
  productSku: ProductSku,
  isEdit: boolean = true
): Record<string, string> => {
  if (!isEdit) return {};
  
  const errors: Record<string, string> = {};

  // Only validate inheritance-related fields if canInheritImage is true
  if (productSku.canInheritImage) {
    // Validate parent SKU if canInheritImage is true
    if (!productSku.parentSku?.[0]?.parentSku?.sku) {
      errors.parentSku =
        "Parent SKU is required when image inheritance is enabled";
    }

    // Validate inheritance rule if canInheritImage is true
    if (!productSku.imageInheritRule) {
      errors.imageInheritRule = "Image inheritance rule is required";
    }

    // Validate priority if parent SKU is specified
    if (
      productSku.parentSku?.[0]?.parentSku?.sku &&
      (productSku.imageInheritancePriority === null ||
        productSku.imageInheritancePriority === undefined)
    ) {
      errors.imageInheritancePriority =
        "Priority is required when parent SKU is specified";
    }

    // Validate exception handling rule if canInheritImage is true
    if (!productSku.exceptionHandlingRule) {
      errors.exceptionHandlingRule = "Exception handling rule is required";
    }
  }

  return errors;
};

/**
 * Validates numeric fields in a product SKU
 * @param productSku The product SKU to validate
 * @param fields Array of field names to validate
 * @returns Object containing validation errors
 */
export const validateNumericFields = (
  productSku: ProductSku,
  fields: (keyof ProductSku)[] = []
): Record<string, string> => {
  const errors: Record<string, string> = {};

  // Only validate the specific fields passed in
  fields.forEach((field) => {
    const value = productSku[field];
    if (value !== undefined && value !== null && value !== "") {
      const numValue = Number(value);
      if (isNaN(numValue) || numValue <= 0) {
        errors[field as string] = "Please enter a positive number";
      }
    }
  });

  return errors;
};

/**
 * Validates all SKU mapping fields including numeric values
 * @param productSku The product SKU to validate
 * @param isEdit Whether the form is in edit mode
 * @returns Object containing all validation errors
 */
export const validateSkuMappingComplete = (
  productSku: ProductSku,
  isEdit: boolean = true
): Record<string, string> => {
  if (!isEdit) return {};
  
  // First get basic validation errors
  const basicErrors = validateSkuMapping(productSku, isEdit);
  
  // Only validate numeric fields if canInheritImage is true
  if (!productSku.canInheritImage) {
    return basicErrors;
  }
  
  // Add numeric validation errors only for relevant fields
  const numericFields: (keyof ProductSku)[] = [];
  
  // Only validate imageInheritancePriority if parent SKU exists
  if (productSku.parentSku?.[0]?.parentSku?.sku) {
    numericFields.push("imageInheritancePriority");
  }
  
  // Get numeric validation errors only for relevant fields
  const numericErrors = validateNumericFields(productSku, numericFields);
  
  // Combine errors
  return {
    ...basicErrors,
    ...numericErrors
  };
};

/**
 * Maps field names to their respective tabs
 */
export const fieldToTabMap: Record<string, string> = {
  // General tab fields
  sku: "General",
  "products[0].name": "General",
  "products[0].description": "General",
  isActive: "General",
  
  // Image Processing tab fields
  requireCropping: "Image Processing",
  croppingMethod: "Image Processing",
  artworkType: "Image Processing",
  imageNamingConvention: "Image Processing",
  
  // SKU Mapping tab fields
  canInheritImage: "SKU Mapping",
  parentSku: "SKU Mapping",
  imageInheritRule: "SKU Mapping",
  imageInheritancePriority: "SKU Mapping",
  exceptionHandlingRule: "SKU Mapping",
  customerFollowupEnabled: "SKU Mapping",
  followupTiming: "SKU Mapping",
  
  // Manufacturing tab fields
  manufacturingMethod: "Manufacturing",
  
  // Order Processing tab fields
  processingTime: "Order Processing",
  
  // Shipping tab fields
  shippingMethod: "Shipping",
  
  // Pricing tab fields
  basePrice: "Pricing",
  
  // Upsell & Cross-Sell tab fields
  relatedProducts: "Upsell & Cross-Sell",
  
  // Notes tab fields
  notes: "Notes"
};

/**
 * Maps field names to more readable versions
 */
export const fieldNameMap: Record<string, string> = {
  sku: "SKU",
  "products[0].name": "Product Name",
  "products[0].description": "Product Description",
  isActive: "Status",
  requireCropping: "Require Cropping",
  croppingMethod: "Cropping Method",
  artworkType: "Artwork Type",
  imageNamingConvention: "Image Naming Convention",
  canInheritImage: "Can Inherit Image",
  parentSku: "Parent SKU",
  imageInheritRule: "Image Inheritance Rule",
  imageInheritancePriority: "Priority for Image Inheritance",
  exceptionHandlingRule: "Exception Handling Rule",
  customerFollowupEnabled: "Customer Follow-up",
  followupTiming: "Follow-Up Timing",
  // Add more field mappings as needed
};

/**
 * Gets formatted error information including tab and readable field name
 * @param errors The error object
 * @returns Array of formatted error objects with tab, field, and message
 */
export const getFormattedErrors = (errors: Record<string, string>) => {
  return Object.entries(errors).map(([field, message]) => {
    return {
      field,
      readableField: fieldNameMap[field] || field,
      tab: fieldToTabMap[field] || "Unknown",
      message
    };
  });
};
