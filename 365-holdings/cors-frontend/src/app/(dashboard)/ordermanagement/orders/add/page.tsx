import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import AddManualOrderForm from '@/views/order-management/orders/add/AddManualOrderForm';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';

const OrdersAddManualData = async () => {
  try {
    return <AddManualOrderForm />;
  } catch (error) {
    console.error('Failed to fetch roles:', error);
    return <DataError />;
  }
};

const UserAddPage = () => {
  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary action={Actions.CreateOrder} actionTarget={ActionsTarget.ORDERS}>
        <OrdersAddManualData />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default UserAddPage;
