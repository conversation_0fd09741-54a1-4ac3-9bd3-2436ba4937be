import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import OrderView from '@/views/order-management/orders/view';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';

const OrderViewPage = async ({ params }: { params: Promise<{ id: string }> }) => {
  const resolvedParams = await params;
  const id = resolvedParams.id;

  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary action={Actions.EditOrderDetialPage} actionTarget={ActionsTarget.ORDERS}>
        <OrderView id={id} />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default OrderViewPage;
