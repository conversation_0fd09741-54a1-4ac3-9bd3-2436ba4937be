import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import OrdersWrapper from '@/views/order-management/orders';
import { fetchOrders } from '@/actions/orders';
import { PaginationQueryParams } from '@/utils/paginationParamHelper';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';

const OrdersData = async ({ page, limit }: Pick<PaginationQueryParams, 'page' | 'limit'>) => {
  try {
    const [unflaggedData, flaggedData] = await Promise.all([
      fetchOrders({ page, limit, fq: 'flagged:eq:false' }),
      fetchOrders({ page, limit, fq: 'flagged:eq:true' }),
    ]);

    return (
      <OrdersWrapper
        data={{
          unflagged: unflaggedData,
          flagged: flaggedData,
        }}
        page={page}
        limit={limit}
      />
    );
  } catch (error) {
    return <DataError />;
  }
};

const OrderIntaketPage = async (Props: { searchParams: any }) => {
  const params = await Props.searchParams;
  const page = params.page;
  const limit = params.limit;

  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary
        action={Actions.ViewOrderListingPage}
        actionTarget={ActionsTarget.ORDERS}
      >
        <OrdersData page={Number(page)} limit={Number(limit)} />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default OrderIntaketPage;
