import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import OrderManagementWrapper from '@/views/order-management';
import { fetchFilteredProductsSkusAdvanced } from '@/actions/products';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';

const ProductsData = async ({ page, limit }: { page: number; limit: number }) => {
  try {
    const data = await fetchFilteredProductsSkusAdvanced({
      filters: [],
      page,
      limit,
    });

    return <OrderManagementWrapper page={page} limit={limit} data={data} />;
  } catch (error) {
    console.error('Failed to fetch data:', error);
    return <DataError />;
  }
};

const OrderManagementPage = async (Props: any) => {
  const { searchParams } = Props;
  const { page = 1, limit = 25 } = await searchParams;

  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary action={Actions.ViewPIMSListingPage} actionTarget={ActionsTarget.PIMS}>
        <ProductsData page={Number(page)} limit={Number(limit)} />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default OrderManagementPage;
