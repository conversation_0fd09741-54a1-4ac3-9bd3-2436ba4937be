import { Suspense } from 'react';
import apiClient from '@/utils/axios';
import LoadingView from '@/components/LoadingView';
import ProductSkuEdit from '@/views/order-management/products/edit';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';

const formatProductData = (data: any) => {
  // Make a deep copy to avoid modifying the original
  const formattedData = { ...data };
  if (
    Array.isArray(formattedData.parentSku) &&
    formattedData.parentSku.length > 0 &&
    typeof formattedData.parentSku[0] === 'string'
  ) {
    formattedData.parentSku = formattedData.parentSku.map((sku: string) => ({
      parentSku: {
        sku: sku,
        id: { key: sku, value: sku },
      },
    }));
  }

  if (
    Array.isArray(formattedData.childSku) &&
    formattedData.childSku.length > 0 &&
    typeof formattedData.childSku[0] === 'string'
  ) {
    formattedData.childSku = formattedData.childSku.map((sku: string) => ({
      childSku: {
        sku: sku,
        id: { key: sku, value: sku },
      },
    }));
  }

  return formattedData;
};

const ProductViewData = async ({ params }: { params: { id: string; isEdit: boolean } }) => {
  try {
    // Fetch product data
    const productResponse = await apiClient.get(`/product-sku/${params.id}`);

    // Format the data
    const formattedData = formatProductData(productResponse.data);

    // Convert numeric fields to numbers
    const transformedData = {
      ...formattedData,
      shippingWeight: formattedData.shippingWeight ? Number(formattedData.shippingWeight) : null,
      productLength: formattedData.productLength ? Number(formattedData.productLength) : null,
      productWidth: formattedData.productWidth ? Number(formattedData.productWidth) : null,
      productHeight: formattedData.productHeight ? Number(formattedData.productHeight) : null,
    };

    return <ProductSkuEdit productData={transformedData} isEdit={params.isEdit} />;
  } catch (error) {
    console.error('Failed to fetch product data:', error);
    return <DataError />;
  }
};

const ProductViewPage = async ({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ isEdit?: boolean; mode?: string; Edit?: string }>;
}) => {
  const resolvedParams = await params;
  const resolveSearchParams = await searchParams;

  // Determine if it's edit mode based on all possible edit parameters
  const isEditMode =
    resolveSearchParams.isEdit === true ||
    resolveSearchParams.mode === 'edit' ||
    resolveSearchParams.Edit === 'true';
  const isViewMode = resolveSearchParams.mode === 'view';

  // Determine the appropriate permission based on whether it's edit mode or view mode
  const requiredAction = isEditMode ? Actions.EditDetailPage : Actions.ViewDetailPage;

  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary action={requiredAction} actionTarget={ActionsTarget.PIMS}>
        <ProductViewData params={{ id: resolvedParams.id, isEdit: isEditMode }} />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default ProductViewPage;
