import LoadingView from '@/components/LoadingView';
import { PageProps } from '../../../../../.next/types/app/(dashboard)/ordermanagement/products/page';
import QueuesWrapper from '@/views/order-management/queues';
import { Suspense } from 'react';
import { apiCall } from '@/actions/common.actions';
import { TabQueues } from '@/types/queues.types';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';

export const dynamic = 'force-dynamic';

const QueuesManagementData = async () => {
  try {
    const response: TabQueues[] = await apiCall('get', '/workflow-queues/all');
    return <QueuesWrapper allQueues={response || []} />;
  } catch (error) {
    console.error('Failed to fetch data:', error);
    return <DataError />;
  }
};

const QueuesManagementPage = async (Props: PageProps) => {
  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary
        action={Actions.ViewQueuesListingPage}
        actionTarget={ActionsTarget.QUEUES}
      >
        <QueuesManagementData />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default QueuesManagementPage;
