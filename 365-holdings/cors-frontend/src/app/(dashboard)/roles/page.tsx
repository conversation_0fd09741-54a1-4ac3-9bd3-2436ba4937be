import { Suspense } from 'react';
import apiClient from '@/utils/axios';
import Roles from '@/views/roles';
import LoadingView from '@/components/LoadingView';
import { PageProps } from '../../../../.next/types/app/(dashboard)/roles/page';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';

const RolesData = async ({
  page,
  limit,
  q,
  fq,
  sort,
}: {
  page: number;
  limit: number;
  q: string;
  fq: string;
  sort?: string;
}) => {
  try {
    const response = await apiClient.get(
      `/roles?page=${page}&limit=${limit}${q ? `&q=${q}` : ''}${fq ? `&fq=${fq}` : ''}${sort ? `&sort=${sort}` : ''}`,
    );
    return <Roles userData={response.data.data} count={response.data.count} />;
  } catch (error) {
    console.error('Failed to fetch role:', error);
    return <DataError />;
  }
};

const RolesApp = async (Props: PageProps) => {
  const { searchParams } = Props;
  const { page = 1, limit = 25, q, fq, sort } = await searchParams;
  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary
        action={Actions.ViewRolesListingPage}
        actionTarget={ActionsTarget.RoleManagement}
      >
        <RolesData sort={sort} q={q} fq={fq} page={page} limit={limit} />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default RolesApp;
