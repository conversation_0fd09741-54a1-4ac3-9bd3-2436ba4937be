import { Suspense } from 'react';
import RoleAddForm from '@/views/roles/RoleAddForm';
import LoadingView from '@/components/LoadingView';
import apiClient from '@/utils/axios';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';

export const dynamic = 'force-dynamic';

const RoleAddData = async () => {
  try {
    const response = await apiClient.get('/roles/permissions');
    return <RoleAddForm permissions={response.data} />;
  } catch (error) {
    console.error('Failed to fetch permissions:', error);
    return <DataError />;
  }
};

const RoleAddPage = () => {
  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary action={Actions.CreateRole} actionTarget={ActionsTarget.RoleManagement}>
        <RoleAddData />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default RoleAddPage;
