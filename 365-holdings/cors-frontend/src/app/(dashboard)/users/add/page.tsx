import { Suspense } from 'react';
import apiClient from '@/utils/axios';
import UserAddForm from '@/views/users/add/UserAddForm';
import LoadingView from '@/components/LoadingView';
import { RolesData } from '@/types/roleTypes';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';
export const dynamic = 'force-dynamic';

const UserAddData = async () => {
  try {
    const response = await apiClient.get('/roles');
    const rolesData = response.data.data.filter((role: RolesData) => role.name !== 'Owner') || [];
    return <UserAddForm roles={rolesData} />;
  } catch (error) {
    console.error('Failed to fetch roles:', error);
    return <DataError />;
  }
};

const UserAddPage = () => {
  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary action={Actions.CreateUsers} actionTarget={ActionsTarget.UserManagment}>
        <UserAddData />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default UserAddPage;
