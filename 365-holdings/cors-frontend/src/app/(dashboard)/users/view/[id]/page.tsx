import { Suspense } from 'react';
import UserViewForm from '@/views/users/view/UserViewForm';
import apiClient from '@/utils/axios';
import LoadingView from '@/components/LoadingView';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';

async function UserView({ id }: { id: string }) {
  try {
    const userResponse = await apiClient.get(`/users/${id}`);

    return <UserViewForm userData={userResponse.data} roleData={userResponse?.data.roles[0]} />;
  } catch (error) {
    console.error('Failed to fetch data:', error);
    return <DataError />;
  }
}

export default async function UserViewPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const id = resolvedParams.id;

  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary
        action={Actions.ViewUserDetail}
        actionTarget={ActionsTarget.UserManagment}
      >
        <UserView id={id} />
      </PermissionsBoundary>
    </Suspense>
  );
}
