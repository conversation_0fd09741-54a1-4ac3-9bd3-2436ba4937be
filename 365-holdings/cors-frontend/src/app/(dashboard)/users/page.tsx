import { Suspense } from 'react';
import UserList from '@/views/users/list';
import apiClient from '@/utils/axios';
import LoadingView from '@/components/LoadingView';
import { PageProps } from '../../../../.next/types/app/(dashboard)/users/page';
import PermissionsBoundary from '@/@layouts/PermissionsBoundary';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import DataError from '@/components/DataError';

const UserData = async ({
  page,
  limit,
  q,
  fq,
  sort,
}: {
  page: number;
  limit: number;
  q: string;
  fq: string;
  sort?: string;
}) => {
  try {
    const response = await apiClient.get(
      `/users?page=${page}&limit=${limit}${q ? `&q=${q}` : ''}${fq ? `&fq=${fq}` : ''}${sort ? `&sort=${sort}` : ''}`,
    );
    return <UserList userData={response.data?.data} count={response.data.count} />;
  } catch (error) {
    console.error('Failed to fetch users:', error);
    return <DataError />;
  }
};

const UserListApp = async (Props: PageProps) => {
  const { searchParams } = Props;
  const { page = 1, limit = 25, q, fq, sort } = await searchParams;

  return (
    <Suspense fallback={<LoadingView />}>
      <PermissionsBoundary
        action={Actions.ViewUsersListingPage}
        actionTarget={ActionsTarget.UserManagment}
      >
        <UserData sort={sort} fq={fq} page={page} limit={limit} q={q} />
      </PermissionsBoundary>
    </Suspense>
  );
};

export default UserListApp;
