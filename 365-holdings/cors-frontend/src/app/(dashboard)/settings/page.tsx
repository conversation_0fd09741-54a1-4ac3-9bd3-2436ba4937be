import { Suspense } from 'react';

import LoadingView from '@/components/LoadingView';
import SettingsView from '@/views/settings';
import DataError from '@/components/DataError';

const SettingsData = async () => {
  try {
    return <SettingsView />;
  } catch (error) {
    console.error('Failed to fetch role:', error);
    return <DataError />;
  }
};

const Settings = async () => {
  return (
    <Suspense fallback={<LoadingView />}>
      <SettingsData />
    </Suspense>
  );
};

export default Settings;
