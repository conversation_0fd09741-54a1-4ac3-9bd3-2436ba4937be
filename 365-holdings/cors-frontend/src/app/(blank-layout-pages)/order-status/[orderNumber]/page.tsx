import React from 'react';
import { Metadata } from 'next';
import PublicOrderStatusPage from '@/views/PublicOrderStatus';
import { apiCall } from '@/actions/common.actions';
import { PublicOrderStatus } from '@/types/public-order-status.types';
import { handleOrderStatusError } from '@/utils/order-status.utils';

export const metadata: Metadata = {
  title: 'Order Status - Track Your Order',
  description: 'Check the status of your order and track its progress.',
};

interface OrderStatusWithNumberPageProps {
  params: Promise<{ orderNumber: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}

const OrderStatusWithNumberPage: React.FC<OrderStatusWithNumberPageProps> = async ({
  params,
  searchParams,
}) => {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  let orderData: PublicOrderStatus | null = null;

  try {
    const requestBody = {
      orderNumber: resolvedParams.orderNumber,
      email: resolvedSearchParams.customerEmail || '',
    };
    orderData = await apiCall<PublicOrderStatus>('POST', '/order-tracking', requestBody, 'public');
  } catch (err) {
    handleOrderStatusError(err);
  }

  return (
    <PublicOrderStatusPage
      shopifyOrderNumber={resolvedParams.orderNumber}
      customerEmail={resolvedSearchParams.customerEmail || ''}
    />
  );
};

export default OrderStatusWithNumberPage;
