import React, { use } from 'react';
import { Metadata } from 'next';
import PublicOrderStatusPage from '@/views/PublicOrderStatus';
import { apiCall } from '@/actions/common.actions';
import { PublicOrderStatus } from '@/types/public-order-status.types';
import { handleOrderStatusError } from '@/utils/order-status.utils';
import useApiCall from '@/hooks/useApiCall';

export const metadata: Metadata = {
  title: 'Order Status - Track Your Order',
  description: 'Check the status of your order and track its progress.',
};

interface OrderStatusPageProps {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}

const OrderStatusPage: React.FC<OrderStatusPageProps> = async ({ searchParams }) => {
  const params = await searchParams;

  return (
    <PublicOrderStatusPage
      shopifyOrderNumber={params.shopifyOrderNumber || ''}
      customerEmail={params.customerEmail || ''}
    />
  );
};

export default OrderStatusPage;
