import type { Metadata } from 'next'
import Login from '@views/Login'
import { getServerMode } from '@core/utils/serverHelpers'

export const metadata: Metadata = {
  title: 'Login',
  description: 'Login to your account',
}

const LoginPage = async () => {
  const mode = await getServerMode()
  
  // Don't access searchParams here, we'll handle it in the Login component
  return <Login mode={mode} />
}

export default LoginPage
