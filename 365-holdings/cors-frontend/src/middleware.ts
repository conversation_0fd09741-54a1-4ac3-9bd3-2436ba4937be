import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname
  const protectedRoutes = ['/home', '/dashboard']
  const publicRoutes = ['/order-status', '/api/auth']  
  if (publicRoutes.some(route => path.startsWith(route))) {
    return NextResponse.next()
  }

  const session = await getToken({
    req,
    secret: process.env.NEXTAUTH_SECRET ?? process.env.AUTH_SECRET
  })

  if (!session && protectedRoutes.some(route => path.startsWith(route))) {
    const loginUrl = new URL('/login', req.url)
    loginUrl.searchParams.set('redirectTo', path)
    return NextResponse.redirect(loginUrl)
  }
  return NextResponse.next()
}

export const config = {
  matcher: ['/((?!api/auth|_next/static|_next/image|images|favicon.ico).*)']
}
