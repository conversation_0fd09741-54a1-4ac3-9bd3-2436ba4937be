import { useMemo } from 'react';
import { createColumn<PERSON><PERSON>per, ColumnDef, AccessorFn } from '@tanstack/react-table';
import { Typography, Chip, Link } from '@mui/material';
import { format } from 'date-fns';

// Generic type for any data structure
type DataItem = Record<string, any>;

// Type for column configuration
type ColumnConfig<T extends DataItem> = {
  accessor: AccessorFn<T> | string; // Support both accessor functions and key-based access
  header: string | React.ReactNode; // Support string or custom header component
  id?: string; // Unique column ID (optional, defaults to header string or accessor key)
  type?: 'text' | 'date' | 'number' | 'boolean' | 'link' | 'chip' | 'custom'; // Data type
  customRender?: (value: any, row: T) => React.ReactNode; // Custom cell renderer
  customHeaderRender?: () => React.ReactNode; // Custom header renderer
  formatDate?: string; // Date format (e.g., 'MM/dd/yyyy')
  chipConfig?: {
    // Configuration for chip display
    valueMap: Record<
      string,
      {
        label: string;
        color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
      }
    >;
  };
  linkConfig?: {
    // Configuration for links
    href: (value: any, row: T) => string;
    target?: '_blank' | '_self' | '_parent' | '_top';
  };
  enableSorting?: boolean; // Enable/disable sorting
  enableFiltering?: boolean; // Enable/disable filtering
  align?: 'left' | 'center' | 'right'; // Cell alignment
  width?: number | string; // Column width
  meta?: Record<string, any>; // Additional metadata for custom use
};

// Utility to get value from accessor or key
const getValueFromAccessor = <T extends DataItem>(
  row: T,
  accessor: AccessorFn<T> | string,
): any => {
  if (typeof accessor === 'function') {
    return accessor(row, 0); // Pass row index as second argument
  }
  return row[accessor];
};

export const useTableColumns = <T extends DataItem>(
  columns: Array<ColumnConfig<T>>,
): ColumnDef<T, any>[] => {
  const columnHelper = createColumnHelper<T>();

  const tableColumns = useMemo<ColumnDef<T, any>[]>(
    () =>
      columns.map(column => {
        const {
          accessor,
          header,
          id,
          type = 'text',
          customRender,
          customHeaderRender,
          formatDate,
          chipConfig,
          linkConfig,
          align = 'left',
        } = column;

        // Ensure id is always a string
        const columnId =
          id ||
          (typeof header === 'string' ? header : '') ||
          (typeof accessor === 'string' ? accessor : 'column');

        return columnHelper.accessor((row: T) => getValueFromAccessor(row, accessor), {
          id: columnId,
          header: customHeaderRender ? customHeaderRender : () => header,
          enableSorting: false,

          cell: ({ getValue, row }) => {
            const value = getValue();

            // Handle null/undefined values
            if (value == null) {
              return (
                <Typography variant="body2" color="text.secondary">
                  N/A
                </Typography>
              );
            }

            // Custom renderer takes precedence
            if (customRender) {
              return customRender(value, row.original);
            }

            // Render based on type
            switch (type) {
              case 'date':
                try {
                  const date = new Date(value);
                  return (
                    <Typography variant="body2">
                      {format(date, formatDate || 'MM/dd/yyyy')}
                    </Typography>
                  );
                } catch {
                  return <Typography variant="body2">Invalid Date</Typography>;
                }

              case 'number':
                return (
                  <Typography variant="body2" style={{ textAlign: align }}>
                    {typeof value === 'number' ? value.toLocaleString() : value}
                  </Typography>
                );

              case 'boolean':
                return (
                  <Typography variant="body2" style={{ textAlign: align }}>
                    {value.toString()}
                  </Typography>
                );

              case 'link':
                if (linkConfig) {
                  return (
                    <Link
                      href={linkConfig.href(value, row.original)}
                      target={linkConfig.target || '_self'}
                      rel={linkConfig.target === '_blank' ? 'noopener noreferrer' : undefined}
                    >
                      {value}
                    </Link>
                  );
                }
                return <Typography variant="body2">{value}</Typography>;

              case 'chip':
                if (chipConfig && chipConfig.valueMap[value]) {
                  const { label, color } = chipConfig.valueMap[value];
                  return <Chip label={label} size="small" color={color} variant="tonal" />;
                }
                return <Chip label={String(value)} size="small" variant="tonal" />;

              case 'custom':
                return value; // Expect customRender to be provided

              case 'text':
              default:
                return <Typography variant="body2">{String(value)}</Typography>;
            }
          },
        });
      }),
    [columns],
  );

  return tableColumns;
};
