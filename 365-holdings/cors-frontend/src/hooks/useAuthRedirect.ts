import { useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';

export const useAuthRedirect = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const hasRedirected = useRef(false);
  const getRedirectKey = () => `auth_redirect_${session?.user?.email || 'unknown'}`;
  
  const checkIfRedirected = () => {
    if (typeof window === 'undefined') return false;
    return localStorage.getItem(getRedirectKey()) === 'true';
  };
  
  const markAsRedirected = () => {
    if (typeof window === 'undefined') return;
    localStorage.setItem(getRedirectKey(), 'true');
    setTimeout(() => {
      localStorage.removeItem(getRedirectKey());
    }, 5000);
  };

  useEffect(() => {
    if (status === 'authenticated' && session?.user && !hasRedirected.current && !checkIfRedirected()) {
      const redirectURL = searchParams.get('redirectTo') || '/home';
      hasRedirected.current = true;
      markAsRedirected();
      
      const timer = setTimeout(() => {
        window.location.href = redirectURL;
      }, 200);
      
      return () => clearTimeout(timer);
    }
  }, [status, session, router, searchParams]);

  return { hasRedirected: hasRedirected.current };
}; 