'use client';
import { useState, useEffect } from 'react';
import { fetchArtworkTypes } from '@/actions';
import { toast } from 'react-toastify';

interface ArtworkType {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export const useFetchArtworkTypes = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<ArtworkType[]>([]);

  useEffect(() => {
    const loadArtworkTypes = async () => {
      setIsLoading(true);
      try {
        const types = await fetchArtworkTypes();
        setData(types);
        setError(null);
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Failed to fetch artwork types');
        setError(error);
        toast.error('Failed to fetch artwork types');
      } finally {
        setIsLoading(false);
      }
    };

    loadArtworkTypes();
  }, []);

  const addArtworkType = (newType: ArtworkType) => {
    setData(prev => [...prev, newType]);
  };

  return { isLoading, error, data, addArtworkType };
}; 