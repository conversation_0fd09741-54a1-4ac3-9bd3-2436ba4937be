import { useCallback } from 'react';

export function useLocalStorage<T = string>() {
  // SSR safety: check if window is defined
  const isClient = typeof window !== 'undefined';

  const get = useCallback((key: string): T | null => {
    if (!isClient) return null;
    try {
      const item = window.localStorage.getItem(key);
      return item ? (JSON.parse(item) as T) : null;
    } catch (err) {
      return null;
    }
  }, [isClient]);

  const set = useCallback((key: string, value: T) => {
    if (!isClient) return;
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (err) {
      // ignore
    }
  }, [isClient]);

  const remove = useCallback((key: string) => {
    if (!isClient) return;
    try {
      window.localStorage.removeItem(key);
    } catch (err) {
      // ignore
    }
  }, [isClient]);

  return { get, set, remove };
} 