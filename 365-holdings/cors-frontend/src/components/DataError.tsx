'use client';
import { LockOutlined } from '@mui/icons-material';
import { Container, Button, Typography } from '@mui/material';
import { Box } from '@mui/material';
import { useRouter } from 'nextjs-toploader/app';

interface DataErrorProps {
  isActionAllowed?: boolean;
}
const DataError: React.FC<DataErrorProps> = ({
  isActionAllowed = true,
}: {
  isActionAllowed?: boolean;
}) => {
  const router = useRouter();
  return (
    <Container maxWidth="sm" className="h-full flex items-center justify-center">
      <Box className=" p-8 rounded-lg shadow-lg text-center" sx={{ maxWidth: 600, width: '100%' }}>
        <LockOutlined sx={{ fontSize: 60, mb: 2 }} />
        <Typography variant="h5" color="text.primary" gutterBottom>
          Oops! Something went wrong
        </Typography>
        <Typography variant="body1" color="text.secondary">
          We encountered an error while fetching the data. Please try again later or contact support
          if the problem persists.
        </Typography>
        {isActionAllowed && (
          <Button
            variant="contained"
            color="primary"
            className="mt-4"
            onClick={() => router.push('/')}
          >
            Return to Home
          </Button>
        )}
      </Box>
    </Container>
  );
};

export default DataError;
