'use client';

import { useEffect, useRef, useState } from 'react';
import type { MouseEvent } from 'react';
import { useRouter } from 'nextjs-toploader/app';
import { styled } from '@mui/material/styles';
import Badge from '@mui/material/Badge';
import Avatar from '@mui/material/Avatar';
import Popper from '@mui/material/Popper';
import Fade from '@mui/material/Fade';
import Paper from '@mui/material/Paper';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import MenuList from '@mui/material/MenuList';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import { useSettings } from '@core/hooks/useSettings';
import { signOut } from 'next-auth/react';
import apiClient from '@/utils/axios';
import { UserInfo } from '@/types/userTypes';
import { useSession } from 'next-auth/react';
import { useDispatch } from 'react-redux';
import { clearFilters as clearOrdersFilters } from '@/redux-store/stores/orders.store';
import { clearFilters as clearProductsFilters } from '@/redux-store/stores/ProductSku.store';
import { useLocalStorage } from '@/hooks/useLocalStorage';

const BadgeContentSpan = styled('span')({
  width: 8,
  height: 8,
  borderRadius: '50%',
  cursor: 'pointer',
  backgroundColor: 'var(--mui-palette-success-main)',
  boxShadow: '0 0 0 2px var(--mui-palette-background-paper)',
});

const UserDropdown = () => {
  const [open, setOpen] = useState(false);
  const anchorRef = useRef<HTMLDivElement>(null);
  const [user, setUser] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { settings } = useSettings();
  const { data: session } = useSession();
  const userFetchedRef = useRef(false);
  const dispatch = useDispatch();
  const { get, set, remove } = useLocalStorage<UserInfo>();

  const handleDropdownOpen = () => {
    setOpen(!open);
  };

  const handleDropdownClose = (
    event?: MouseEvent<HTMLLIElement> | (MouseEvent | TouchEvent),
    url?: string,
  ) => {
    if (url) {
      router.push(url);
    }

    if (anchorRef.current && anchorRef.current.contains(event?.target as HTMLElement)) {
      return;
    }

    setOpen(false);
  };

  const handleUserLogout = async () => {
    try {
      // Clear filters in Redux
      dispatch(clearOrdersFilters());
      dispatch(clearProductsFilters());
      // Clear persisted user data on logout
      remove('userData');
      remove('auth-token');
      remove('refresh-token');

      // Use redirect: false to handle redirection manually
      await signOut({ redirect: false });

      // Navigate after signOut completes
      router.push('/login');
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    // Check if we have cached user data in localStorage
    const cachedUserData = get('userData');
    if (cachedUserData) {
      setUser(cachedUserData);
      return;
    }

    // Only fetch if we have a session token and haven't fetched before
    if (session?.user?.token && !userFetchedRef.current) {
      userFetchedRef.current = true; // Mark as fetched

      const fetchUser = async () => {
        setLoading(true);
        try {
          const res = await apiClient.get('/auth/me');

          const userData = res.data;

          if (userData && userData.email) {
            // Extract the first role if available
            const userRole =
              userData.roles && userData.roles.length > 0 ? userData.roles[0] : { name: 'User' };

            // Use email username as display name
            const displayName = userData.email.split('@')[0];

            const userInfo = {
              name: displayName,
              email: userData.email,
              role: { name: userRole.name },
            };

            // Save to state
            setUser(userInfo);

            // Persist to localStorage
            set('userData', userInfo);
          } else {
            throw new Error('Invalid user data format');
          }
        } catch (err: any) {
          const fallbackUser = {
            name: session?.user?.name || 'User',
            email: session?.user?.email || '<EMAIL>',
            role: { name: 'User' },
          };

          setUser(fallbackUser);

          // Persist fallback user data too
          set('userData', fallbackUser);
        } finally {
          setLoading(false);
        }
      };

      fetchUser();
    } else if (session?.user && !userFetchedRef.current) {
      userFetchedRef.current = true;
      const fallbackUser = {
        name: session.user.name || 'User',
        email: session.user.email || '<EMAIL>',
        role: { name: 'User' },
      };

      setUser(fallbackUser);
      set('userData', fallbackUser);
    }
  }, [session, get, set]);

  return (
    <>
      <Badge
        ref={anchorRef}
        overlap="circular"
        badgeContent={<BadgeContentSpan onClick={handleDropdownOpen} />}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        className="mis-2"
      >
        <Avatar
          ref={anchorRef}
          alt="John Doe"
          src="/images/avatars/1.png"
          onClick={handleDropdownOpen}
          className="cursor-pointer bs-[38px] is-[38px]"
        />
      </Badge>
      <Popper
        open={open}
        transition
        disablePortal
        placement="bottom-end"
        anchorEl={anchorRef.current}
        className="min-is-[240px] !mbs-4 z-[1]"
      >
        {({ TransitionProps, placement }) => (
          <Fade
            {...TransitionProps}
            style={{
              transformOrigin: placement === 'bottom-end' ? 'right top' : 'left top',
            }}
          >
            <Paper className={settings.skin === 'bordered' ? 'border shadow-none' : 'shadow-lg'}>
              <ClickAwayListener
                onClickAway={e => handleDropdownClose(e as MouseEvent | TouchEvent)}
              >
                <MenuList>
                  {loading ? (
                    <div className="flex justify-center items-center p-4">
                      <CircularProgress size={30} />
                    </div>
                  ) : (
                    // Remove the Fragment (<></>) and use a div wrapper instead
                    <div>
                      <div className="flex items-center plb-2 pli-4 gap-2" tabIndex={-1}>
                        <Avatar alt="John Doe" src="/images/avatars/1.png" />
                        <div className="flex items-start flex-col">
                          <Typography variant="caption">{user?.role?.name}</Typography>
                          <Typography className="font-medium" color="text.primary">
                            {user?.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user?.email}
                          </Typography>
                        </div>
                      </div>
                      <Divider className="mlb-1" />
                      <div className="flex items-center plb-2 pli-4">
                        <Button
                          fullWidth
                          variant="contained"
                          color="error"
                          size="small"
                          endIcon={<i className="ri-logout-box-r-line" />}
                          onClick={handleUserLogout}
                          sx={{ '& .MuiButton-endIcon': { marginInlineStart: 1.5 } }}
                        >
                          Logout
                        </Button>
                      </div>
                    </div>
                  )}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Fade>
        )}
      </Popper>
    </>
  );
};

export default UserDropdown;
