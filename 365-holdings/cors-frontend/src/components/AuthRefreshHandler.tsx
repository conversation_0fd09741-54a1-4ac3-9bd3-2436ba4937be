'use client'

import { useEffect, useState } from 'react'
import { useSession, signOut, SessionProvider } from 'next-auth/react'
import { useRouter } from 'nextjs-toploader/app';

// Create a separate component that uses the session
const SessionHandler = () => {
  const { data: session } = useSession()
  const router = useRouter()

  useEffect(() => {
      if (!session) return

    if ((session as any)?.error === 'RefreshAccessTokenError') {
      // Session expired, sign out and redirect with query
      signOut({ redirect: false }).then(() => {
        router.push('/login?error=session-expired')
      })
    }
  }, [session, router])

  return null
}

const AuthRefreshHandler = () => {
  return (
    <SessionProvider>
      <SessionHandler />
    </SessionProvider>
  )
}

export default AuthRefreshHandler
