import { Chip, ChipProps } from '@mui/material';

interface StatusChipProps extends Omit<ChipProps, 'color' | 'variant'> {
  status?: string;
  variant?: 'order' | 'item';
  size?: 'small' | 'medium';
}

const StatusChip = ({ status, variant = 'order', size = 'medium', ...props }: StatusChipProps) => {
  const normalizedStatus = status?.toLowerCase() || 'unfulfilled';

  // Determine background color based on status and variant
  const getBackgroundColor = () => {
    if (variant === 'order') {
      if (normalizedStatus === 'fulfilled') return 'success.dark';
      if (normalizedStatus === 'partially fulfilled') return 'warning.dark';
      if (normalizedStatus === 'unfulfilled') return 'error.dark';
      return 'info.light';
    } else {
      // Item variant
      if (normalizedStatus === 'cancelled') return '#ffcccc';
      if (normalizedStatus === 'shipped') return '#c8e6c9';
      if (normalizedStatus === 'ready for vendor') return '#bbdefb';
      return '#e0e0e0';
    }
  };

  // Format the label to capitalize first letter of each word
  const formattedLabel = status
    ? status.replace(/\b\w/g, (l: string) => l.toUpperCase())
    : 'Unfulfilled';

  return (
    <Chip
      label={formattedLabel}
      size={size}
      sx={{
        backgroundColor: getBackgroundColor(),
        color: variant === 'item' ? '#000' : undefined,
        mt: 1,
        ...props.sx,
      }}
      {...props}
    />
  );
};

export default StatusChip;
