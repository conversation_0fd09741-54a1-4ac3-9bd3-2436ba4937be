'use client';
import { RoleProtected } from '@/components/ProtectedRoleWrapper';
import { Actions, ActionsTarget, ActionsType } from '@/libs/casl/ability';
import { IconButton, IconButtonProps } from '@mui/material';
import { useRouter } from 'next/navigation';
import { ReactElement } from 'react';

interface CustomButtonProps extends IconButtonProps {
  icon: ReactElement;
  ButtonAction?: ActionsType;
  actionTarget?: ActionsTarget;
  navigationRoute?: string | undefined;
}

const CustomIconButton = ({
  icon,
  ButtonAction,
  navigationRoute,
  onClick,
  actionTarget,
  ...rest
}: CustomButtonProps) => {
  const router = useRouter();

  const handleNavigation = (e: React.MouseEvent, path: string) => {
    // If Ctrl or Cmd key is pressed, open in new tab
    if (e.ctrlKey || e.metaKey) {
      window.open(path, '_blank');
    } else {
      // Normal navigation
      router.push(path);
    }
  };

  return (
    <>
      <RoleProtected action={ButtonAction} actionTarget={actionTarget}>
        <IconButton
          {...rest}
          onClick={e => {
            // Don't execute any navigation or click handlers if the button is disabled
            if (rest.disabled) {
              return;
            }

            if (navigationRoute) {
              handleNavigation(e, navigationRoute);
            } else if (onClick) {
              onClick(e);
            }
          }}
        >
          {icon}
        </IconButton>
      </RoleProtected>
    </>
  );
};

export default CustomIconButton;
