'use client';

import React from 'react';
import { Box, Chip, Typography } from '@mui/material';

interface WorkflowTagsChipProps {
  tags: string[];
  size?: 'small' | 'medium';
}

const WorkflowTagsChip: React.FC<WorkflowTagsChipProps> = ({ tags, size = 'small' }) => {
  const getTagColors = (tag: string) => {
    const tagUpper = tag.toUpperCase();

    if (tagUpper.includes('URGENT') || tagUpper.includes('CRITICAL')) {
      return {
        backgroundColor: '#ffebee',
        textColor: '#d32f2f',
      };
    } else if (tagUpper.includes('PENDING') || tagUpper.includes('WAITING')) {
      return {
        backgroundColor: '#fff3e0',
        textColor: '#f57c00',
      };
    } else if (tagUpper.includes('COMPLETED') || tagUpper.includes('DONE')) {
      return {
        backgroundColor: '#e8f5e8',
        textColor: '#388e3c',
      };
    } else if (tagUpper.includes('REVIEW') || tagUpper.includes('APPROVAL')) {
      return {
        backgroundColor: '#f3e5f5',
        textColor: '#7b1fa2',
      };
    } else if (tagUpper.includes('BLOCKED') || tagUpper.includes('HOLD')) {
      return {
        backgroundColor: '#fafafa',
        textColor: '#616161',
      };
    } else if (tagUpper.includes('IN_PROGRESS') || tagUpper.includes('PROCESSING')) {
      return {
        backgroundColor: '#e1f5fe',
        textColor: '#0277bd',
      };
    } else if (tagUpper.includes('ESCALATED') || tagUpper.includes('ESCALATION')) {
      return {
        backgroundColor: '#fce4ec',
        textColor: '#c2185b',
      };
    }

    // Default colors
    return {
      backgroundColor: '#e3f2fd',
      textColor: '#1976d2',
    };
  };

  if (!Array.isArray(tags) || tags.length === 0) {
    return (
      <Typography variant="body2" color="text.secondary">
        No tags
      </Typography>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
      {tags.map((tag: string, index: number) => {
        const { backgroundColor, textColor } = getTagColors(tag);

        return (
          <Chip
            key={`${tag}-${index}`}
            label={tag}
            size={size}
            sx={{
              backgroundColor,
              color: textColor,
              fontWeight: 500,
              fontSize: size === 'small' ? '0.75rem' : '0.875rem',
              '&:hover': {
                backgroundColor,
                opacity: 0.8,
              },
            }}
          />
        );
      })}
    </Box>
  );
};

export default WorkflowTagsChip;
