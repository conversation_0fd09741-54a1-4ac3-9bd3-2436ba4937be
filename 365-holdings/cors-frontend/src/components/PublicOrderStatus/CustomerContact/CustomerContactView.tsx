'use client';
import React from 'react';
import { <PERSON>, Typography, TextField, Button } from '@mui/material';
import { Send, Message } from '@mui/icons-material';
import { toast } from 'react-toastify';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { PublicOrderStatus, PublicLineItem } from '@/types/public-order-status.types';
import useApiCall from '@/hooks/useApiCall';
import RequestHeader from '../NewImageRequest/RequestHeader';

interface CustomerContactViewProps {
  orderData: PublicOrderStatus;
  lineItem: PublicLineItem;
  onSuccess?: () => void;
}

// Form validation schema
const validationSchema = yup.object().shape({
  message: yup
    .string()
    .required('Message is required')
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message must not exceed 1000 characters'),
}) as yup.ObjectSchema<{ message: string }>;

const CustomerContactView: React.FC<CustomerContactViewProps> = ({
  orderData,
  lineItem,
  onSuccess,
}) => {
  const {
    handleSubmit,
    control,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<{ message: string }>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      message: '',
    },
  });

  const { makeRequest: sendCustomerContact, isLoading: isApiLoading } = useApiCall(
    '/workflow-queues/customer-contact',
    'post',
    false,
  );

  const onSubmit = async (data: { message: string }) => {
    try {
      await sendCustomerContact({
        body: {
          orderNumber: orderData.shopifyOrderNumber,
          lineItemId: lineItem.id,
          message: data.message.trim(),
        },
      });

      toast.success('Message sent successfully');
      reset();
      onSuccess?.();
    } catch (error) {
      toast.error('Failed to send message');
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <RequestHeader
        orderData={orderData}
        lineItem={lineItem}
        title="Customer Contact"
        subtitle="Send a message to our team about your order"
        icon={<Message />}
      />

      <Box sx={{ p: 3, flex: 1, overflow: 'auto' }}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Please describe your concern or question about this order:
          </Typography>

          <Controller
            name="message"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                multiline
                rows={6}
                placeholder="Enter your message here..."
                variant="outlined"
                error={!!errors.message}
                helperText={errors.message?.message}
                sx={{ mb: 3 }}
              />
            )}
          />

          {/* Submit Button */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              type="submit"
              variant="contained"
              startIcon={<Send />}
              disabled={isSubmitting || isApiLoading}
              sx={{ minWidth: '140px', py: 1.5 }}
            >
              {isSubmitting || isApiLoading ? 'Sending...' : 'Send Message'}
            </Button>
          </Box>
        </form>
      </Box>
    </Box>
  );
};

export default CustomerContactView;
