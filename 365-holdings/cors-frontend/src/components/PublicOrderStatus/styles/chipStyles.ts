// Chip styling configurations for PublicOrderStatus components

export interface ChipStyleConfig {
  background: string;
  color: string;
  border: string;
  hoverBackground: string;
}

// Status chip styles for line items
export const STATUS_CHIP_STYLES: Record<string, ChipStyleConfig> = {
  'ORDER RECEIVED': {
    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
    color: '#1565c0',
    border: '1px solid #90caf9',
    hoverBackground: 'linear-gradient(135deg, #bbdefb 0%, #90caf9 100%)',
  },
  'CUSTOMER ACTION NEEDED': {
    background: 'linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%)',
    color: '#c62828',
    border: '1px solid #ef9a9a',
    hoverBackground: 'linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%)',
  },
  'ART IN PROGRESS': {
    background: 'linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)',
    color: '#7b1fa2',
    border: '1px solid #ce93d8',
    hoverBackground: 'linear-gradient(135deg, #e1bee7 0%, #ce93d8 100%)',
  },
  'READY FOR PRODUCTION': {
    background: 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)',
    color: '#2e7d32',
    border: '1px solid #a5d6a7',
    hoverBackground: 'linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%)',
  },
};

// Priority chip styles
export const PRIORITY_CHIP_STYLES: Record<string, ChipStyleConfig> = {
  'Pajama Rush': {
    background: 'linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%)',
    color: '#e65100',
    border: '1px solid #ffb74d',
    hoverBackground: 'linear-gradient(135deg, #ffcc02 0%, #ffb74d 100%)',
  },
  'Standard': {
    background: 'linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)',
    color: '#424242',
    border: '1px solid #bdbdbd',
    hoverBackground: 'linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%)',
  },
  'default': {
    background: 'linear-gradient(135deg, #e8eaf6 0%, #c5cae9 100%)',
    color: '#3f51b5',
    border: '1px solid #9fa8da',
    hoverBackground: 'linear-gradient(135deg, #c5cae9 0%, #9fa8da 100%)',
  },
};

// Common chip styles
export const COMMON_CHIP_STYLES = {
  fontWeight: 500,
  textTransform: 'capitalize' as const,
  transition: 'all 0.2s ease-in-out',
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: '0 4px 8px rgba(0,0,0,0.15)',
  },
};

// Helper function to get status chip style
export const getStatusChipStyle = (status: string): ChipStyleConfig => {
  return STATUS_CHIP_STYLES[status] || STATUS_CHIP_STYLES['ORDER RECEIVED'];
};

// Helper function to get priority chip style
export const getPriorityChipStyle = (priority: string): ChipStyleConfig => {
  if (priority === 'Pajama Rush') {
    return PRIORITY_CHIP_STYLES['Pajama Rush'];
  }
  if (priority === 'Standard') {
    return PRIORITY_CHIP_STYLES['Standard'];
  }
  return PRIORITY_CHIP_STYLES['default'];
};

// Helper function to create chip sx props
export const createChipSxProps = (styleConfig: ChipStyleConfig) => ({
  ...COMMON_CHIP_STYLES,
  background: styleConfig.background,
  color: styleConfig.color,
  border: styleConfig.border,
  '&:hover': {
    ...COMMON_CHIP_STYLES['&:hover'],
    background: styleConfig.hoverBackground,
  },
}); 