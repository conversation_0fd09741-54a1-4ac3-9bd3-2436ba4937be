'use client';

import React from 'react';
import { Dialog, DialogContent, IconButton, Box } from '@mui/material';
import { Close } from '@mui/icons-material';
import NewImageRequestView from './NewImageRequestView';
import { PublicOrderStatus, PublicLineItem } from '@/types/public-order-status.types';

interface NewImageRequestModalProps {
  open: boolean;
  onClose: () => void;
  orderData: PublicOrderStatus;
  lineItem: PublicLineItem;
  onSuccess?: () => void;
}

const NewImageRequestModal: React.FC<NewImageRequestModalProps> = ({
  open,
  onClose,
  orderData,
  lineItem,
  onSuccess,
}) => {
  const handleSuccess = () => {
    if (onSuccess) {
      onSuccess();
    }
    onClose(); // Close the modal after successful submission
  };
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 2,
          maxHeight: '90vh',
        },
      }}
    >
      <Box sx={{ position: 'relative' }}>
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 25,
            top: 25,
            zIndex: 2,
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.9)',
            },
          }}
        >
          <Close />
        </IconButton>

        <DialogContent sx={{ p: 0, overflow: 'hidden' }}>
          <NewImageRequestView
            orderData={orderData}
            lineItem={lineItem}
            formType="newImageRequest"
            onSuccess={handleSuccess}
          />
        </DialogContent>
      </Box>
    </Dialog>
  );
};

export default NewImageRequestModal;
