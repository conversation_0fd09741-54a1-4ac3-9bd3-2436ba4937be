'use client';

import React, { useRef, useState } from 'react';
import { Paper, Typography, LinearProgress, Box, Fade, Zoom } from '@mui/material';
import { CloudUpload, Image as ImageIcon } from '@mui/icons-material';

interface FileUploadZoneProps {
  onFileSelect: (file: File) => void;
  uploading?: boolean;
  disabled?: boolean;
  acceptedTypes?: string;
  maxSizeMB?: number;
  id: string;
}

const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  onFileSelect,
  uploading = false,
  disabled = false,
  acceptedTypes = 'image/jpeg,image/jpg,image/png,image/heic,image/heif,.heic,.heif,image/*',
  maxSizeMB = 10,
  id,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type - allow JPG, PNG, and HEIC
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif'];
      const allowedExtensions = ['.jpg', '.jpeg', '.png', '.heic', '.heif'];
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

      // Check both MIME type and file extension
      const isValidType =
        allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);

      if (!isValidType) {
        // You can add toast notification here if you have it available
        console.error('Invalid file type. Only JPG, PNG, and HEIC files are allowed.');
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > maxSizeMB * 1024 * 1024) {
        console.error(`File size must be less than ${maxSizeMB}MB`);
        return;
      }

      onFileSelect(file);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled && !uploading) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    if (disabled || uploading) return;

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];

      // Validate file type - allow JPG, PNG, and HEIC
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif'];
      const allowedExtensions = ['.jpg', '.jpeg', '.png', '.heic', '.heif'];
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

      // Check both MIME type and file extension
      const isValidType =
        allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);

      if (!isValidType) {
        console.error('Invalid file type. Only JPG, PNG, and HEIC files are allowed.');
        return;
      }

      // Validate file size
      if (file.size > maxSizeMB * 1024 * 1024) {
        console.error(`File size must be less than ${maxSizeMB}MB`);
        return;
      }

      onFileSelect(file);
    }
  };

  const handleClick = () => {
    if (!disabled && !uploading) {
      fileInputRef.current?.click();
    }
  };

  const isInteractive = !disabled && !uploading;

  return (
    <Box sx={{ position: 'relative' }}>
      <input
        ref={fileInputRef}
        id={id}
        type="file"
        accept={acceptedTypes}
        onChange={handleFileChange}
        style={{ display: 'none' }}
        disabled={disabled || uploading}
      />

      <Paper
        variant="outlined"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
        sx={{
          p: { xs: 2, sm: 3, md: 4 },
          textAlign: 'center',
          borderStyle: 'dashed',
          borderWidth: 2,
          borderRadius: 3,
          cursor: isInteractive ? 'pointer' : 'not-allowed',
          opacity: disabled || uploading ? 0.6 : 1,
          transition: 'all 0.3s ease',
          position: 'relative',
          overflow: 'hidden',
          minHeight: { xs: 150, sm: 180, md: 200 },
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderColor: isDragOver ? 'primary.main' : uploading ? 'warning.main' : 'grey.300',
          bgcolor: isDragOver ? 'primary.50' : uploading ? 'warning.50' : 'background.paper',
          '&:hover': isInteractive
            ? {
                borderColor: 'primary.main',
                bgcolor: 'primary.50',
                transform: 'translateY(-2px)',
                boxShadow: 4,
              }
            : undefined,
        }}
      >
        {/* Upload Icon with Animation */}
        <Zoom in={!uploading} timeout={300}>
          <Box>
            <CloudUpload
              sx={{
                fontSize: { xs: 48, sm: 56, md: 64 },
                color: isDragOver ? 'primary.main' : 'grey.400',
                mb: { xs: 1, sm: 2 },
                transition: 'all 0.3s ease',
              }}
            />
          </Box>
        </Zoom>

        {/* Loading State */}
        {uploading && (
          <Fade in={uploading}>
            <Box sx={{ mb: { xs: 1, sm: 2 } }}>
              <ImageIcon
                sx={{
                  fontSize: { xs: 48, sm: 56, md: 64 },
                  color: 'warning.main',
                  animation: 'pulse 1.5s ease-in-out infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 },
                  },
                }}
              />
            </Box>
          </Fade>
        )}

        {/* Text Content */}
        <Box>
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              fontWeight: 600,
              color: uploading ? 'warning.main' : isDragOver ? 'primary.main' : 'text.primary',
              fontSize: { xs: '1rem', sm: '1.25rem' },
              mb: { xs: 1, sm: 2 },
            }}
          >
            {uploading
              ? 'Uploading your image...'
              : isDragOver
                ? 'Drop your image here'
                : 'Drop your image here'}
          </Typography>

          {!uploading && (
            <>
              <Typography
                variant="body1"
                color="text.secondary"
                gutterBottom
                sx={{
                  mb: { xs: 1.5, sm: 2 },
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                }}
              >
                or click to browse files
              </Typography>

              <Box
                sx={{
                  p: { xs: 1.5, sm: 2 },
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'grey.200',
                }}
              >
                <Typography
                  variant="body2"
                  color="text.secondary"
                  gutterBottom
                  sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                >
                  <strong>Supported formats:</strong> JPG, PNG, HEIC
                </Typography>
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.625rem', sm: '0.75rem' },
                    fontStyle: 'italic',
                    display: 'block',
                    mt: 0.5,
                  }}
                >
                  HEIC files are supported and will be automatically converted
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                >
                  <strong>Maximum size:</strong> {maxSizeMB}MB
                </Typography>
                <Typography
                  variant="caption"
                  color="error.main"
                  sx={{
                    fontSize: { xs: '0.625rem', sm: '0.75rem' },
                    display: 'block',
                    mt: 0.5,
                    fontStyle: 'italic',
                  }}
                >
                  GIF files are not supported
                </Typography>
              </Box>
            </>
          )}
        </Box>

        {/* Progress Bar */}
        {uploading && (
          <Box sx={{ width: '100%', mt: { xs: 2, sm: 3 } }}>
            <LinearProgress
              color="warning"
              sx={{
                height: 6,
                borderRadius: 3,
                bgcolor: 'warning.100',
              }}
            />
            <Typography
              variant="caption"
              color="warning.main"
              sx={{
                mt: 1,
                display: 'block',
                fontWeight: 500,
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
              }}
            >
              Please wait while we process your image...
            </Typography>
          </Box>
        )}

        {/* Drag Overlay */}
        {isDragOver && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(25, 118, 210, 0.1)',
              border: '3px dashed',
              borderColor: 'primary.main',
              borderRadius: 3,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1,
            }}
          >
            <Typography
              variant="h5"
              color="primary.main"
              sx={{
                fontWeight: 700,
                fontSize: { xs: '1.25rem', sm: '1.5rem' },
              }}
            >
              Drop to upload
            </Typography>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default FileUploadZone;
