'use client';
import React from 'react';
import { <PERSON>, CardContent, Typography, <PERSON>, Divider, Alert, Chip } from '@mui/material';
import Grid2 from '@mui/material/Grid2';
import { Error, CloudUpload, Image as ImageIcon } from '@mui/icons-material';
import ImageCardWithActions from './ImageCardWithActions';
import FileUploadZone from './FileUploadZone';

interface RejectedImageSectionProps {
  rejectedImages: any[];
  uploadedImages: {
    [rejectedId: string]: { file: File | null; url: string | null; uploading: boolean };
  };
  onFileChange: (rejectedId: string, event: React.ChangeEvent<HTMLInputElement>) => void;
  onDeleteImage: (rejectedId: string) => void;
  onViewImage: (imageUrl: string) => void;
}

const RejectedImageSection: React.FC<RejectedImageSectionProps> = ({
  rejectedImages,
  uploadedImages,
  onFileChange,
  onDeleteImage,
  onViewImage,
}) => {
  const handleFileSelect = (rejectedId: string, file: File) => {
    // Create a synthetic event to match the expected interface
    const syntheticEvent = {
      target: {
        files: [file],
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    onFileChange(rejectedId, syntheticEvent);
  };

  return (
    <Card
      elevation={3}
      sx={{
        mb: { xs: 2, sm: 3, md: 4 },
        borderRadius: 3,
        overflow: 'hidden',
      }}
    >
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
          color: 'white',
          p: { xs: 2, sm: 3 },
        }}
      >
        <Box
          display="flex"
          flexDirection={{ xs: 'column', sm: 'row' }}
          alignItems={{ xs: 'center', sm: 'flex-start' }}
          gap={{ xs: 1, sm: 2 }}
        >
          <Error sx={{ fontSize: { xs: 24, sm: 28, md: 32 } }} />
          <Box textAlign={{ xs: 'center', sm: 'left' }}>
            <Typography
              variant="h5"
              fontWeight="bold"
              sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem', md: '1.75rem' } }}
            >
              Rejected Images
            </Typography>
            <Typography
              variant="body1"
              sx={{
                opacity: 1,
                mt: 0.5,
                fontSize: { xs: '0.875rem', sm: '1rem' },
              }}
            >
              Please review and upload new images for the rejected items below
            </Typography>
          </Box>
          <Box
            sx={{
              ml: { xs: 0, sm: 'auto' },
              mt: { xs: 1, sm: 0 },
            }}
          >
            <Chip
              label={`${rejectedImages.length} ${rejectedImages.length === 1 ? 'Image' : 'Images'}`}
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                fontWeight: 600,
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
              }}
            />
          </Box>
        </Box>
      </Box>

      <CardContent sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
        {rejectedImages.length === 0 ? (
          <Alert severity="info" sx={{ borderRadius: 2 }}>
            No rejected images found for this order.
          </Alert>
        ) : (
          <Grid2 container spacing={{ xs: 2, sm: 3, md: 4 }}>
            {rejectedImages.map((img: any, idx: number) => (
              <Grid2 size={12} key={img.id}>
                <Card
                  variant="outlined"
                  sx={{
                    borderRadius: 3,
                    overflow: 'hidden',
                    border: '2px solid',
                    borderColor: 'error.100',
                    bgcolor: 'error.50',
                  }}
                >
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    {/* Image Header */}
                    <Box
                      display="flex"
                      flexDirection={{ xs: 'column', sm: 'row' }}
                      alignItems={{ xs: 'flex-start', sm: 'center' }}
                      justifyContent="space-between"
                      mb={3}
                      gap={{ xs: 1, sm: 0 }}
                    >
                      <Box display="flex" alignItems="center" gap={2}>
                        <ImageIcon color="error" />
                        <Typography
                          variant="h6"
                          fontWeight="bold"
                          color="error.main"
                          sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
                        >
                          Image {idx + 1}
                        </Typography>
                      </Box>
                      <Chip
                        label="Rejected"
                        color="error"
                        size="small"
                        sx={{
                          fontWeight: 600,
                          fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        }}
                      />
                    </Box>

                    <Grid2 container spacing={{ xs: 2, sm: 3, md: 4 }} alignItems="flex-start">
                      {/* Rejected Image */}
                      <Grid2 size={{ xs: 12, lg: 6 }}>
                        <Box>
                          <Typography
                            variant="subtitle1"
                            fontWeight="bold"
                            color="error.main"
                            gutterBottom
                            sx={{
                              mb: 2,
                              fontSize: { xs: '0.875rem', sm: '1rem' },
                            }}
                          >
                            Rejected Image
                          </Typography>
                          <ImageCardWithActions
                            imageUrl={img.url}
                            alt={img.filename}
                            fileName={img.filename}
                            status={img.status}
                            uploadedAt={new Date(img.uploadedAt).toLocaleDateString()}
                            onView={() => onViewImage(img.url)}
                            variant="rejected"
                            helperText="This image was rejected and needs to be replaced"
                          />
                        </Box>
                      </Grid2>

                      {/* Upload New Image */}
                      <Grid2 size={{ xs: 12, lg: 6 }}>
                        <Box>
                          <Box display="flex" alignItems="center" gap={1} mb={2}>
                            <CloudUpload color="primary" />
                            <Typography
                              variant="subtitle1"
                              fontWeight="bold"
                              color="primary.main"
                              sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}
                            >
                              Upload Replacement
                            </Typography>
                          </Box>

                          {uploadedImages[img.id]?.url ? (
                            <ImageCardWithActions
                              imageUrl={uploadedImages[img.id].url || ''}
                              alt="Replacement preview"
                              fileName={uploadedImages[img.id].file?.name}
                              fileSizeMB={
                                uploadedImages[img.id].file
                                  ? uploadedImages[img.id].file!.size / 1024 / 1024
                                  : undefined
                              }
                              variant="uploaded"
                              onView={() => onViewImage(uploadedImages[img.id].url!)}
                              onDelete={() => onDeleteImage(img.id)}
                              showDelete
                              showProgress={uploadedImages[img.id].uploading}
                              progress={uploadedImages[img.id].uploading}
                              helperText="New replacement image ready for submission"
                            />
                          ) : (
                            <FileUploadZone
                              id={`file-input-${img.id}`}
                              onFileSelect={file => handleFileSelect(img.id, file)}
                              uploading={uploadedImages[img.id]?.uploading}
                              disabled={uploadedImages[img.id]?.uploading}
                            />
                          )}
                        </Box>
                      </Grid2>
                    </Grid2>

                    {/* Divider between images (except last one) */}
                    {idx < rejectedImages.length - 1 && (
                      <Divider sx={{ mt: { xs: 2, sm: 3, md: 4 }, borderColor: 'error.200' }} />
                    )}
                  </CardContent>
                </Card>
              </Grid2>
            ))}
          </Grid2>
        )}
      </CardContent>
    </Card>
  );
};

export default RejectedImageSection;
