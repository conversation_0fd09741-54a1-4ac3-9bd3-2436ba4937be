'use client';

import React from 'react';
import Chip from '@mui/material/Chip';
import {
  ORDER_STATUS_LABELS,
  ORDER_STATUS_COLORS,
  LINE_ITEM_STATUS_LABELS,
  OrderStatusType,
} from '@/constants/public-order-status.constants';
import { getStatusChipStyle, createChipSxProps } from './styles/chipStyles';

interface PublicStatusChipProps {
  status: string;
  variant: 'order' | 'lineItem';
  size?: 'small' | 'medium';
}

const PublicStatusChip: React.FC<PublicStatusChipProps> = ({
  status,
  variant,
  size = 'medium',
}) => {
  const getStatusLabel = () => {
    if (variant === 'order') {
      return ORDER_STATUS_LABELS[status as OrderStatusType] || status;
    }
    return LINE_ITEM_STATUS_LABELS[status] || status;
  };

  const getStatusColor = () => {
    if (variant === 'order') {
      return ORDER_STATUS_COLORS[status as OrderStatusType] || 'default';
    }
    // For line items, we use custom styling instead of default colors
    return 'default';
  };

  const getCustomChipStyle = () => {
    if (variant === 'lineItem') {
      const styleConfig = getStatusChipStyle(status);
      return createChipSxProps(styleConfig);
    }
    return {};
  };

  return (
    <Chip
      label={getStatusLabel()}
      color={variant === 'lineItem' ? 'default' : getStatusColor()}
      size={size}
      variant="filled"
      sx={getCustomChipStyle()}
    />
  );
};

export default PublicStatusChip;
