'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Alert,
  Paper,
  Avatar,
  CardMedia,
  TextField,
  LinearProgress,
} from '@mui/material';
import { Palette, Info, CheckCircle, Edit } from '@mui/icons-material';
import { toast } from 'react-toastify';
import { PublicOrderStatus } from '@/types/public-order-status.types';
import PublicStatusChip from '../PublicStatusChip';

interface CustomerApprovalViewProps {
  orderData: PublicOrderStatus;
}

const CustomerApprovalView: React.FC<CustomerApprovalViewProps> = ({ orderData }) => {
  const [revisionMessage, setRevisionMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedAction, setSelectedAction] = useState<'approve' | 'revision' | null>(null);

  const approvalItem = orderData.lineItems.find(item => item.selectedImage);

  const handleApprove = async () => {
    if (!approvalItem) {
      toast.error('No item found for approval');
      return;
    }

    setSelectedAction('approve');
    setIsSubmitting(true);
  };

  const handleRequestRevision = async () => {
    if (!revisionMessage.trim()) {
      toast.error('Please provide details about the revisions needed');
      return;
    }

    if (!approvalItem) {
      toast.error('No item found for revision request');
      return;
    }

    setSelectedAction('revision');
    setIsSubmitting(true);
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      {/* Header Card */}
      <Card
        elevation={3}
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
        }}
      >
        <CardContent sx={{ py: 3 }}>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
              <Palette fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold">
                Artwork Approval
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                Order #{orderData.shopifyOrderNumber}
              </Typography>
            </Box>
          </Box>

          <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Order Status:
            </Typography>
            <PublicStatusChip status={orderData.orderStatus} variant="order" size="small" />

            {approvalItem && (
              <>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Item #{approvalItem.itemNumber}:
                </Typography>
                <PublicStatusChip status={approvalItem.status} variant="lineItem" size="small" />
              </>
            )}
          </Box>
        </CardContent>
      </Card>

      {isSubmitting && <LinearProgress sx={{ mb: 2 }} />}

      {/* Artwork Preview Section */}
      {approvalItem?.selectedImage && (
        <Card elevation={2} sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={1} mb={2}>
              <Info color="primary" />
              <Typography variant="h6" fontWeight="bold" color="primary">
                Artwork Preview
              </Typography>
            </Box>

            <Card
              sx={{
                maxWidth: 500,
                mb: 2,
                borderRadius: 2,
                overflow: 'hidden',
                mx: 'auto',
              }}
            >
              <CardMedia
                component="img"
                height="350"
                image={approvalItem.selectedImage.url}
                alt="Artwork preview"
                sx={{ objectFit: 'cover' }}
              />
            </Card>

            {approvalItem.selectedImage.message && (
              <Alert
                severity="info"
                sx={{
                  borderRadius: 2,
                  '& .MuiAlert-message': {
                    width: '100%',
                  },
                }}
              >
                <Typography variant="body2">
                  <strong>Design Notes:</strong>
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {approvalItem.selectedImage.message}
                </Typography>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Please Review and Choose an Action
          </Typography>

          <Box display="flex" gap={2} mb={3} justifyContent="center" flexWrap="wrap">
            <Button
              variant="contained"
              color="success"
              size="large"
              startIcon={<CheckCircle />}
              onClick={handleApprove}
              disabled={isSubmitting}
              sx={{
                minWidth: 160,
                py: 1.5,
                borderRadius: 2,
                fontSize: '1rem',
              }}
            >
              {isSubmitting && selectedAction === 'approve' ? 'Approving...' : 'Approve Artwork'}
            </Button>

            <Button
              variant="outlined"
              color="warning"
              size="large"
              startIcon={<Edit />}
              onClick={() => {
                // Scroll to revision section
                document.getElementById('revision-section')?.scrollIntoView({ behavior: 'smooth' });
              }}
              disabled={isSubmitting}
              sx={{
                minWidth: 160,
                py: 1.5,
                borderRadius: 2,
                fontSize: '1rem',
              }}
            >
              Request Revisions
            </Button>
          </Box>

          <Alert severity="info" sx={{ borderRadius: 2 }}>
            <Typography variant="body2">
              <strong>Approve:</strong> If you're happy with the artwork, click "Approve" to proceed
              to production.
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              <strong>Request Revisions:</strong> If you need changes, provide detailed feedback
              below.
            </Typography>
          </Alert>
        </CardContent>
      </Card>

      {/* Revision Request Section */}
      <Card elevation={2} sx={{ mb: 3 }} id="revision-section">
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <Edit color="warning" />
            <Typography variant="h6" fontWeight="bold">
              Request Revisions
            </Typography>
          </Box>

          <Typography variant="body2" color="text.secondary" gutterBottom>
            Please provide specific details about what changes you'd like to see:
          </Typography>

          <TextField
            fullWidth
            multiline
            rows={4}
            placeholder="Please describe the specific changes you'd like to see in the artwork. Be as detailed as possible to help our designers understand your requirements..."
            value={revisionMessage}
            onChange={e => setRevisionMessage(e.target.value)}
            variant="outlined"
            sx={{
              mb: 2,
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
              },
            }}
          />

          <Box textAlign="center">
            <Button
              variant="contained"
              color="warning"
              size="large"
              startIcon={<Edit />}
              onClick={handleRequestRevision}
              disabled={isSubmitting || !revisionMessage.trim()}
              sx={{
                minWidth: 200,
                py: 1.5,
                borderRadius: 2,
                fontSize: '1rem',
              }}
            >
              {isSubmitting && selectedAction === 'revision'
                ? 'Submitting...'
                : 'Submit Revision Request'}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Paper elevation={1} sx={{ p: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Need Help?
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          If you're unsure about the artwork or need assistance with your decision, please contact
          our support team.
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • Take your time to review the artwork carefully • Consider how it will look on the final
          product • Provide specific feedback for any changes needed
        </Typography>
      </Paper>
    </Box>
  );
};

export default CustomerApprovalView;
