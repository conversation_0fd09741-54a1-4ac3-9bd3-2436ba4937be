'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Alert,
  Paper,
  CardMedia,
  IconButton,
  CircularProgress,
} from '@mui/material';
import { Pa<PERSON>, CheckCircle, Close, ImageSearch, ZoomIn } from '@mui/icons-material';
import { toast } from 'react-toastify';
import { PublicOrderStatus, PublicLineItem } from '@/types/public-order-status.types';
import ImagePreviewDialog from '@/components/ImagePreviewDialog';
import RequestHeader from '../NewImageRequest/RequestHeader';
import useApiCall from '@/hooks/useApiCall';

interface CustomerApprovalModalProps {
  open: boolean;
  onClose: () => void;
  orderData: PublicOrderStatus;
  lineItem: PublicLineItem;
  onSuccess?: () => void;
}

const CustomerApprovalModal: React.FC<CustomerApprovalModalProps> = ({
  open,
  onClose,
  orderData,
  lineItem,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedAction, setSelectedAction] = useState<'approve' | 'reject' | null>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [artworkImage, setArtworkImage] = useState<{ artfileUrl: string } | null>(null);

  // Use API hook for fetching artwork
  const {
    data: artFileData,
    isLoading: isLoadingArtwork,
    makeRequest: fetchArtwork,
  } = useApiCall<any>(
    '/order-tracking/artfile',
    'get',
    false, // Don't auto-fetch on render
    {
      queryParams: {
        orderNumber: orderData?.shopifyOrderNumber || '',
        email: orderData?.customerEmail || '',
        itemNumber: lineItem?.itemNumber || '',
      },
    },
    'public', // Use public client
  );

  // Use API hook for customer response to artwork
  const { makeRequest: submitCustomerResponse } = useApiCall<any>(
    '/workflow-queues/customer-response-to-artwork',
    'post',
    false, // Don't auto-fetch on render
    {},
    'customer', // Use customer client
  );

  // Fetch artwork when modal opens
  useEffect(() => {
    if (open && orderData.shopifyOrderNumber && orderData.customerEmail && lineItem.itemNumber) {
      fetchArtwork({
        queryParams: {
          orderNumber: orderData.shopifyOrderNumber,
          email: orderData.customerEmail,
          itemNumber: lineItem.itemNumber,
        },
      });
    }
  }, [
    open,
    orderData.shopifyOrderNumber,
    orderData.customerEmail,
    lineItem.itemNumber,
    fetchArtwork,
  ]);

  // Update artwork image when data is loaded
  useEffect(() => {
    if (artFileData && artFileData.artfileUrl) {
      setArtworkImage({ artfileUrl: artFileData.artfileUrl });
    } else if (lineItem.selectedImage) {
      // Fallback to selected image from line item
      setArtworkImage({ artfileUrl: lineItem.selectedImage.url });
    }
  }, [artFileData, lineItem.selectedImage]);

  const handleApprove = async () => {
    if (!lineItem.itemNumber) {
      toast.error('Missing order information');
      return;
    }

    setSelectedAction('approve');
    setIsSubmitting(true);

    try {
      await submitCustomerResponse({
        body: {
          itemNumber: lineItem.itemNumber,
          action: 'approved',
        },
      });
      toast.success('Artwork approved successfully!');
      if (onSuccess) onSuccess();
      onClose();
    } catch (error) {
      toast.error('Failed to approve artwork');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!lineItem.itemNumber) {
      toast.error('Missing order information');
      return;
    }

    setSelectedAction('reject');
    setIsSubmitting(true);

    try {
      await submitCustomerResponse({
        body: {
          itemNumber: lineItem.itemNumber,
          action: 'rejected',
        },
      });
      toast.success('Artwork rejected successfully!');
      if (onSuccess) onSuccess();
      onClose();
    } catch (error) {
      toast.error('Failed to reject artwork');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenPreview = () => {
    if (artworkImage?.artfileUrl) {
      setPreviewImage(artworkImage.artfileUrl);
    }
  };

  const handleClosePreview = () => {
    setPreviewImage(null);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 3 },
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={2}>
            <Palette color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Customer Approval Required
            </Typography>
          </Box>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        {/* Reuse RequestHeader component with custom props for artwork approval */}
        <RequestHeader
          orderData={orderData}
          lineItem={lineItem}
          isSubmitting={isSubmitting}
          uploading={false}
          rejectedCount={0}
          uploadedCount={0}
          title="Artwork Approval"
          subtitle="Please review and approve your artwork"
          icon={<Palette sx={{ fontSize: 36 }} />}
          showStatusChip={true}
          statusChipProps={{
            status: lineItem.status || 'Customer Approval Pending',
            variant: 'lineItem',
            size: 'small',
          }}
        />

        {/* Artwork Preview with Preview Button */}
        <Card elevation={2} sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
              <Box display="flex" alignItems="center" gap={1}>
                <ImageSearch color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  Artwork for Review
                </Typography>
              </Box>

              <Button
                variant="outlined"
                size="small"
                startIcon={<ZoomIn />}
                onClick={handleOpenPreview}
                sx={{ borderRadius: 2 }}
              >
                Preview
              </Button>
            </Box>

            {/* Artwork Image */}
            {isLoadingArtwork ? (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                minHeight={200}
                bgcolor="grey.100"
                borderRadius={2}
              >
                <Box textAlign="center">
                  <CircularProgress size={40} sx={{ mb: 2 }} />
                  <Typography variant="body2" color="text.secondary">
                    Loading artwork...
                  </Typography>
                </Box>
              </Box>
            ) : artworkImage?.artfileUrl ? (
              <Box
                sx={{
                  position: 'relative',
                  cursor: 'pointer',
                  borderRadius: 2,
                  overflow: 'hidden',
                  border: '2px solid',
                  borderColor: 'grey.200',
                  '&:hover': {
                    borderColor: 'primary.main',
                    '& .preview-overlay': {
                      opacity: 1,
                    },
                  },
                }}
                onClick={handleOpenPreview}
              >
                <CardMedia
                  component="img"
                  image={artworkImage.artfileUrl}
                  alt="Artwork for approval"
                  sx={{
                    width: '100%',
                    height: 300,
                    objectFit: 'contain',
                    bgcolor: 'grey.50',
                  }}
                />
                <Box
                  className="preview-overlay"
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    bgcolor: 'rgba(0,0,0,0.5)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    opacity: 0,
                    transition: 'opacity 0.3s ease',
                  }}
                >
                  <Box textAlign="center" color="white">
                    <ZoomIn fontSize="small" />
                    <Typography variant="caption">Click to enlarge</Typography>
                  </Box>
                </Box>
              </Box>
            ) : (
              <Alert severity="warning" sx={{ borderRadius: 2 }}>
                No artwork available for review. Please contact support if you believe this is an
                error.
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <Box display="flex" gap={2} justifyContent="center">
          <Button
            variant="contained"
            color="success"
            size="large"
            startIcon={<CheckCircle />}
            onClick={handleApprove}
            disabled={isSubmitting || !artworkImage?.artfileUrl}
            sx={{
              minWidth: 150,
              py: 1.5,
              px: 3,
              borderRadius: 2,
              fontWeight: 600,
            }}
          >
            {isSubmitting && selectedAction === 'approve' ? 'Approving...' : 'Approve'}
          </Button>

          <Button
            variant="contained"
            color="error"
            size="large"
            startIcon={<Close />}
            onClick={handleReject}
            disabled={isSubmitting || !artworkImage?.artfileUrl}
            sx={{
              minWidth: 150,
              py: 1.5,
              px: 3,
              borderRadius: 2,
              fontWeight: 600,
            }}
          >
            {isSubmitting && selectedAction === 'reject' ? 'Rejecting...' : 'Reject'}
          </Button>
        </Box>
      </DialogContent>

      {/* Image Preview Dialog */}
      <ImagePreviewDialog
        open={!!previewImage}
        onClose={handleClosePreview}
        imageUrl={previewImage}
        title="Artwork Preview"
      />
    </Dialog>
  );
};

export default CustomerApprovalModal;
