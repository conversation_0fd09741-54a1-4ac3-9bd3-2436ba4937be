import React, { useState, useEffect, useRef } from 'react';
import {
  Autocomplete,
  Checkbox,
  TextField,
  FormControl,
  CircularProgress,
  Popper,
  MenuItem,
  Select,
} from '@mui/material';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import axios from 'axios';
import apiClient from '@/utils/axios';
import { FieldsType, Options } from '@/redux-store/stores/common.store';
import { PopperProps } from '@mui/material/Popper/BasePopper.types';
import { SelectValue } from '@/types/order-details.types';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const CustomPopper = (props: PopperProps) => {
  return (
    <Popper
      {...props}
      style={{ width: 'auto', minWidth: 100, maxWidth: 'fit-content' }}
      placement="bottom-start"
    />
  );
};

const normalizeOption = (option: any) => {
  if (!option) return { key: '', value: '' };
  if (typeof option === 'string') return { key: option, value: option };
  if (typeof option === 'object') {
    if ('key' in option && 'value' in option) return { key: option.key, value: option.value };
    if ('sku' in option && 'id' in option) return { key: option.sku, value: option.id };
    if ('parentSku' in option && option.parentSku) {
      return {
        key: option.parentSku.sku || String(option.parentSku.id),
        value:
          typeof option.parentSku.id === 'object' && option.parentSku.id.value
            ? option.parentSku.id.value
            : option.parentSku.id,
      };
    }
    if ('childSku' in option && option.childSku) {
      return {
        key: option.childSku.sku || String(option.childSku.id),
        value:
          typeof option.childSku.id === 'object' && option.childSku.id.value
            ? option.childSku.id.value
            : option.childSku.id,
      };
    }
    if ('value' in option) return { key: String(option.value), value: option.value };
  }
  return { key: String(option), value: String(option) };
};

export default function SearchableSelect({
  attr,
  value,
  handleValueChange,
  multiple,
  serachUrl = `product-sku`,
  sx,
  size,
  customLabel,
  placeholder = 'Search...',
  selectedOptions = [],
}: {
  attr: FieldsType;
  value: SelectValue;
  handleValueChange: (event: string, newValue: any) => void;
  multiple?: boolean;
  serachUrl?: string;
  sx?: any;
  size?: 'small' | 'medium';
  customLabel?: string;
  placeholder?: string | never;
  selectedOptions?: any[];
}) {
  const [options, setOptions] = useState<any[] | Options[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const attrKey = useRef<null | string>(null);
  useEffect(() => {
    const controller = new AbortController();
    const fetchOptions = async () => {
      if (inputValue === '' || !attr.fetch_db) return;
      setLoading(true);
      try {
        let queryParam;

        if (serachUrl === 'artwork-types') {
          queryParam = `name:like:${inputValue}`;
        } else if (serachUrl === 'product-sku/products') {
          queryParam = inputValue;
        } else {
          queryParam = `${attr?.filter_key ?? (attr.secondary_key ? `${attr.key}.${attr.secondary_key}` : attr.key)}:like:${inputValue}`;
        }

        const res = await apiClient.get(serachUrl, {
          params: {
            q: queryParam,
          },
          signal: controller.signal,
        });

        const filteredData = res?.data?.data?.flatMap((val: any) => {
          const key = attr.key;
          const backendKey = attr?.backend_key;
          const filterKey = attr.filter_key;
          const secondary_key = attr.secondary_key;
          if (serachUrl === 'artwork-types') {
            const result = { key: val.name, value: val.id };
            return result;
          }

          if (filterKey) {
            const result = { key: val[filterKey], value: val[backendKey as string] };
            return result;
          }

          if (secondary_key && Array.isArray(val[key])) {
            return val[key].map((secondVal: any) => secondVal[secondary_key]);
          }
          const result = val[secondary_key ?? key];
          return result;
        });
        setOptions(filteredData?.flat());
      } catch (error) {
        if (axios.isCancel(error)) return;
        console.error('Search failed:', error);
      } finally {
        setLoading(false);
      }
    };

    const debounce = setTimeout(fetchOptions, 300);

    return () => {
      controller.abort();
      clearTimeout(debounce);
    };
  }, [inputValue]);

  useEffect(() => {
    if (!attrKey.current) attrKey.current = attr.key;
  }, []);

  useEffect(() => {
    if (attr.key === attrKey.current) return;
    attrKey.current = attr.key;
    setOptions([]);
  }, [attr]);

  const calculateWidth = (label: string) => {
    if (!label) return 300;
    const width = label.length * 10;
    return Math.max(Math.min(width, 500), 300);
  };

  // Merge options and selectedOptions for Autocomplete
  const mergedOptions = React.useMemo(() => {
    const selected = Array.isArray(selectedOptions) ? selectedOptions : [];
    const all = [...options];
    // Normalize all options and selectedOptions
    const normalizedAll = all.map(normalizeOption);
    const normalizedSelected = selected.map(normalizeOption);
    // Merge selected into options if missing
    normalizedSelected.forEach(sel => {
      if (!normalizedAll.some(opt => opt.value === sel.value)) {
        normalizedAll.push(sel);
      }
    });
    return normalizedAll;
  }, [options, selectedOptions]);

  return (
    <FormControl
      style={{
        minWidth: calculateWidth(customLabel || attr.label || ''),
        maxWidth: '500px',
        width: '100%',
      }}
      size={size}
      variant="outlined"
    >
      {attr?.fetch_db ? (
        <Autocomplete
          size={size}
          multiple={multiple}
          disableCloseOnSelect
          key={attr.key}
          options={mergedOptions}
          loading={loading}
          freeSolo
          slots={{ popper: CustomPopper }}
          style={{ width: '100%' }}
          getOptionLabel={option =>
            option && typeof option === 'object'
              ? option.key || (option.value ? `SKU-${option.value}` : '')
              : String(option)
          }
          value={
            Array.isArray(value)
              ? value.map(normalizeOption)
              : value
                ? [normalizeOption(value)]
                : []
          }
          inputValue={inputValue}
          onChange={(event, newValue) => {
            if (multiple) {
              handleValueChange(
                `${attr?.key}${attr?.secondary_key ? `.${attr?.secondary_key}` : ''}`,
                Array.isArray(newValue) ? newValue.map(normalizeOption) : [],
              );
            } else {
              handleValueChange(
                `${attr?.key}${attr?.secondary_key ? `.${attr?.secondary_key}` : ''}`,
                newValue ? normalizeOption(newValue) : null,
              );
            }
          }}
          onInputChange={(event, newInputValue) => {
            if (!attr?.fetch_db) return;
            setInputValue(newInputValue);
          }}
          isOptionEqualToValue={(option, value) => {
            const o = normalizeOption(option);
            const v = normalizeOption(value);
            return o.value === v.value;
          }}
          renderOption={(props, option, { selected }) => {
            const { key, ...otherProps } = props;
            return (
              <li key={key} {...otherProps}>
                {multiple && (
                  <Checkbox
                    icon={icon}
                    checkedIcon={checkedIcon}
                    style={{ marginRight: 8 }}
                    checked={selected}
                  />
                )}
                {option.key || option}
              </li>
            );
          }}
          renderInput={params => (
            <TextField
              {...params}
              label={customLabel || attr.label}
              placeholder={placeholder}
              variant="outlined"
              fullWidth
              slotProps={{
                input: {
                  ...params.InputProps,
                  style: {
                    ...params.inputProps?.style,
                    overflowX: 'auto',
                    whiteSpace: 'normal',
                    wordBreak: 'break-word',
                  },
                  endAdornment: (
                    <>
                      {loading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                },
              }}
              sx={{
                minWidth: 300,
                maxWidth: 500,
                '& .MuiInputBase-root': {
                  maxWidth: '100%',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                },
              }}
            />
          )}
        />
      ) : (
        <Select
          fullWidth
          value={value}
          onChange={e => handleValueChange(attr.key, e.target.value)}
          label={customLabel || attr.label}
          sx={{
            minWidth: 300,
            maxWidth: 500,
            '& .MuiSelect-select': {
              whiteSpace: 'normal',
              wordBreak: 'break-word',
            },
          }}
        >
          {attr?.options?.map(option => (
            <MenuItem key={option?.key ?? option} value={(option?.value as string) ?? option}>
              {option?.key ?? option}
            </MenuItem>
          ))}
        </Select>
      )}
    </FormControl>
  );
}
