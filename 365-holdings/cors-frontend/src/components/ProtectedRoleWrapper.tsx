import { ActionsTarget, ActionsType, AppAbility } from '@/libs/casl/ability';
import { useAbility } from '@/libs/casl/AbilityContext';
import React, { ReactElement } from 'react';

interface ProtectedProps {
  children: ReactElement;
  actionTarget?: ActionsTarget;
  action?: ActionsType | undefined;
}

export const RoleProtected = ({ action, actionTarget, children }: ProtectedProps) => {
  const ability: AppAbility | null = useAbility();

  const hasPermission = action && actionTarget ? ability?.can(action, actionTarget) : true;
  const disabled = !hasPermission;
  const className = disabled ? 'pointer-events-none opacity-40 cursor-not-allowed' : '';

  // If user doesn't have permission, prevent onClick and onChange from being called
  const modifiedChildren = React.cloneElement(children, {
    disabled,
    className,
    onClick: disabled ? undefined : children.props.onClick,
    onChange: disabled ? undefined : children.props.onChange,
    sx: disabled
      ? {
          opacity: 0.4,
          cursor: 'not-allowed',
          '&:hover': { opacity: 0.4 },
          '&:active': { opacity: 0.4 },
        }
      : children.props.sx,
  });

  return modifiedChildren;
};
