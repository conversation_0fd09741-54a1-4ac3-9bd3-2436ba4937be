'use client';

import { ReactNode } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import { ThemeColor } from '@core/types';

interface CustomDialogProps {
  open: boolean;
  title: string;
  children: ReactNode;
  confirmLabel?: string;
  cancelLabel?: string;
  confirmColor?: ThemeColor;
  variantType?: 'contained' | 'outlined';
  loading?: boolean;
  disableConfirm?: boolean;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  sx?: any;
}

const CustomDialog = ({
  open,
  title,
  children,
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  confirmColor = 'primary',
  variantType = 'contained',
  loading = false,
  disableConfirm = false,
  maxWidth = 'sm',
  fullWidth = true,
  onConfirm,
  onCancel,
}: CustomDialogProps) => {
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      aria-labelledby="custom-dialog-title"
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      sx={{
        '& .MuiDialog-paper': {
          overflow: 'visible',
        },
      }}
    >
      <DialogTitle id="custom-dialog-title">{title}</DialogTitle>
      <DialogContent>{children}</DialogContent>
      <DialogActions>
        <Button onClick={onCancel} disabled={loading}>
          {cancelLabel}
        </Button>
        <Button
          onClick={onConfirm}
          color={confirmColor}
          autoFocus
          disabled={loading || disableConfirm}
          variant={variantType}
        >
          {loading ? (
            <>
              <CircularProgress size={20} color="inherit" className="mie-2" />
              Processing...
            </>
          ) : (
            confirmLabel
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CustomDialog;
