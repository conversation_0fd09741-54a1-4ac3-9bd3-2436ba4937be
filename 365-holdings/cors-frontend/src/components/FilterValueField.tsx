import React from 'react';
import { TextField, Box, Chip, CircularProgress, Stack } from '@mui/material';
import CustomSelect from './CustomSelect';
import {Options } from '@/redux-store/stores/common.store';
import Autocomplete from '@mui/material/Autocomplete';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format } from 'date-fns';
import { FilterValueFieldProps, ParentChildOption, FilterValueFieldSelectValue } from '@/types/filter-value-field.types';


const FilterValueField: React.FC<FilterValueFieldProps> = ({
  data,
  value,
  onChange,
  label = 'Value',
  asyncOptions,
  isLoading,
  onSearch,
}) => {
  if (!data) return null;

  // 1. Async autocomplete multi-select for sku, products, parentSku, childSku, shopifyOrderNumber
  const isAsyncMultiSelect =
    data.fetch_db &&
    (data.key === 'sku' ||
      data.key === 'products' ||
      data.key === 'parentSku' ||
      data.key === 'childSku' ||
      data.key === 'shopifyOrderNumber');
  if (isAsyncMultiSelect) {
    const isParentOrChild = data.key === 'parentSku' || data.key === 'childSku';
    // Merge selected ids into options so chips always show
    const mergedOptions = [
      ...(asyncOptions || []),
      ...(Array.isArray(value) && isParentOrChild
        ? (value as ParentChildOption[])
            .filter(
              v =>
                typeof v === 'object' &&
                v !== null &&
                !(asyncOptions || []).some(opt => opt.value === v.value),
            )
            .map(v => ({ label: v.label, value: v.value }))
        : []),
    ];
    return (
      <Autocomplete
        multiple
        freeSolo
        size="small"
        fullWidth
        options={isParentOrChild ? mergedOptions : asyncOptions || []}
        value={
          Array.isArray(value) && isParentOrChild
            ? (value as ParentChildOption[])
            : Array.isArray(value)
              ? (value as (string | number | boolean)[]).map(v => {
                  const found = (asyncOptions || []).find(opt => opt.value === v);
                  return found ? found : { label: String(v), value: String(v) };
                })
              : []
        }
        getOptionLabel={option =>
          typeof option === 'string' ? option : String(option.label || option.value || '')
        }
        isOptionEqualToValue={(option, val) =>
          (typeof option === 'string' ? option : option.value) ===
          (typeof val === 'string' ? val : val.value)
        }
        renderOption={(props, option, { selected }) => {
          const { key: reactKey, ...rest } = props;
          const optionKey = typeof option === 'string' ? option : option.value;
          return (
            <li key={reactKey ?? optionKey} {...rest}>
              <Chip
                style={{ marginRight: 8 }}
                label={typeof option === 'string' ? option : option.label}
              />
            </li>
          );
        }}
        renderTags={(selected, getTagProps) =>
          selected.map((option, idx) => (
            <Chip
              label={typeof option === 'string' ? option : option.label || option.value}
              {...getTagProps({ index: idx })}
              key={String(typeof option === 'string' ? option : option.value)}
            />
          ))
        }
        renderInput={params => (
          <TextField
            {...params}
            label={label}
            placeholder={`Type to search...`}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <>
                  {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
        onInputChange={(_, newValue) => {
          if (onSearch) onSearch(newValue || '');
        }}
        onChange={(_, newValue) => {
          if (isParentOrChild) {
            // Only allow array of ParentChildOption
            const arr = Array.isArray(newValue)
              ? (newValue.filter(
                  v => typeof v === 'object' && v !== null && 'label' in v && 'value' in v,
                ) as ParentChildOption[])
              : [];
            onChange(arr);
          } else {
            let values: string[] = [];
            if (Array.isArray(newValue)) {
              values = newValue.map(item => (typeof item === 'string' ? item : String(item.value)));
            }
            onChange(values);
          }
        }}
        loading={isLoading}
      />
    );
  }

  // 2. Shopify Native Variant: two-step async dropdown
  if (data.key === 'shopifyNativeVariant') {
    // asyncOptions: { label: key, value: value }[]
    const key = typeof value === 'object' && value && 'key' in value ? value.key : '';
    const val = typeof value === 'object' && value && 'value' in value ? value.value : '';
    const keyOptions = Array.from(new Set((asyncOptions || []).map(opt => opt.label))).map(k => ({
      key: k,
      label: k,
    }));
    const valueOptions = (asyncOptions || [])
      .filter(opt => opt.label === key)
      .map(opt => ({ key: opt.value, label: opt.value }));
    return (
      <Box sx={{ display: 'flex', gap: 2 }}>
        <CustomSelect
          value={key}
          onChange={newKey => onChange({ key: newKey as string, value: '' })}
          options={keyOptions}
          label="Key"
          disabled={isLoading}
        />
        <CustomSelect
          value={val}
          onChange={newVal => onChange({ key: key, value: newVal as string })}
          options={valueOptions}
          label="Value"
          disabled={isLoading || !key}
        />
      </Box>
    );
  }

  // 3. Date range picker for orderDate
  if (data.type === 'date') {
    // Value is { start?: string, end?: string }
    const valObj = typeof value === 'object' && value !== null ? value : {};
    const start = 'start' in valObj ? valObj.start : '';
    const end = 'end' in valObj ? valObj.end : '';
    return (
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Stack direction="row" spacing={2} sx={{ minWidth: 300 }}>
          <DatePicker
            label="Start Date"
            value={start ? new Date(start) : null}
            onChange={date => {
              const formatted = date ? format(date, 'yyyy-MM-dd') : '';
              onChange({ ...(valObj || {}), start: formatted });
            }}
            slotProps={{ textField: { size: 'small' } }}
          />
          <DatePicker
            label="End Date"
            value={end ? new Date(end) : null}
            onChange={date => {
              const formatted = date ? format(date, 'yyyy-MM-dd') : '';
              onChange({ ...(valObj || {}), end: formatted });
            }}
            slotProps={{ textField: { size: 'small' } }}
          />
        </Stack>
      </LocalizationProvider>
    );
  }

  // 4. All other select/multi_select
  if (data.type === 'select' || data.type === 'multi_select') {
    let normalizedValue: FilterValueFieldSelectValue = '';
    if (data.type === 'multi_select') {
      if (Array.isArray(value)) {
        normalizedValue = value.filter(
          v => typeof v === 'string' || typeof v === 'number' || typeof v === 'boolean',
        );
      } else if (
        typeof value === 'string' ||
        typeof value === 'number' ||
        typeof value === 'boolean'
      ) {
        normalizedValue = [value];
      } else {
        normalizedValue = [];
      }
    } else {
      normalizedValue =
        typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean'
          ? value
          : '';
    }
    return (
      <CustomSelect
        value={normalizedValue}
        onChange={(val: FilterValueFieldSelectValue) => {
          if (data.type === 'multi_select') {
            onChange(
              Array.isArray(val)
                ? val
                : val !== undefined && val !== null && val !== ''
                  ? [val]
                  : [],
            );
          } else {
            onChange(val);
          }
        }}
        multiple={data.type === 'multi_select'}
        label={label}
        options={(data.options || []).map((option: Options) => ({
          key: option.value as string | number | boolean,
          label: String(option.key),
        }))}
        renderValue={(selected: string | number | (string | number)[]) => {
          if (Array.isArray(selected)) {
            return (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {selected.map(val => {
                  const found = (data.options || []).find(opt => String(opt.value) === String(val));
                  return <Chip key={String(val)} label={found ? String(found.key) : String(val)} />;
                })}
              </Box>
            );
          }
          const found = (data.options || []).find(opt => String(opt.value) === String(selected));
          return found ? String(found.key) : String(selected);
        }}
      />
    );
  }

  // fallback: always show a text field if nothing else matches
  const safeValue = typeof value === 'string' || typeof value === 'number' ? value : '';
  return (
    <TextField
      fullWidth
      size="small"
      label={label}
      value={safeValue}
      onChange={e => onChange(e.target.value)}
    />
  );
};

export default FilterValueField;
