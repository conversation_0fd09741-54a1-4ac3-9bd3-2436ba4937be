import React, { useState } from 'react';
import { Box, Typography, Button, IconButton, Grid, Chip } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DeleteIcon from '@mui/icons-material/Delete';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import PetsIcon from '@mui/icons-material/Pets';

interface ImageUploadSectionProps {
  images: File[];
  onAddImages: (newImages: File[]) => void;
  onRemoveImage: (index: number) => void;
  onPreviewImage: (file: File) => void;
  maxImages?: number;
  error?: boolean;
  errorMessage?: string;
  title?: string;
  helperText?: string;
  showPetLabels?: boolean;
}

const ImageUploadSection = ({
  images,
  onAddImages,
  onRemoveImage,
  onPreviewImage,
  maxImages = 10,
  error = false,
  errorMessage = 'Please upload at least one image',
  title = 'Upload Images',
  helperText = 'You can upload up to 10 images (Maximum size: 5MB each)',
  showPetLabels = false,
}: ImageUploadSectionProps) => {
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files);
      // Limit total images to maxImages
      const totalImages = images.length + newFiles.length;
      if (totalImages > maxImages) {
        // Truncate the list to respect the maximum
        const allowedNewFiles = newFiles.slice(0, maxImages - images.length);
        onAddImages(allowedNewFiles);
      } else {
        onAddImages(newFiles);
      }
    }
  };

  const inputId = `image-upload-${Math.random().toString(36).substring(2, 9)}`;

  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="subtitle1" gutterBottom>
        {title}
      </Typography>
      <Box
        sx={{
          border: '1px dashed #ccc',
          p: 3,
          textAlign: 'center',
          borderRadius: 1,
          mb: 2,
          borderColor: error ? 'error.main' : '#ccc',
        }}
      >
        <input
          accept="image/*"
          style={{ display: 'none' }}
          id={inputId}
          type="file"
          multiple
          onChange={handleImageUpload}
          disabled={images.length >= maxImages}
        />
        <label htmlFor={inputId}>
          <Button
            variant="outlined"
            component="span"
            startIcon={<CloudUploadIcon />}
            disabled={images.length >= maxImages}
          >
            {images.length >= maxImages ? 'Maximum Images Reached' : 'Upload Images'}
          </Button>
        </label>
        <Typography variant="caption" display="block" sx={{ mt: 1 }}>
          {helperText}
        </Typography>
        {error && (
          <Typography variant="caption" color="error" display="block" sx={{ mt: 1 }}>
            {errorMessage}
          </Typography>
        )}
        {images.length >= maxImages && (
          <Typography variant="caption" color="warning.main" display="block" sx={{ mt: 1 }}>
            Maximum number of images reached ({maxImages})
          </Typography>
        )}
      </Box>

      {/* Display uploaded images */}
      {images.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
            {images.length}/{maxImages} images uploaded
          </Typography>
          <Grid container spacing={2}>
            {images.map((file, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Box
                  sx={{
                    position: 'relative',
                    width: '100%',
                    height: 200,
                    border: '1px solid #eee',
                    borderRadius: 1,
                    overflow: 'hidden',
                    cursor: 'zoom-in',
                  }}
                  onClick={() => onPreviewImage(file)}
                >
                  {showPetLabels && (
                    <Chip
                      icon={<PetsIcon />}
                      label={`Pet ${index + 1}`}
                      color="primary"
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: 8,
                        left: 8,
                        zIndex: 2,
                        fontWeight: 'medium',
                      }}
                    />
                  )}
                  <img
                    src={URL.createObjectURL(file)}
                    alt={`Upload ${index}`}
                    style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                  />
                  <IconButton
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      bgcolor: 'rgba(255,255,255,0.8)',
                      '&:hover': {
                        bgcolor: 'rgba(255,255,255,1)',
                      },
                      zIndex: 1,
                    }}
                    onClick={e => {
                      e.stopPropagation(); // Prevent opening preview when clicking delete
                      onRemoveImage(index);
                    }}
                  >
                    <DeleteIcon fontSize="small" color="error" />
                  </IconButton>
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 8,
                      right: 8,
                      bgcolor: 'rgba(0,0,0,0.5)',
                      color: 'white',
                      borderRadius: '50%',
                      width: 32,
                      height: 32,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <ZoomInIcon fontSize="small" />
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default ImageUploadSection;
