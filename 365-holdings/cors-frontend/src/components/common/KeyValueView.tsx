import { Box } from '@mui/material';
import Grid from '@mui/material/Grid2';
import React from 'react';

const KeyValueView = ({ name, value, index }: { name: string; value: string; index?: number }) => {
  return (
    <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index || 'key-value-view'}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          flexWrap: 'wrap',
        }}
      >
        <Box sx={{ fontWeight: 'bold' }}>{name}:</Box>
        <Box sx={{ wordBreak: 'break-word' }}>{value || '-'}</Box>
      </Box>
    </Grid>
  );
};

export default KeyValueView;
