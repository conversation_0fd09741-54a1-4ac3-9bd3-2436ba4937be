import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Select,
  MenuItem,
  ListSubheader,
  InputLabel,
  FormControl,
  OutlinedInput,
  CircularProgress,
  Box,
  Chip,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import { FieldsType } from '@/redux-store/stores/common.store';
import SearchableSelect from '../SearchableSelect';

export const RenderValueField = ({ attr, handleValueChange, value, size }: any) => {
  if (!attr) return null;

  const controlId = `select-${attr.key}`;

  if (attr.key === 'products.category') {
    const isMultiSelect = false;
    const validValue = isMultiSelect
      ? Array.isArray(value)
        ? value
        : []
      : value && attr.options?.some((option: any) => option.value === value)
        ? value
        : '';

    return (
      <FormControl fullWidth size={size} id={controlId}>
        <InputLabel htmlFor={`${controlId}-input`}>{attr.label || 'Category'}</InputLabel>
        <Select
          fullWidth
          multiple={isMultiSelect}
          value={validValue}
          onChange={e => handleValueChange(attr.key, e.target.value)}
          input={<OutlinedInput label={attr.label || 'Category'} />}
          renderValue={
            isMultiSelect
              ? selected => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {(selected as string[]).map(val => (
                      <Chip key={val} label={val} size="small" />
                    ))}
                  </Box>
                )
              : undefined
          }
          sx={{ minWidth: { xs: '100%', sm: 'auto' } }}
          inputProps={{ id: `${controlId}-input` }}
        >
          {!isMultiSelect && (
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
          )}
          {attr.options?.map((option: any) =>
            option && option.value ? (
              <MenuItem key={option.key || option.value} value={option.value}>
                {option.key || option.value}
              </MenuItem>
            ) : null,
          )}
        </Select>
      </FormControl>
    );
  }
  if (attr.type === 'multi_select' && !attr.fetch_db) {
    return (
      <FormControl fullWidth size={size} id={controlId}>
        <InputLabel htmlFor={`${controlId}-input`}>{attr.label || 'Value'}</InputLabel>
        <Select
          fullWidth
          multiple
          value={Array.isArray(value) ? value : []}
          onChange={e => handleValueChange(attr.key, e.target.value)}
          renderValue={selected => (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {(selected as string[]).map(value => (
                <Chip key={value} label={value} size="small" />
              ))}
            </Box>
          )}
          sx={{ minWidth: { xs: '100%', sm: 'auto' } }}
          inputProps={{ id: `${controlId}-input` }}
        >
          {attr.options?.map((option: any) =>
            option && option.value ? (
              <MenuItem key={option.key || option.value} value={option.value}>
                {option.key || option.value}
              </MenuItem>
            ) : null,
          )}
        </Select>
      </FormControl>
    );
  }

  if (attr.key === 'parentSkuIds' || attr.key === 'childSkuIds') {
    return (
      <SearchableSelect
        multiple={true}
        attr={{
          ...attr,
          fetch_db: true,
          type: 'multi_select',
        }}
        value={value || []}
        serachUrl="product-sku"
        handleValueChange={(field: string, newValue: any) => {
          handleValueChange(attr.key, newValue);
        }}
        size={size}
      />
    );
  }

  if (attr.key === 'artworkTypeId') {
    return (
      <SearchableSelect
        multiple={true}
        attr={{
          ...attr,
          fetch_db: true,
          type: 'multi_select',
        }}
        value={value || []}
        serachUrl="artwork-types"
        handleValueChange={(field: string, newValue: any) => {
          handleValueChange(attr.key, newValue);
        }}
        size={size}
      />
    );
  }

  if (attr.type === 'select' && attr.options) {
    return (
      <FormControl fullWidth size={size}>
        <InputLabel>{attr.label}</InputLabel>
        <Select
          fullWidth
          value={value !== undefined ? value : ''}
          onChange={e => handleValueChange(attr.key, e.target.value)}
          input={<OutlinedInput label={attr.label} />}
          style={{ minWidth: 200 }}
        >
          {attr.options.map((option: any) => (
            <MenuItem key={option.value?.toString()} value={option.value}>
              {option.key}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    );
  }

  if (attr.type === 'text' || attr.type === 'number') {
    return (
      <TextField
        size={size}
        fullWidth
        type={attr.type === 'number' ? 'number' : 'text'}
        label={attr.label || 'Value'}
        inputProps={attr.type === 'number' ? { min: 0 } : {}}
        value={value || ''}
        onChange={e => {
          if (attr.type === 'number') {
            const val = e.target.value;
            // Allow empty string for clearing the field
            if (val === '' || Number(val) >= 0) {
              handleValueChange(attr.key, val);
            }
          } else {
            handleValueChange(attr.key, e.target.value);
          }
        }}
      />
    );
  }

  return null;
};
function normalizeAttributes(input: any) {
  const normalizedAttributes: Record<string, any> = {};

  for (const [key, value] of Object.entries(input)) {
    if (Array.isArray(value) && value.every(v => typeof v === 'object' && v.value)) {
      normalizedAttributes[key] = value.map(v => v.value);
    } else {
      normalizedAttributes[key] = value;
    }
  }

  return normalizedAttributes;
}

export default function BulkUpdateModal({
  open,
  onClose,
  onApply,
  attributes,
  size,
}: {
  open: boolean;
  onClose: () => void;
  onApply: (data: any, resetValues: any) => void;
  attributes: FieldsType[] | undefined | any;
  size?: string;
}) {
  const [selectedAttributes, setSelectedAttributes] = useState<string | undefined>();
  const [values, setValues] = useState<{ [key: string]: any }>({});
  const [loading, setLoading] = useState(false);
  let selectedAttrs: any = [];

  // Filter valid attributes
  for (const [category, items] of Object.entries(attributes || {}) as [string, FieldsType[]][]) {
    if (items) {
      selectedAttrs = [
        ...selectedAttrs,
        ...items.filter(
          attr => attr?.key === selectedAttributes && attr?.label && typeof attr.label === 'string',
        ),
      ];
    }
  }

  const handleValueChange = (key: string, val: any) => {
    setValues(prevValues => ({
      ...prevValues,
      [key]: val,
    }));
  };

  // Reset values when selected attribute changes
  useEffect(() => {
    if (selectedAttributes) {
      setValues({});
    }
  }, [selectedAttributes]);

  const resetModalValues = () => {
    setSelectedAttributes(undefined);
    setValues({});
    setLoading(false);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Bulk Update SKUs</DialogTitle>
      <DialogContent>
        <FormControl fullWidth sx={{ mt: 2 }}>
          <InputLabel>Attributes</InputLabel>
          <Select
            value={selectedAttributes || ''}
            onChange={e => {
              setSelectedAttributes(e.target.value || undefined);
            }}
            input={<OutlinedInput label="Attributes" />}
          >
            {Object.entries(attributes || {}).map(([category, items]: [string, any]) => [
              <ListSubheader sx={{ fontSize: '1.5vh' }} color="primary" key={category}>
                {category}
              </ListSubheader>,
              ...items
                .filter(
                  (attr: FieldsType) => attr?.key && attr?.label && typeof attr.label === 'string',
                )
                .map((attr: FieldsType) => (
                  <MenuItem prefix="-" key={attr.key} sx={{ marginLeft: '2%' }} value={attr.key}>
                    {attr.label}
                  </MenuItem>
                )),
            ])}
          </Select>
        </FormControl>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          {selectedAttrs?.map((attr: FieldsType) => {
            const value =
              values[attr.key] !== undefined
                ? values[attr.key]
                : attr.key === 'products_category'
                  ? ''
                  : attr.type === 'multi_select'
                    ? []
                    : '';
            return (
              <Grid size={{ xs: 12, sm: 6 }} key={attr.key}>
                <RenderValueField
                  attr={attr}
                  handleValueChange={handleValueChange}
                  value={value}
                  size={size}
                />
              </Grid>
            );
          })}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            resetModalValues();
            onClose();
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={() => {
            const normalizeData = normalizeAttributes(values);
            setLoading(true);
            onApply(normalizeData, resetModalValues);
          }}
          disabled={!selectedAttributes}
        >
          {loading ? <CircularProgress size={20} color="inherit" /> : 'Apply'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
