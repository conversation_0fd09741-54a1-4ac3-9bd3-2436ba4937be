import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  ButtonProps,
  Grid,
} from '@mui/material';
import FilterComponent from '../FilterDropDown';
import { fetchFilteredProductsSkus } from '@/actions';
import { setProducts, setProductsFilters } from '@/redux-store/stores/ProductSku.store';
import { useDispatch } from 'react-redux';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux-store';

interface FilterModalProps {
  open: boolean;
  onClose: () => void;
  columnsConfig?: any;
  children?: any;
  title?: string;
  buttons?: any[];
  storeName?: keyof RootState;
  applyFilters?: (normalizedFilters: any) => {};
}

type DefaultButtonConfig = {
  title: string | React.ReactNode;
  buttonProps: ButtonProps;
};

interface FilterableState {
  filters: any;
}

const FilterModal = ({
  open,
  onClose,
  columnsConfig,
  children,
  title,
  buttons,
  storeName = 'productSku',
  applyFilters,
}: FilterModalProps) => {
  const dispatch = useDispatch();
  const filters = useSelector((state: RootState) => (state[storeName] as FilterableState)?.filters);
  const [localFilters, setLocalFilters] = useState(
    filters.length === 0 ? [[{ attribute: '', operator: '', value: '' }]] : filters,
  );
  const [loading, setLoading] = useState(false);

  const isFilterButtonDisabled = () => {
    if (localFilters.length === 1 && localFilters[0].length === 1) {
      const filter = localFilters[0][0];
      return !filter.attribute || !filter.operator;
    }
    return false;
  };

  const isResetButtonDisabled = () => {
    if (localFilters.length === 1 && localFilters[0].length === 1) {
      const filter = localFilters[0][0];
      return !filter.attribute && !filter.operator && !filter.value;
    }
    return false;
  };

  const defaultButtons: DefaultButtonConfig[] = [
    {
      title: 'Reset Filters',
      buttonProps: {
        onClick: async () => {
          handleFilter(true);
        },
        color: 'secondary',
        disabled: isResetButtonDisabled(),
      },
    },
    {
      title: 'Cancel',
      buttonProps: {
        onClick: onClose,
        color: 'secondary',
      },
    },
    {
      title: loading ? <CircularProgress size={20} color="inherit" /> : 'Filter',
      buttonProps: {
        onClick: () => handleFilter(),
        variant: 'contained',
        disabled: isFilterButtonDisabled(),
      },
    },
  ];

  const handleChange = (index: number, key: string, value: any, subIndex: number) => {
    const newFilters = localFilters.map((group: any) => [...group]);

    if (key === 'attribute') {
      const mainKey = value.split('.')[0];

      switch (value) {
        case 'orderDate':
          newFilters[index][subIndex] = {
            ...newFilters[index][subIndex],
            [key]: value,
            operator: 'between',
            value: { start: '', end: '' },
          };
          break;
        case 'shopifyOrderNumber':
        case 'products_category':
        case 'priorities':
          newFilters[index][subIndex] = {
            ...newFilters[index][subIndex],
            [key]: value,
            operator: '',
            value: [],
          };
          break;
        case 'shopifyNativeVariant':
          newFilters[index][subIndex] = {
            ...newFilters[index][subIndex],
            [key]: value,
            value: { key: '', value: '' },
          };
          break;

        case 'parentSku.parentSku':
        case 'childSku.childSku':
          newFilters[index][subIndex] = {
            ...newFilters[index][subIndex],
            operator: '',
            [key]: value,
            value: [],
          };
          break;
        case 'customerFirstName':
        case 'customerLastName':
        case 'customerEmail':
          newFilters[index][subIndex] = {
            ...newFilters[index][subIndex],
            operator: '',
            [key]: value,
            value: '',
          };
          break;

        default:
          newFilters[index][subIndex] = {
            ...newFilters[index][subIndex],
            operator: '',
            [key]: value,
            value: null,
          };
      }
    } else {
      newFilters[index][subIndex] = {
        ...newFilters[index][subIndex],
        [key]: value,
      };
    }

    setLocalFilters(newFilters);
  };

  const handleAdd = (index: number = -1) => {
    const filtersCopy: any = JSON.parse(JSON.stringify(localFilters));

    if (index > -1) {
      filtersCopy[index] = [...filtersCopy[index], { attribute: '', operator: '', value: '' }];
    }
    setLocalFilters(
      index > -1 ? filtersCopy : [...filtersCopy, [{ attribute: '', operator: '', value: '' }]],
    );
  };

  const handleRemove = (index: number, subIndex: number) => {
    let localFiltersCopy = JSON.parse(JSON.stringify(localFilters));
    if (localFiltersCopy[index].length > 1) {
      localFiltersCopy[index] = localFiltersCopy[index].filter(
        (_: never, i: number) => i !== subIndex,
      );
    } else localFiltersCopy = localFiltersCopy?.filter((_: never, i: number) => i !== index);
    setLocalFilters([...localFiltersCopy]);
  };

  async function handleFilter(isReset: boolean = false) {
    try {
      if (isReset) setLocalFilters([[{ attribute: '', operator: '', value: '' }]]);

      const processedFilters = localFilters.map((group: any[]) =>
        group.map(filter => {
          if (filter.attribute === 'orderDate') {
            return {
              ...filter,
              operator: 'between',
              value: filter.value || { start: '', end: '' },
            };
          }
          return filter;
        }),
      );

      // Validate filters before submitting
      const validationErrors = [];
      for (const group of processedFilters) {
        for (const filter of group) {
          // Skip empty filters
          if (!filter.attribute) continue;

          // Special validation for orderDate
          if (filter.attribute === 'orderDate') {
            // Check if both start and end dates are provided
            if (
              !filter.value ||
              typeof filter.value !== 'object' ||
              !filter.value.start ||
              !filter.value.end
            ) {
              validationErrors.push('Both start and end dates are required for date range filter');
              continue;
            }

            // Check if start date is before end date
            const startDate = new Date(filter.value.start);
            const endDate = new Date(filter.value.end);
            if (startDate > endDate) {
              validationErrors.push('Start date cannot be after end date');
            }
          } else if (filter.attribute === 'shopifyOrderNumber') {
            // For shopifyOrderNumber, check if at least one value is selected
            if (!Array.isArray(filter.value) || filter.value.length === 0) {
              validationErrors.push('At least one order number must be selected');
              continue;
            }
          } else {
            // For non-date fields, check if operator and value are provided
            if (!filter.operator) {
              validationErrors.push(`Operator is required for ${filter.attribute}`);
              continue;
            }

            if (filter.value === undefined || filter.value === null || filter.value === '') {
              validationErrors.push(`Value is required for ${filter.attribute}`);
            }
          }
        }
      }

      // If there are validation errors, show them and return
      if (validationErrors.length > 0) {
        toast.error(validationErrors[0]);
        return;
      }

      const normalizedFilters = processedFilters
        .map((group: any[]) =>
          group
            .filter(filter => filter.attribute)
            .map(filter => {
              // Handle date range for orderDate
              if (filter.attribute === 'orderDate') {
                return {
                  ...filter,
                  operator: 'between',
                  value: {
                    start: filter.value.start,
                    end: filter.value.end,
                  },
                };
              }

              // Handle multi-select values
              if (Array.isArray(filter.value) && typeof filter.value[0] === 'object') {
                return {
                  ...filter,
                  value: filter.value.map((v: any) => v?.value),
                };
              }
              return filter;
            }),
        )
        .filter((group: string | any[]) => group.length > 0);

      setLoading(true);

      if (applyFilters) {
        await applyFilters(isReset ? [] : normalizedFilters);
        // Only close the modal after successful filter application
        onClose();
      } else {
        const data = await fetchFilteredProductsSkus({
          filters: isReset ? [] : normalizedFilters,
          pageNo: 1,
          limit: 25,
        });
        dispatch(setProductsFilters(isReset ? [] : localFilters));
        toast.success('Filters applied successfully!');
        dispatch(setProducts(data));
        onClose();
      }
    } catch (err: any) {
      console.error('Unexpected Error:', err);
      toast.error(err.message || 'Something went wrong!');
    } finally {
      setLoading(false);
    }
  }

  // Build API query or perform search here
  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{title ?? 'Filter'}</DialogTitle>
      <DialogContent>
        {children ? (
          children
        ) : (
          <FilterComponent
            filters={localFilters}
            handleAdd={handleAdd}
            handleRemove={handleRemove}
            handleChange={handleChange}
            columnsConfig={columnsConfig}
          />
        )}
      </DialogContent>
      <DialogActions>
        {(buttons ?? defaultButtons).map(({ title, buttonProps }, index) => (
          <Button key={index} {...buttonProps}>
            {title}
          </Button>
        ))}
      </DialogActions>
    </Dialog>
  );
};

export default FilterModal;
