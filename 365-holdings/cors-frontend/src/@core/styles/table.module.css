.table {
  inline-size: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  white-space: nowrap;

  [align='right'] > * {
    text-align: end;
  }
  [align='center'] > * {
    text-align: center;
  }

  thead {
    text-transform: uppercase;
    color: var(--mui-palette-text-primary);

    th {
      font-weight: 500;
      font-size: 0.8125rem;
      letter-spacing: 0.2px;
      line-height: 1.8462;
      text-align: start;
      block-size: 56px;
      background-color: var(--mui-palette-customColors-tableHeaderBg);
      &:not(:first-of-type):not(:last-of-type) {
        padding-block: 0.5rem;
        padding-inline: 1rem;
      }
      &:first-of-type {
        &:not(:has(input[type='checkbox'])) {
          padding-block: 0.5rem;
          padding-inline: 1.25rem 1rem;
        }
        &:has(input[type='checkbox']) {
          padding-inline-start: 0.6875rem;
        }
      }
      &:last-of-type {
        padding-block: 0.5rem;
        padding-inline: 1rem 1.25rem;
      }
    }
  }

  tbody {
    color: var(--mui-palette-text-secondary);

    th,
    td {
      font-size: 0.9375rem;
      line-height: 1.4667;
      block-size: 50px;
      &:not(:first-of-type):not(:last-of-type) {
        padding-block: 0.5rem;
        padding-inline: 1rem;
      }
      &:first-of-type {
        &:not(:has(input[type='checkbox'])) {
          padding-block: 0.5rem;
          padding-inline: 1.25rem 1rem;
        }
        &:has(input[type='checkbox']) {
          padding-inline-start: 0.6875rem;
        }
      }
      &:last-of-type {
        padding-block: 0.5rem;
        padding-inline: 1rem 1.25rem;
      }
    }

    tr:not(:last-child) {
      border-block-end: 1px solid var(--border-color);
    }
  }
}

.cellWithInput input {
  inline-size: 100%;
  background-color: transparent;
  font-size: inherit;
  color: inherit;
  border-radius: var(--mui-shape-customBorderRadius-sm);
  padding-block: 6px;
  padding-inline: 10px;
  margin-inline: -10px;

  &:focus-visible {
    outline: 1px solid var(--mui-palette-primary-main);
  }
}

/* Top scroll bar styles */
.topScrollBar {
  border-bottom: 2px solid var(--border-color);
  background: linear-gradient(90deg, var(--mui-palette-background-paper) 0%, var(--mui-palette-primary-light) 50%, var(--mui-palette-background-paper) 100%);
  min-height: 20px;
  max-height: 20px;
  cursor: grab;
  position: relative;
  transition: all 0.2s ease;
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.topScrollBar:hover {
  background: linear-gradient(90deg, var(--mui-palette-action-hover) 0%, var(--mui-palette-primary-main) 50%, var(--mui-palette-action-hover) 100%);
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.topScrollBar:active {
  cursor: grabbing;
  background: linear-gradient(90deg, var(--mui-palette-action-selected) 0%, var(--mui-palette-primary-dark) 50%, var(--mui-palette-action-selected) 100%);
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.topScrollBar.scrolling {
  background: linear-gradient(90deg, var(--mui-palette-primary-light) 0%, var(--mui-palette-primary-main) 50%, var(--mui-palette-primary-light) 100%);
  opacity: 1;
  box-shadow: 0 0 12px rgba(25, 118, 210, 0.4);
}

/* Show scroll indicator when content is scrollable */
.topScrollBar:has(+ div[style*="overflow-x: auto"]:hover) {
  opacity: 1;
}

/* Custom scrollbar styling for both top and bottom */
.topScrollBar::-webkit-scrollbar,
.bottomScrollBar::-webkit-scrollbar {
  height: 10px;
}

.topScrollBar::-webkit-scrollbar-track,
.bottomScrollBar::-webkit-scrollbar-track {
  background: var(--mui-palette-background-paper);
  border-radius: 8px;
  margin: 2px 0;
}

.topScrollBar::-webkit-scrollbar-thumb,
.bottomScrollBar::-webkit-scrollbar-thumb {
  background: var(--mui-palette-primary-main);
  border-radius: 8px;
  transition: background-color 0.2s ease;
  border: 2px solid var(--mui-palette-background-paper);
}

.topScrollBar::-webkit-scrollbar-thumb:hover,
.bottomScrollBar::-webkit-scrollbar-thumb:hover {
  background: var(--mui-palette-primary-dark);
}

.topScrollBar::-webkit-scrollbar-thumb:active,
.bottomScrollBar::-webkit-scrollbar-thumb:active {
  background: var(--mui-palette-primary-dark);
}

/* For Webkit browsers (Chrome, Edge, Safari) */
.overflow-x-auto::-webkit-scrollbar {
  height: 14px;
  background: transparent;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #8C57FF;         /* Exact sidebar/menu color */
  border-radius: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #28243D;         /* Exact sidebar/menu background */
  border-radius: 8px;
}

/* For Firefox */
.overflow-x-auto {
  scrollbar-color: #8C57FF #28243D; /* thumb color, track color */
  scrollbar-width: thick;
}

/* DataTable Custom Scrollbar Styles */
.dataTableScrollbar {
  scrollbar-width: auto;
  scrollbar-color: var(--mui-palette-primary-main) transparent;
  cursor: pointer;
}

.dataTableScrollbar::-webkit-scrollbar {
  height: 8px;
}

.dataTableScrollbar::-webkit-scrollbar-track {
  background: transparent;
  cursor: pointer;
}

.dataTableScrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(
    270deg,
    var(--mui-palette-primary-main),
    rgb(197, 171, 255) 100%
  );
  border-radius: 4px;
  cursor: grab;
}

.dataTableScrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    270deg,
    rgb(197, 171, 255),
    var(--mui-palette-primary-main) 100%
  );
  cursor: grab;
}

.dataTableScrollbar::-webkit-scrollbar-thumb:active {
  cursor: grabbing;
}
