import { Box, Card, CardContent, Divider, Typography } from '@mui/material';
import SettingsTabs from './components/SettingsTabs';

const SettingsView = () => {
  return (
    <Card>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
          }}
        >
          <Typography variant="h5">Settings</Typography>
        </Box>

        <Divider sx={{ mb: 4 }} />

        <SettingsTabs />
      </CardContent>
    </Card>
  );
};

export default SettingsView;
