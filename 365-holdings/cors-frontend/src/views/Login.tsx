'use client';
import { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import { signIn, useSession, getSession } from 'next-auth/react';
import { Controller, useForm, SubmitHandler } from 'react-hook-form';
import { valibotResolver } from '@hookform/resolvers/valibot';
import { object, minLength, string, email, pipe, nonEmpty } from 'valibot';
import type { InferInput } from 'valibot';
import type { Mode } from '@core/types';
import Logo from '@components/layout/shared/Logo';
import Illustrations from '@components/Illustrations';
import themeConfig from '@configs/themeConfig';
import { useImageVariant } from '@core/hooks/useImageVariant';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import { toast } from 'react-toastify';

type FormData = InferInput<typeof schema>;

const schema = object({
  email: pipe(
    string(),
    minLength(1, 'This field is required'),
    email('Please enter a valid email address'),
  ),
  password: pipe(string(), nonEmpty('This field is required')),
});

const Login = ({ mode }: { mode: Mode }) => {
  const { data: session, status } = useSession();
  const searchParams = useSearchParams();
  const [hasRedirected, setHasRedirected] = useState(false);
  const [isPasswordShown, setIsPasswordShown] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const emailInputRef = useRef<HTMLInputElement>(null);
  const passwordInputRef = useRef<HTMLInputElement>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: valibotResolver(schema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const handleClickShowPassword = () => {
    setIsPasswordShown(!isPasswordShown);
  };

  const darkImg = '/images/pages/auth-v2-mask-dark.png';
  const lightImg = '/images/pages/auth-v2-mask-light.png';
  const authBackground = useImageVariant(mode, lightImg, darkImg);

  // Handle redirect when session becomes authenticated
  useEffect(() => {
    if (status === 'authenticated' && session?.user && !hasRedirected) {
      const redirectURL = searchParams.get('redirectTo') || '/home';
      setHasRedirected(true);
      window.location.href = redirectURL;
    }
  }, [status, session, hasRedirected, searchParams]);

  const onSubmit: SubmitHandler<FormData> = async (data: FormData) => {
    setIsLoading(true);

    try {
      const res = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (res && res.ok && !res.error) {
        toast.success('Login successful! Redirecting...');
      } else {
        toast.error('Wrong credentials. Please check your email and password.');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (status === 'loading' || (status === 'authenticated' && hasRedirected)) {
    return (
      <div className="flex flex-col justify-center items-center min-bs-[100dvh] relative p-6">
        <Card className="flex flex-col sm:is-[450px]">
          <CardContent className="p-6 sm:!p-12 flex flex-col items-center gap-4">
            <Logo />
            <CircularProgress />
            <Typography>{status === 'loading' ? 'Loading...' : 'Redirecting...'}</Typography>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-center items-center min-bs-[100dvh] relative p-6">
      <Card className="flex flex-col sm:is-[450px]">
        <CardContent className="p-6 sm:!p-12">
          <Link href="/" className="flex justify-center items-center mbe-6">
            <Logo />
          </Link>
          <div className="flex flex-col gap-5">
            <div>
              <Typography variant="h4">{`Welcome to ${themeConfig.templateName}!👋🏻`}</Typography>
              <Typography className="mbs-1">
                Please sign-in to your account and start the PIMS
              </Typography>
            </div>
            <form
              noValidate
              action={() => {}}
              autoComplete="off"
              onSubmit={handleSubmit(onSubmit)}
              className="flex flex-col gap-5"
            >
              <Controller
                name="email"
                control={control}
                rules={{ required: true }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    autoFocus
                    type="email"
                    label="Email"
                    autoComplete="email"
                    slotProps={{
                      inputLabel: { shrink: true },
                    }}
                    inputRef={emailInputRef}
                    onChange={e => {
                      field.onChange(e.target.value);
                    }}
                    {...(errors.email && {
                      error: true,
                      helperText: errors.email.message,
                    })}
                    sx={{
                      '& .MuiFormHelperText-root': {
                        marginTop: '1.25rem',
                      },
                      '& input:-webkit-autofill': {
                        WebkitBoxShadow:
                          '0 0 0 100px var(--mui-palette-background-paper) inset !important',
                        WebkitTextFillColor: 'var(--mui-palette-text-primary) !important',
                      },
                    }}
                  />
                )}
              />
              <Controller
                name="password"
                control={control}
                rules={{ required: true }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Password"
                    id="login-password"
                    inputRef={passwordInputRef}
                    autoComplete="current-password"
                    slotProps={{
                      inputLabel: { shrink: true },
                      input: {
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              size="small"
                              edge="end"
                              onClick={handleClickShowPassword}
                              onMouseDown={e => e.preventDefault()}
                              aria-label="toggle password visibility"
                            >
                              <i className={isPasswordShown ? 'ri-eye-off-line' : 'ri-eye-line'} />
                            </IconButton>
                          </InputAdornment>
                        ),
                      },
                    }}
                    type={isPasswordShown ? 'text' : 'password'}
                    onChange={e => {
                      field.onChange(e.target.value);
                    }}
                    sx={{
                      '& .MuiFormHelperText-root': {
                        marginTop: '1.25rem',
                      },
                      '& input:-webkit-autofill': {
                        WebkitBoxShadow:
                          '0 0 0 100px var(--mui-palette-background-paper) inset !important',
                        WebkitTextFillColor: 'var(--mui-palette-text-primary) !important',
                      },
                    }}
                    {...(errors.password && {
                      error: true,
                      helperText: errors.password.message,
                    })}
                  />
                )}
              />
              <div className="flex justify-end items-center">
                <Typography
                  className="text-end"
                  color="primary.main"
                  component={Link}
                  href="/forgot-password"
                >
                  Forgot password?
                </Typography>
              </div>
              <Button fullWidth variant="contained" type="submit" disabled={isLoading}>
                {isLoading ? <CircularProgress size={24} color="inherit" /> : 'Log In'}
              </Button>
            </form>
          </div>
        </CardContent>
      </Card>
      <Illustrations maskImg={{ src: authBackground }} />
    </div>
  );
};

export default Login;
