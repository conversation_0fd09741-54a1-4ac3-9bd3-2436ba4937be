'use client'
import { AppDispatch } from '@/redux-store'
import { fetchTableConfigs } from '@/redux-store/stores/common.store'
import type { Mode } from '@core/types'
import { Typography } from '@mui/material'
import Grid from '@mui/material/Grid2'
import { useEffect } from 'react'
import { useDispatch } from 'react-redux'

const HomePageWrapper = ({ mode }: { mode: Mode }) => {
  const dispatch = useDispatch<AppDispatch>();
  useEffect(()=>{
    dispatch(fetchTableConfigs())

  },[])
  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }} className="flex justify-between items-center mb-2">
        <Typography variant="h4">Home page</Typography>
      </Grid>
    </Grid>
  )
}

export default HomePageWrapper
