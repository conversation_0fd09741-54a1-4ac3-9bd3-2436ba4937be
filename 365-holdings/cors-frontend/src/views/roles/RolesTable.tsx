'use client';
import { useState, useMemo, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'react-toastify';
import apiClient from '@/utils/axios';
import { useRouter } from 'nextjs-toploader/app';
import Chip from '@mui/material/Chip';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import { createColumnHelper } from '@tanstack/react-table';
import type { ColumnDef } from '@tanstack/react-table';
import DataTable from '@/components/Datatable';
import { RolesData } from '@/types/roleTypes';
import { RoleProtected } from '@/components/ProtectedRoleWrapper';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import { useAbility } from '@/libs/casl/AbilityContext';
import { Switch } from '@mui/material';
import useApiCall from '@/hooks/useApiCall';

const RolesTable = ({
  tableData,
  count,
  onDataUpdate,
}: {
  tableData?: RolesData[];
  count: number;
  onDataUpdate?: (data: RolesData[], newCount: number) => void;
}) => {
  const { data: session } = useSession();
  const router = useRouter();
  const [data, setData] = useState<RolesData[]>(tableData || []);
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<RolesData>();
  const [selectedRole, setSelectedRole] = useState<RolesData | null>(null);
  const ability = useAbility();
  const { isLoading: isRolesLoading, makeRequest: refetchRolesData } = useApiCall(
    '/roles',
    'get',
    false, // tryOnRender
    {},
  );
  const {
    isLoading: isRolesLoadingStatus,
    makeRequest: updateRoleStatus,
    error: rolesErrorStatus,
  } = useApiCall('/roles/:id', 'patch', false, {});

  const { isLoading: isRolesDeleteLoading, makeRequest: deleteRole } = useApiCall(
    '/roles/:id',
    'delete',
    false, // tryOnRender
    {},
  );
  const handleNavigation = (e: React.MouseEvent, path: string) => {
    if (e.ctrlKey || e.metaKey) {
      window.open(path, '_blank');
    } else {
      router.push(path);
    }
  };
  useEffect(() => {
    setData(tableData || []);
  }, [tableData]);

  const fetchRolesData = async (sortParam?: string) => {
    const searchParams = new URLSearchParams(window.location.search);
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '25';
    const q = searchParams.get('q') || '';
    const fq = searchParams.get('fq') || '';

    const response = await refetchRolesData({
      queryParams: {
        page,
        limit,
        q,
        fq,
        sort: sortParam || '',
      },
    });

    setData(response?.data || []);
    // Update the count in parent component
    if (onDataUpdate) {
      onDataUpdate(response?.data || [], response?.count || 0);
    }
  };

  const handleStatusChange = (role: RolesData) => {
    setSelectedRole(role);
    setOpenStatusModal(true);
  };

  const handleConfirmStatusChange = async () => {
    if (!selectedRole || !session?.user?.token) return;

    const response = await updateRoleStatus({
      urlParams: {
        id: selectedRole.id as number,
      },
      body: {
        id: selectedRole.id,
        isActive: !selectedRole.isActive,
      },
    });
    if (response) {
      const searchParams = new URLSearchParams(window.location.search);
      const sort = searchParams.get('sort') || '';
      fetchRolesData(sort);
      toast.success(`Role ${selectedRole.isActive ? 'deactivated' : 'activated'} successfully`);
      setOpenStatusModal(false);
    }
  };

  const handleDeleteRole = (role: RolesData) => {
    setRoleToDelete(role);
    setOpenDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!roleToDelete || !session?.user?.token) return;

    const response = await deleteRole({
      urlParams: {
        id: roleToDelete.id as number,
      },
    });
    if (response) {
      setData(prevData => prevData.filter(role => role.id !== roleToDelete.id));
      toast.success(`Role "${roleToDelete.name}" deleted successfully`);
      setOpenDeleteModal(false);
    }
  };

  const columnHelper = createColumnHelper<RolesData>();

  const columns = useMemo<ColumnDef<RolesData>[]>(
    () => [
      columnHelper.accessor('name', {
        enableColumnFilter: true,
        header: 'Role',
        cell: info => <Typography color="text.primary">{info.getValue()}</Typography>,
        enableSorting: true,
      }) as ColumnDef<RolesData>,
      columnHelper.accessor('permissionCount', {
        header: 'Permissions',
        enableColumnFilter: true,
        meta: { filterType: 'num' },
        cell: info => {
          return <Chip label={`${info.getValue()} Actions`} size="small" color="primary" />;
        },
        enableSorting: true,
      }) as ColumnDef<RolesData>,
      columnHelper.accessor('isActive', {
        header: 'Status',
        cell: ({ row }) => {
          const isActive = row.original?.isActive;
          const statusText = isActive ? 'Active' : 'Inactive';
          const statusColor = isActive ? 'success' : 'error';

          return (
            <div className="flex items-center gap-3">
              <Chip
                variant="tonal"
                label={statusText}
                size="small"
                color={statusColor}
                className="capitalize"
              />
            </div>
          );
        },
        enableColumnFilter: true,
        meta: {
          filterType: 'select',
          isBoolean: true,
          booleanOptions: { Active: true, Inactive: false },
        },

        enableSorting: true,
      }) as ColumnDef<RolesData>,
      columnHelper.display({
        id: 'action',
        header: 'Actions',
        enableColumnFilter: false,
        cell: ({ row }) => {
          const isOwnerRole = row.original.name.toLowerCase() === 'owner';

          return (
            <div className="flex items-center">
              <Tooltip title="View Role">
                <span>
                  <IconButton
                    onClick={e => {
                      handleNavigation(e, `/roles/view/${row.original.id}`);
                    }}
                    disabled={
                      isOwnerRole ||
                      !ability?.can(Actions.ViewRoleDetail, ActionsTarget.RoleManagement)
                    }
                    sx={{ opacity: isOwnerRole ? 0.5 : 1 }}
                  >
                    <i className="ri-eye-line text-textSecondary" />
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title={isOwnerRole ? 'Owner role cannot be edited' : 'Edit Role'}>
                <span>
                  <IconButton
                    onClick={e => {
                      if (!isOwnerRole) {
                        handleNavigation(e, `/roles/edit/${row.original.id}`);
                      }
                    }}
                    disabled={
                      isOwnerRole || !ability?.can(Actions.EditRole, ActionsTarget.RoleManagement)
                    }
                    sx={{ opacity: isOwnerRole ? 0.5 : 1 }}
                  >
                    <i className="ri-edit-line text-textSecondary" />
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title={isOwnerRole ? 'Owner role cannot be deleted' : 'Delete Role'}>
                <span>
                  <IconButton
                    onClick={() => {
                      if (!isOwnerRole) {
                        handleDeleteRole(row?.original);
                      }
                    }}
                    sx={{ ml: 1, opacity: isOwnerRole ? 0.5 : 1 }}
                    disabled={
                      isOwnerRole || !ability?.can(Actions.DeleteRole, ActionsTarget.RoleManagement)
                    }
                  >
                    <i className="ri-delete-bin-line" />
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip
                title={
                  isOwnerRole
                    ? 'Owner role cannot be deactivated'
                    : row.original.isActive
                      ? 'Deactivate Role'
                      : 'Activate Role'
                }
              >
                <span>
                  <Switch
                    checked={row.original.isActive}
                    onChange={() => {
                      if (!isOwnerRole) {
                        handleStatusChange(row.original);
                      }
                    }}
                    disabled={
                      isOwnerRole ||
                      !ability?.can(
                        row.original.isActive ? Actions.DeactivateRole : Actions.ActivateRole,
                        ActionsTarget.RoleManagement,
                      )
                    }
                    sx={{ ml: 1, opacity: isOwnerRole ? 0.5 : 1 }}
                  />
                </span>
              </Tooltip>
            </div>
          );
        },
      }),
    ],
    [router],
  );

  return (
    <>
      <DataTable
        columns={columns}
        loading={isRolesLoading}
        data={data}
        endpoint="roles"
        totalCount={count}
        globalSearch
        onSortingUpdate={(columnId, direction) => {
          const params = new URLSearchParams(window.location.search);
          if (direction) {
            params.set('sort', `${columnId}:${direction}`);
          } else {
            params.delete('sort');
          }
          params.set('page', '1'); // Reset to page 1 when sorting changes
          window.history.pushState({}, '', `?${params.toString()}`);

          // Fetch new data without page reload
          const sortParam = direction ? `${columnId}:${direction}` : undefined;
          fetchRolesData(sortParam);
        }}
      />

      <ConfirmationDialog
        open={openStatusModal}
        title={`${selectedRole?.isActive ? 'Deactivate' : 'Activate'} Role`}
        message={
          selectedRole && (
            <>
              Are you sure you want to {selectedRole.isActive ? 'deactivate' : 'activate'} the role
              "{selectedRole.name}"?
              {selectedRole.isActive
                ? ' This will prevent users with this role from accessing certain features.'
                : ' This will allow users with this role to access the associated features.'}
            </>
          )
        }
        confirmLabel="Confirm"
        confirmColor={selectedRole?.isActive ? 'error' : 'success'}
        loading={isRolesLoadingStatus}
        onConfirm={handleConfirmStatusChange}
        onCancel={() => setOpenStatusModal(false)}
      />

      <ConfirmationDialog
        open={openDeleteModal}
        title="Delete Role"
        message={
          roleToDelete && (
            <>
              Are you sure you want to delete the role "{roleToDelete.name}"? This action cannot be
              undone and will permanently remove the role and its permissions.
            </>
          )
        }
        confirmLabel="Delete"
        confirmColor="error"
        loading={isRolesDeleteLoading}
        onConfirm={handleConfirmDelete}
        onCancel={() => setOpenDeleteModal(false)}
      />
    </>
  );
};

export default RolesTable;
