'use client';
import { useEffect, useState } from 'react';
import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';
import RolesTable from './RolesTable';
import { UsersType } from '@/types/userTypes';
import { RolesData } from '@/types/roleTypes';
import Button from '@/components/Button';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import { useRouter } from 'nextjs-toploader/app';

const Roles = ({ userData, count }: { userData?: RolesData[]; count: number }) => {
  const router = useRouter();
  const [currentData, setCurrentData] = useState<RolesData[]>(userData || []);
  const [currentCount, setCurrentCount] = useState<number>(count);

  const handleAddRole = () => {
    router.push('/roles/add');
  };
  useEffect(() => {
    setCurrentData(userData || []);
    setCurrentCount(count);
  }, [userData, count]);

  const handleDataUpdate = (data: RolesData[], newCount: number) => {
    setCurrentData(data);
    setCurrentCount(newCount);
  };

  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }} className="flex justify-between items-center mb-2">
        <Typography variant="h4">Roles List</Typography>
        <Button
          title="Add Role"
          variant="contained"
          size="medium"
          ButtonAction={Actions.CreateRole}
          actionTarget={ActionsTarget.RoleManagement}
          onClick={handleAddRole}
        />
      </Grid>

      <Grid size={{ xs: 12 }}>
        <RolesTable tableData={currentData} count={currentCount} onDataUpdate={handleDataUpdate} />
      </Grid>
    </Grid>
  );
};

export default Roles;
