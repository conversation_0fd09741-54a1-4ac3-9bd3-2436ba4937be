"use client";

import { useState } from "react";
import { useParams, useSearchParams } from "next/navigation";
import Materio<PERSON>ogo from "@core/svg/Logo";

import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import classnames from "classnames";
import type { Mode } from "@core/types";
import { i18n, type Locale } from "@configs/i18n";
import Logo from "@components/layout/shared/Logo";
import Illustrations from "@components/Illustrations";
import { useImageVariant } from "@core/hooks/useImageVariant";
import { useSettings } from "@core/hooks/useSettings";
import { getLocalizedUrl } from "@/utils/i18n";
import Link from "next/link";
import IconButton from "@mui/material/IconButton";
import { validatePasswordWithError } from "@/utils/validation";

const ResetPasswordV2 = ({ mode }: { mode: Mode }) => {
  const darkImg = "/images/pages/auth-v2-mask-dark.png";
  const lightImg = "/images/pages/auth-v2-mask-light.png";
  const darkIllustration = "/images/illustrations/characters/5.png";
  const lightIllustration = "/images/illustrations/characters/5.png";
  const borderedDarkIllustration =
    "/images/illustrations/auth/v2-reset-password-dark-border.png";
  const borderedLightIllustration =
    "/images/illustrations/auth/v2-reset-password-light-border.png";

  const { lang: locale } = useParams();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const authBackground = useImageVariant(mode, lightImg, darkImg);
  const { settings } = useSettings();

  const characterIllustration = useImageVariant(
    mode,
    lightIllustration,
    darkIllustration,
    borderedLightIllustration,
    borderedDarkIllustration
  );

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);

    const { isValid, error } = validatePasswordWithError(newPassword);
    setPasswordError(error || "");

    // Check confirm password match if confirm password is not empty
    if (confirmPassword) {
      if (newPassword !== confirmPassword) {
        setConfirmPasswordError("Passwords do not match");
      } else {
        setConfirmPasswordError("");
      }
    }
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newConfirmPassword = e.target.value;
    setConfirmPassword(newConfirmPassword);

    if (!newConfirmPassword) {
      setConfirmPasswordError("");
    } else if (newConfirmPassword !== password) {
      setConfirmPasswordError("Passwords do not match");
    } else {
      setConfirmPasswordError("");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!token) {
      setError("Invalid or expired reset token");
      return;
    }

    const { isValid, error } = validatePasswordWithError(password);
    if (!isValid) {
      setPasswordError(error || "");
      return;
    }

    if (password !== confirmPassword) {
      setConfirmPasswordError("Passwords do not match");
      return;
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, "");
      const response = await fetch(`${apiUrl}/auth/reset-password`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token,
          newPassword: password,
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || "Failed to reset password");
      }

      setSuccess(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to reset password");
    }
  };

  return (
    <div className="flex bs-full justify-center">
      <div
        className={classnames(
          "flex bs-full items-center justify-center flex-1 min-bs-[100dvh] relative p-6 max-md:hidden",
          {
            "border-ie": settings.skin === "bordered",
          }
        )}>
        <Link href="/" className="flex justify-center items-center mbe-6">
          <div style={{ transform: "scale(5)" }}>
            <MaterioLogo />
          </div>
        </Link>
        <Illustrations
          image1={{ src: "/images/illustrations/objects/tree-2.png" }}
          image2={null}
          maskImg={{ src: authBackground }}
        />
      </div>
      <div className="flex justify-center items-center bs-full bg-backgroundPaper !min-is-full p-6 md:!min-is-[unset] md:p-12 md:is-[480px]">
        <Link
          href={getLocalizedUrl("/", (locale as Locale) || i18n.defaultLocale)}
          className="absolute block-start-5 sm:block-start-[38px] inline-start-6 sm:inline-start-[38px]">
          <Logo />
        </Link>
        <div className="flex flex-col gap-5 is-full sm:is-auto md:is-full sm:max-is-[400px] md:max-is-[unset]">
          <div>
            <Typography variant="h4">Reset Password 🔒</Typography>
            <Typography className="mbs-1">
              Enter your new password below
            </Typography>
          </div>
          {success ? (
            <div className="text-center">
              <Typography color="success.main" className="mbs-2">
                Password has been reset successfully!
              </Typography>
              <Link href="/login" className="text-primary">
                Return to Login
              </Link>
            </div>
          ) : (
            <form
              noValidate
              autoComplete="off"
              onSubmit={handleSubmit}
              className="flex flex-col gap-5">
              <TextField
                autoFocus
                fullWidth
                type={showPassword ? "text" : "password"}
                label="New Password"
                value={password}
                onChange={handlePasswordChange}
                error={!!passwordError}
                helperText={passwordError}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end">
                      {showPassword ? (
                        <i className="ri-eye-off-line" />
                      ) : (
                        <i className="ri-eye-line" />
                      )}
                    </IconButton>
                  ),
                }}
              />
              <TextField
                fullWidth
                type={showConfirmPassword ? "text" : "password"}
                label="Confirm Password"
                value={confirmPassword}
                onChange={handleConfirmPasswordChange}
                error={!!confirmPasswordError}
                helperText={confirmPasswordError}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      edge="end">
                      {showConfirmPassword ? (
                        <i className="ri-eye-off-line" />
                      ) : (
                        <i className="ri-eye-line" />
                      )}
                    </IconButton>
                  ),
                }}
              />
              <Button
                fullWidth
                variant="contained"
                type="submit"
                disabled={!!passwordError || !!confirmPasswordError}>
                Reset Password
              </Button>
              <Typography
                className="flex justify-center items-center"
                color="primary.main">
                <Link href="/login" className="flex items-center">
                  <i className="ri-arrow-left-s-line" />
                  <span>Back to Login</span>
                </Link>
              </Typography>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordV2;
