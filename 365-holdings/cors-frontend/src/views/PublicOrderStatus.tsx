'use client';
import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Alert, CircularProgress, Paper } from '@mui/material';
import { PublicOrderStatus } from '@/types/public-order-status.types';
import OrderDetailsCard from '@/components/PublicOrderStatus/OrderDetailsCard';
import LineItemsTable from '@/components/PublicOrderStatus/LineItemsTable';
import useApiCall from '@/hooks/useApiCall';
import DataError from '@/components/DataError';

interface PublicOrderStatusPageProps {
  shopifyOrderNumber?: string;
  customerEmail?: string;
  lineItemId?: string;
}

const PublicOrderStatusPage: React.FC<PublicOrderStatusPageProps> = ({
  shopifyOrderNumber,
  customerEmail,
}) => {
  // Use the API hook for refreshing order data
  const {
    data: orderData,
    isLoading: orderDataLoading,
    error: orderDataError,
    makeRequest: getOrderData,
  } = useApiCall<PublicOrderStatus>('/order-tracking', 'post', false, {}, 'public');

  useEffect(() => {
    if (!shopifyOrderNumber || !customerEmail) return;

    getOrderData({
      body: {
        orderNumber: shopifyOrderNumber,
        email: customerEmail,
      },
    });
  }, []);
  // Function to refresh order data using the hook
  const handleRefreshOrderData = () => {
    if (!shopifyOrderNumber || !customerEmail) return;

    getOrderData({
      body: {
        orderNumber: shopifyOrderNumber,
        email: customerEmail,
      },
    });
  };
  if (orderDataError) {
    return <DataError isActionAllowed={false} />;
  }
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box textAlign="center" mb={4}>
        <Typography variant="h3" component="h1" gutterBottom color="primary">
          Order Status
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Track your order progress and request updates
        </Typography>
      </Box>

      {orderDataLoading && (
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          py={6}
          gap={2}
        >
          <CircularProgress size={60} thickness={4} />
          <Typography variant="h6" color="text.secondary">
            Loading order details...
          </Typography>
        </Box>
      )}

      {/* Error State */}
      {orderDataError && !orderDataLoading && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {orderDataError.message}
        </Alert>
      )}

      {/* Order Data */}
      {orderData && !orderDataLoading && 'lineItems' in orderData && (
        <Box>
          <OrderDetailsCard orderData={orderData as PublicOrderStatus} />
          <LineItemsTable
            lineItems={(orderData as PublicOrderStatus).lineItems}
            orderData={orderData as PublicOrderStatus}
            onOrderUpdate={handleRefreshOrderData}
          />
        </Box>
      )}

      {/* Help Section */}
      <Paper elevation={1} sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Need Help?
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          If you're having trouble with your order or need assistance, please contact our customer
          support team.
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • Make sure you're providing the correct order information
        </Typography>
      </Paper>
    </Container>
  );
};

export default PublicOrderStatusPage;
function refreshOrderData(arg0: { body: { orderNumber: string; email: string } }) {
  throw new Error('Function not implemented.');
}
