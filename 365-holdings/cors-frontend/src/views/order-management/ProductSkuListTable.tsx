import React, { useEffect, useState, useMemo, useRef } from 'react';
import {
  Typography,
  Checkbox,
  Tooltip,
  DialogContentText,
  IconButton,
  Chip,
  Switch,
} from '@mui/material';
import { useRouter } from 'nextjs-toploader/app';
import { useSearchParams } from 'next/navigation';

import { useSettings } from '@core/hooks/useSettings';
import { createColumnHelper, ColumnDef } from '@tanstack/react-table';
import apiClient from '@/utils/axios';
import DataTable from '@/components/Datatable';
import { toast } from 'react-toastify';
import { Product } from '@/types/productTypes';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import { useAbility } from '@/libs/casl/AbilityContext';
import { useSelector, useDispatch } from 'react-redux';
import { AppDispatch, RootState } from '@/redux-store';
import { setProductsFilters, setProducts } from '@/redux-store/stores/ProductSku.store';
import { FieldsType } from '@/redux-store/stores/common.store';
import FilterModal from '@/components/Modals/FilterModal';
import CustomIconButton from '@/components/CustonIconButton';
import { updateProduct, fetchFilteredProductsSkusAdvanced } from '@/actions/products';
import { extractPaginationParams } from '@/utils/paginationParamHelper';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import { RoleProtected } from '@/components/ProtectedRoleWrapper';

interface UpdateProductsParams {
  id: string;
  value: any;
}

const columnHelper = createColumnHelper<Product>();

function formatShopifyNativeVariant(value: any): string {
  if (!value) return '-';

  try {
    if (Array.isArray(value)) {
      return value
        .map(item => {
          if (typeof item === 'object' && item !== null) {
            return Object.values(item).filter(Boolean).join(', ');
          }
          return String(item);
        })
        .join('; ');
    }

    if (typeof value === 'object' && value !== null) {
      return Object.values(value).filter(Boolean).join(', ');
    }

    return String(value);
  } catch (e) {
    console.error('Error formatting shopifyNativeVariant:', e, value);
    return '[Format Error]';
  }
}

function parseCategoryData(categoryData: any): string {
  if (!categoryData) return '-';
  if (Array.isArray(categoryData)) {
    const filteredData = categoryData.filter(item => item && item !== '');
    return filteredData.length > 0 ? [...new Set(filteredData)].join(', ') : '-';
  }
  if (typeof categoryData !== 'string') {
    return String(categoryData) || '-';
  }
  const matches = categoryData.match(/[A-Z][a-z]+/g);
  if (matches && matches.length > 0) {
    return [...new Set(matches)].join(', ');
  }
  return categoryData || '-';
}

export const generateDynamicColumns = (fields: FieldsType[], router: any): ColumnDef<Product>[] => {
  return fields?.map(
    ({ key, label, type, options, secondary_key, sortable, action }: FieldsType | any) => {
      if (key === 'shopifyNativeVariant') {
        return {
          accessorKey: key as keyof Product,
          header: label as string,
          enableColumnFilter: false,
          enableSorting: sortable ?? false,
          meta: { filterType: type, options },
          cell: (info: any) => {
            const value = info.getValue();
            const formattedValue = formatShopifyNativeVariant(value);

            return (
              <Tooltip title={formattedValue} arrow>
                <Typography
                  noWrap
                  sx={{
                    maxWidth: 200,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    cursor: 'default',
                  }}
                >
                  {formattedValue}
                </Typography>
              </Tooltip>
            );
          },
        } as ColumnDef<Product>;
      }
      if (key === 'products.category') {
        return {
          accessorKey: key as keyof Product,
          header: label as string,
          enableColumnFilter: false,
          enableSorting: sortable ?? false,
          meta: { filterType: type, options },
          cell: (info: any) => {
            const rowData = info.row.original;
            if (!rowData?.id) {
              return (
                <Typography
                  noWrap
                  sx={{
                    maxWidth: 200,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  -
                </Typography>
              );
            }

            const categoryData =
              rowData?.product_category || rowData?.productCategory || rowData?.category || null;
            const displayValue = parseCategoryData(categoryData);

            return (
              <Tooltip title={displayValue}>
                <Typography
                  noWrap
                  sx={{
                    maxWidth: 200,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {displayValue}
                </Typography>
              </Tooltip>
            );
          },
        } as ColumnDef<Product>;
      }
      if (key === 'products') {
        return {
          accessorKey: key as keyof Product,
          header: label as string,
          enableColumnFilter: false,
          enableSorting: sortable ?? false,
          meta: { filterType: type, options },
          cell: (info: any) => {
            const value = info.getValue();
            let displayValue = '-';

            if (Array.isArray(value)) {
              displayValue = value.join(', ');
            } else if (typeof value === 'string') {
              displayValue = value;
            }

            return (
              <Tooltip title={displayValue}>
                <Typography
                  noWrap
                  sx={{
                    maxWidth: 200,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {displayValue}
                </Typography>
              </Tooltip>
            );
          },
        } as ColumnDef<Product>;
      }
      if (key === 'isActive') {
        return {
          accessorKey: key as keyof Product,
          header: label as string,
          enableColumnFilter: true,
          meta: {
            filterType: 'select',
            isBoolean: true,
            booleanOptions: { Active: true, Inactive: false },
          },
          enableSorting: sortable ?? false,
          cell: ({ row }) => {
            const isActive = row.original.isActive;
            const statusText = isActive ? 'Active' : 'Inactive';
            const statusColor = isActive ? 'success' : 'error';

            return (
              <div className="flex items-center gap-3">
                <Chip
                  variant="tonal"
                  label={statusText}
                  size="small"
                  color={statusColor}
                  className="capitalize"
                />
              </div>
            );
          },
        } as ColumnDef<Product>;
      }

      return {
        accessorKey: key as keyof Product,
        header: label as string,
        enableColumnFilter: false,
        enableSorting: sortable ?? false,
        meta: { filterType: type, options },
        cell: (info: any) => {
          const value = info.getValue();
          const displayValue =
            typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value ?? '-');
          return (
            <Typography
              noWrap
              sx={{
                maxWidth: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {displayValue}
            </Typography>
          );
        },
      } as ColumnDef<Product>;
    },
  );
};

interface ProductSkuListTableProps {
  page?: any;
  limit: any;
  data: any;
}

const ProductSkuListTable: React.FC<ProductSkuListTableProps> = ({ page, limit, data }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { updatePageSettings } = useSettings();
  const dispatch = useDispatch<AppDispatch>();
  const ability = useAbility();
  const {
    data: products,
    count: productsCount,
    filters,
  } = useSelector((state: RootState) => state.productSku);
  const configs = useSelector((state: RootState) => state.common.tableConfig);
  const [statusModal, setStatusModal] = useState<UpdateProductsParams | null>(null);
  const [loading, setLoading] = useState(false);
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const hasInitialized = useRef(false);

  // Initialize component and handle state restoration
  useEffect(() => {
    const cleanup = updatePageSettings({ skin: 'default' });
    const isFromDetailPage = sessionStorage.getItem('returningFromProductDetail') === 'true';
    if (isFromDetailPage) {
      const savedFilters = sessionStorage.getItem('productsFilters');
      if (savedFilters) {
        try {
          const restoredFilters = JSON.parse(savedFilters);
          dispatch(setProductsFilters(restoredFilters));
        } catch (error) {
          console.error('Failed to parse saved filters:', error);
        }
      }
      sessionStorage.removeItem('returningFromProductDetail');
      sessionStorage.removeItem('productsFilters');
    } else if (data && (!filters || filters.length === 0)) {
      dispatch(setProducts(data));
    }

    hasInitialized.current = true;
    return cleanup;
  }, [data, dispatch, updatePageSettings, filters]);

  // Handle URL parameter changes and data fetching
  useEffect(() => {
    if (!hasInitialized.current) return;

    const paramsObj = extractPaginationParams(searchParams);
    const currentPage = paramsObj.page || 1;
    const currentLimit = paramsObj.limit || 25;
    const flatFilters = Array.isArray(filters) ? filters.flat() : [];

    setLoading(true);

    if (flatFilters.length > 0) {
      const payload = {
        filters,
        page: currentPage,
        limit: currentLimit,
      };

      void fetchFilteredProductsSkusAdvanced(payload)
        .then(result => {
          dispatch(setProducts(result));
        })
        .catch(() => {
          toast.error('Failed to load filtered products');
        })
        .finally(() => setLoading(false));
    } else {
      void fetchFilteredProductsSkusAdvanced({
        filters: [],
        page: currentPage,
        limit: currentLimit,
      })
        .then(result => {
          dispatch(setProducts(result));
        })
        .catch(() => {
          toast.error('Failed to load products');
        })
        .finally(() => setLoading(false));
    }
  }, [searchParams, filters, dispatch]);

  // Function to build return URL with current state
  const buildReturnUrl = (): string => {
    const currentUrl = new URL(window.location.href);
    const currentParams = currentUrl.searchParams;
    const params = new URLSearchParams();

    currentParams.forEach((value, key) => {
      params.set(key, value);
    });

    if (!params.has('page')) {
      params.set('page', '1');
    }
    if (!params.has('limit')) {
      params.set('limit', '25');
    }

    return params.toString();
  };

  // Function to save current state to session storage
  const saveCurrentState = (): void => {
    sessionStorage.setItem('returningFromProductDetail', 'true');
    if (filters && filters.length > 0) {
      sessionStorage.setItem('productsFilters', JSON.stringify(filters));
    }
  };

  const dynamicColumns = useMemo(() => {
    if (!configs?.fields || configs.fields.length === 0) {
      return [];
    }
    return generateDynamicColumns(configs.fields, router);
  }, [configs?.fields, router]);

  const handleRowClick = (row: Product, e?: React.MouseEvent): void => {
    // Check if user has permission to view product details
    if (!ability?.can(Actions.ViewDetailPage, ActionsTarget.PIMS)) {
      return; // Don't navigate if user doesn't have permission
    }

    const path = `/ordermanagement/products/view/${row.id}`;
    if (e && (e.ctrlKey || e.metaKey)) {
      window.open(path, '_blank', 'noopener,noreferrer');
    } else {
      const returnUrl = buildReturnUrl();
      const fullPath = returnUrl
        ? `${path}?mode=view&returnUrl=${encodeURIComponent(`/ordermanagement/products?${returnUrl}`)}`
        : `${path}?mode=view`;
      saveCurrentState();
      void router.push(fullPath, { scroll: false });
    }
  };

  const handleStatusChange = (product: Product) => {
    setSelectedProduct(product);
    setOpenStatusModal(true);
  };

  const tableColumns = useMemo(() => {
    const selectColumn = columnHelper.display({
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          indeterminate={table.getIsSomePageRowsSelected()}
          onChange={table.getToggleAllPageRowsSelectedHandler()}
          onClick={e => e.stopPropagation()}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          disabled={!row.getCanSelect()}
          onChange={e => {
            e.stopPropagation();
            row.getToggleSelectedHandler()(e);
          }}
          onClick={e => e.stopPropagation()}
        />
      ),
      enableColumnFilter: false,
      size: 48,
    });

    const actionColumn = columnHelper.display({
      id: 'action',
      header: 'Actions',
      cell: ({ row }) => {
        const returnUrl = buildReturnUrl();
        const basePath = `/ordermanagement/products/view/${row.original.id}`;

        return (
          <div className="flex items-center">
            <Tooltip title="View Product" arrow placement="left-end">
              <span>
                <CustomIconButton
                  icon={<i className="ri-eye-line text-textSecondary" />}
                  ButtonAction={Actions.ViewDetailPage}
                  actionTarget={ActionsTarget.PIMS}
                  onClick={e => {
                    e.stopPropagation();
                    saveCurrentState();
                    const viewModePath = returnUrl
                      ? `${basePath}?mode=view&returnUrl=${encodeURIComponent(`/ordermanagement/products?${returnUrl}`)}`
                      : `${basePath}?mode=view`;
                    router.push(viewModePath);
                  }}
                />
              </span>
            </Tooltip>
            <Tooltip title="Edit Product" arrow>
              <span>
                <CustomIconButton
                  ButtonAction={Actions.EditDetailPage}
                  actionTarget={ActionsTarget.PIMS}
                  icon={<i className="ri-edit-line text-textSecondary" />}
                  onClick={e => {
                    e.stopPropagation();
                    saveCurrentState();
                    const editPath = returnUrl
                      ? `${basePath}?isEdit=true&returnUrl=${encodeURIComponent(`/ordermanagement/products?${returnUrl}`)}`
                      : `${basePath}?isEdit=true`;
                    router.push(editPath);
                  }}
                />
              </span>
            </Tooltip>
            {ability?.can(Actions.ActivateDeactivateSKU, ActionsTarget.PIMS) && (
              <Tooltip
                title={
                  !ability?.can(Actions.EditDetailPage, ActionsTarget.PIMS)
                    ? 'You need "Edit Detail Page" permission to activate/deactivate products'
                    : row.original.isActive
                      ? 'Deactivate Product'
                      : 'Activate Product'
                }
              >
                <span>
                  <Switch
                    checked={row.original.isActive}
                    onChange={e => {
                      e.stopPropagation();
                      handleStatusChange(row.original);
                    }}
                    onClick={e => {
                      e.stopPropagation();
                    }}
                    disabled={!ability?.can(Actions.EditDetailPage, ActionsTarget.PIMS)}
                    size="small"
                    sx={{ ml: 1 }}
                  />
                </span>
              </Tooltip>
            )}
          </div>
        );
      },
    });

    return [selectColumn, ...dynamicColumns, actionColumn];
  }, [dynamicColumns, buildReturnUrl, saveCurrentState, router, handleStatusChange]);

  const currentTabData = useMemo(() => {
    if (products && typeof products === 'object' && 'data' in products) {
      return products.data;
    }
    if (Array.isArray(products)) {
      return products;
    }
    return [];
  }, [products]);

  const handleApplyFilters = async (normalizedFilters: any): Promise<null> => {
    try {
      setLoading(true);
      const isReset = !normalizedFilters || normalizedFilters.length === 0;

      if (isReset) {
        dispatch(setProductsFilters([]));
        const url = new URL(window.location.href);
        url.searchParams.set('page', '1');
        window.history.replaceState({}, '', url.toString());
        setLoading(false);
        return null;
      } else {
        dispatch(setProductsFilters(normalizedFilters));
        setLoading(false);
        return null;
      }
    } catch (error) {
      setLoading(false);
      toast.error('Filter operation failed');
      return null;
    }
  };

  const handleConfirmStatusChange = async () => {
    if (!selectedProduct) return;

    setLoading(true);

    try {
      const newStatus = !selectedProduct.isActive;

      const response = await apiClient.patch(`/product-sku/${selectedProduct.id}`, {
        isActive: newStatus,
      });

      const paramsObj = extractPaginationParams(searchParams);
      const currentPage = paramsObj.page || 1;
      const currentLimit = paramsObj.limit || 25;

      const result = await fetchFilteredProductsSkusAdvanced({
        filters,
        page: currentPage,
        limit: currentLimit,
      });
      dispatch(setProducts(result));

      toast.success(`Product ${newStatus ? 'activated' : 'deactivated'} successfully`);
      setOpenStatusModal(false);
    } catch (error: any) {
      console.error('Failed to update product status:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);

      let errorMessage = 'Failed to update product status. Please try again.';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to update this product.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Product not found.';
      }

      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProductStatus = async (): Promise<void> => {
    setLoading(true);
    try {
      await updateProduct(statusModal as UpdateProductsParams);
      const paramsObj = extractPaginationParams(searchParams);
      const currentPage = paramsObj.page || 1;
      const currentLimit = paramsObj.limit || 25;

      const result = await fetchFilteredProductsSkusAdvanced({
        filters,
        page: currentPage,
        limit: currentLimit,
      });
      dispatch(setProducts(result));

      setLoading(false);
      setStatusModal(null);
      toast.success('Updated Successfully!');
    } catch (e) {
      setLoading(false);
      toast.error('Update failed!');
    }
  };

  return (
    <>
      <DataTable
        filterFields={configs?.filterDataFields}
        title="Products Filters"
        storeName="productSku"
        onApplyFilters={handleApplyFilters}
        filterCount={filters?.flat().length || 0}
        totalCount={productsCount || data?.count || 0}
        enableFilters={true}
        columns={tableColumns}
        data={currentTabData as Product[]}
        onRowClick={row => handleRowClick(row, window.event as unknown as React.MouseEvent)}
        loading={loading}
        page={Number(page)} // Convert to zero-based for DataTable
        limit={Number(limit)}
        onPaginationUpdate={(page, limit, isPageChange, isLimitChange) => {
          const params = new URLSearchParams(searchParams);
          if (isLimitChange) {
            params.set('limit', limit.toString());
            params.set('page', '1');
          } else if (isPageChange) {
            params.set('page', !page ? '1' : (page + 1).toString());
            params.set('limit', limit.toString());
          }

          router.push(`?${params.toString()}`);
        }}
        enableBulkUpdate={ability?.can(Actions.EditDetailPage, ActionsTarget.PIMS) || false}
        bulkDataFields={configs?.bulkInsertFields}
        onBulkApply={async ids => {
          const response = await apiClient.patch('/product-sku/bulk-update', ids);
          const paramsObj = extractPaginationParams(searchParams);

          const currentPage = paramsObj.page || 1;
          const currentLimit = paramsObj.limit || 25;
          const result = await fetchFilteredProductsSkusAdvanced({
            filters,
            page: currentPage,
            limit: currentLimit,
          });
          dispatch(setProducts(result));
          return response;
        }}
      />
      <FilterModal
        open={statusModal ? true : false}
        onClose={() => {
          setStatusModal(null);
        }}
        children={
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to {statusModal?.value?.isActive ? 'Deactivate' : 'Activate'} the
            ProductSKU ?
          </DialogContentText>
        }
        applyFilters={handleUpdateProductStatus}
      />
      <ConfirmationDialog
        open={openStatusModal}
        title={`${selectedProduct?.isActive ? 'Deactivate' : 'Activate'} Product`}
        message={
          selectedProduct && (
            <>
              Are you sure you want to {selectedProduct.isActive ? 'deactivate' : 'activate'} this
              product?
              {selectedProduct.isActive
                ? ' This will prevent the product from being used in orders.'
                : ' This will allow the product to be used in orders.'}
            </>
          )
        }
        confirmLabel="Confirm"
        confirmColor={selectedProduct?.isActive ? 'error' : 'success'}
        loading={loading}
        onConfirm={handleConfirmStatusChange}
        onCancel={() => setOpenStatusModal(false)}
      />
    </>
  );
};

export default ProductSkuListTable;
