'use client';
import React, { useEffect } from 'react';
import { Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import ProductSkuListTable from './ProductSkuListTable';
import { useSettings } from '@core/hooks/useSettings';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '@/redux-store';

const OrderManagementWrapper = ({
  page = 1,
  limit = 25,
  data,
}: {
  page?: any;
  limit: any;
  data?: any;
}) => {
  const { updatePageSettings } = useSettings();
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {}, [updatePageSettings]);

  return (
    <>
      <Grid container spacing={6}>
        <Grid size={12}>
          <Typography variant="h4">Products Management</Typography>
        </Grid>
        <Grid size={12}>
          <ProductSkuListTable page={page} limit={limit} data={data} />
        </Grid>
      </Grid>
    </>
  );
};

export default OrderManagementWrapper;
