'use client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Typo<PERSON>, Card, CardContent, Button, Box, Divider } from '@mui/material';
import { useSettings } from '@core/hooks/useSettings';
import { toast } from 'react-toastify';
import LoadingView from '@/components/LoadingView';
import { ProductSku } from '@/types/product-sku';
import apiClient from '@/utils/axios';
import ProductTabs from '../components/ProductTabs';
import { RoleProtected } from '@/components/ProtectedRoleWrapper';
import { Actions, ActionsTarget } from '@/libs/casl/ability';

const ProductSkuView = ({ params }: { params: { id: string } | any }) => {
  const getId = () => {
    if (params && params.id) {
      return params.id;
    }

    // Handle ReactPromise format
    if (params && params.value) {
      try {
        const parsedValue = JSON.parse(params.value);
        return parsedValue.id;
      } catch (e) {
        console.error('Failed to parse params value:', e);
      }
    }

    return null;
  };

  const id = getId();
  const router = useRouter();

  let updatePageSettings;
  try {
    const { updatePageSettings: updateSettings } = useSettings();
    updatePageSettings = updateSettings;
  } catch (error) {
    console.warn('Settings provider not available:', error);
    updatePageSettings = () => {};
  }

  const [productSku, setProductSku] = useState<ProductSku | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentTab, setCurrentTab] = useState('0');

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);
  };

  useEffect(() => {
    if (updatePageSettings) {
      return updatePageSettings({
        skin: 'default',
      });
    }
  }, [updatePageSettings]);

  useEffect(() => {
    if (id) {
      setLoading(true);
      fetchProductSku(id);
    } else {
      console.warn('No ID extracted from params');
    }
  }, [id]);

  const fetchProductSku = async (id: string) => {
    try {
      const response = await apiClient.get(`/product-sku/${id}`);
      // Convert numeric fields to numbers
      const transformedData = {
        ...response.data,
        shippingWeight: response.data.shippingWeight ? Number(response.data.shippingWeight) : null,
        productLength: response.data.productLength ? Number(response.data.productLength) : null,
        productWidth: response.data.productWidth ? Number(response.data.productWidth) : null,
        productHeight: response.data.productHeight ? Number(response.data.productHeight) : null,
      };
      setProductSku(transformedData);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch product SKU:', error);
      toast.error('Failed to load product SKU details');
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingView />;
  }

  if (!productSku) {
    return <Typography>Product SKU not found</Typography>;
  }

  const handleEdit = () => {
    router.push(`/products/edit/${id}`);
  };

  const handleBack = () => {
    setLoading(true);
    // Use returnUrl parameter if available, otherwise fallback to base URL
    const searchParams = new URLSearchParams(window.location.search);
    const returnUrl = searchParams.get('returnUrl');
    if (returnUrl) {
      router.push(returnUrl);
    } else {
      router.push('/ordermanagement/products');
    }
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const isEditParam = searchParams.get('isEdit');
    if (isEditParam === 'true') {
      // Navigate to edit page directly
      router.replace(`/ordermanagement/products/edit/${id}${window.location.search}`);
    }
  }, [id, router]);

  return (
    <Card>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
          }}
        >
          <Typography variant="h5">Product SKU Details</Typography>
          <Box>
            <RoleProtected action={Actions.EditDetailPage} actionTarget={ActionsTarget.PIMS}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleEdit}
                disabled={currentTab === '9'}
                sx={{ mr: 2 }}
              >
                Edit
              </Button>
            </RoleProtected>
            <Button variant="outlined" color="secondary" onClick={handleBack}>
              Back
            </Button>
          </Box>
        </Box>

        <Divider sx={{ mb: 4 }} />

        <ProductTabs productSku={productSku} onTabChange={handleTabChange} />
      </CardContent>
    </Card>
  );
};

export default ProductSkuView;
