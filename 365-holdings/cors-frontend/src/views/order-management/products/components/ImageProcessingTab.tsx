import {
  Typo<PERSON>,
  Box,
  FormControlLabel,
  Switch,
  Select,
  MenuItem,
  FormControl,
  FormHelperText,
  Tooltip,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
} from '@mui/material';
import { useCallback, useState } from 'react';
import { ProductSku } from '@/types/product-sku';
import InfoIcon from '@mui/icons-material/Info';
import { CroppingMethod, CropType, FileUploadFormat } from '@/constants/product.constants';
import AddIcon from '@mui/icons-material/Add';
import {
  tabContainerStyle,
  tabTitleStyle,
  responsiveRowStyle,
  responsiveLabelStyle,
  infoIconStyle,
  responsiveControlContainerStyle,
} from './styles';
import apiClient from '@/utils/axios';
import { toast } from 'react-toastify';
import { useFetchArtworkTypes } from '@/hooks/useFetchArtworkTypes';
import { useAbility } from '@/libs/casl/AbilityContext';
import { Actions, ActionsTarget } from '@/libs/casl/ability';

interface ArtworkType {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

interface GetApiResponse {
  statusCode: number;
  data: ArtworkType[];
}

interface PostApiResponse {
  statusCode: number;
  data: ArtworkType;
}

interface ImageProcessingTabProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
  errors?: Record<string, string>;
  setErrors?: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  setHasErrors?: (hasErrors: boolean) => void;
}

const ImageProcessingTab = ({
  productSku,
  isEdit = false,
  setProductSku,
  errors = {},
}: ImageProcessingTabProps) => {
  const ability = useAbility();
  const [openArtworkTypeDialog, setOpenArtworkTypeDialog] = useState(false);
  const [newArtworkType, setNewArtworkType] = useState('');
  const [loading, setLoading] = useState(false);
  const { isLoading: fetchingTypes, data: artworkTypes, addArtworkType } = useFetchArtworkTypes();

  const handleChange = useCallback(
    (field: string, value: any) => {
      if (isEdit && setProductSku) {
        if (field.includes('.')) {
          const [parent, child] = field.split('.');
          setProductSku(prev => ({
            ...prev,
            [parent]: {
              ...((prev[parent as keyof ProductSku] as object) || {}),
              [child]: value,
            },
          }));
        } else {
          setProductSku(prev => ({ ...prev, [field]: value }));
        }
      }
    },
    [isEdit, setProductSku],
  );

  const handleAddArtworkType = async () => {
    if (newArtworkType.trim() === '') {
      toast.error('Artwork type name cannot be empty');
      return;
    }

    if (artworkTypes.some(type => type.name.toLowerCase() === newArtworkType.toLowerCase())) {
      toast.error('Artwork type already exists');
      return;
    }

    setLoading(true);
    try {
      const response = await apiClient.post<PostApiResponse>('/artwork-types', {
        name: newArtworkType,
      });

      if (response.data.data) {
        addArtworkType(response.data.data);
        setNewArtworkType('');
        setOpenArtworkTypeDialog(false);
        toast.success('Artwork type added successfully');
      }
    } catch (error) {
      console.error('Error adding artwork type:', error);
      toast.error('Failed to add artwork type');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={tabContainerStyle}>
      <Paper elevation={0} sx={{ p: 3, mb: 6, bgcolor: 'background.default' }}>
        <Typography variant="h6" sx={tabTitleStyle}>
          Image Processing & Artwork Attributes
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Configure image processing requirements and artwork specifications for this product
        </Typography>
      </Paper>

      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        {/* Requires Cropping field */}
        <Box sx={responsiveRowStyle}>
          <Box sx={responsiveLabelStyle}>
            Requires Cropping:
            <Tooltip title="Indicates if this product requires image cropping">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={responsiveControlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.requireCropping}
                onChange={e => handleChange('requireCropping', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.requireCropping ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* Cropping Method field - only show if Requires Cropping is true */}
        {(isEdit && productSku.requireCropping) || (!isEdit && productSku.requireCropping) ? (
          <Box sx={responsiveRowStyle}>
            <Box sx={responsiveLabelStyle}>
              Cropping Method:
              <Tooltip title="Method used to crop images for this product">
                <InfoIcon sx={infoIconStyle} />
              </Tooltip>
            </Box>
            <Box sx={responsiveControlContainerStyle}>
              {isEdit ? (
                <FormControl
                  sx={{ minWidth: 300 }}
                  size="small"
                  required
                  error={Boolean(errors.croppingMethod)}
                  fullWidth
                >
                  <Select
                    value={productSku.croppingMethod || ''}
                    onChange={e => handleChange('croppingMethod', e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="">None</MenuItem>
                    <MenuItem value={CroppingMethod.MANUAL}>Manual</MenuItem>
                    <MenuItem value={CroppingMethod.CUTOUT_PRO}>Auto (CutoutPro.ai)</MenuItem>
                  </Select>
                  {errors.croppingMethod && (
                    <FormHelperText>{errors.croppingMethod}</FormHelperText>
                  )}
                </FormControl>
              ) : (
                <Typography>
                  {productSku.croppingMethod === CroppingMethod.CUTOUT_PRO
                    ? 'Auto (CutoutPro.ai)'
                    : productSku.croppingMethod === CroppingMethod.MANUAL
                      ? 'Manual'
                      : 'N/A'}
                </Typography>
              )}
            </Box>
          </Box>
        ) : null}

        {/* Crop Type field - only show if CutoutPro is selected */}
        {(isEdit && productSku.croppingMethod === CroppingMethod.CUTOUT_PRO) ||
        (!isEdit && productSku.croppingMethod === CroppingMethod.CUTOUT_PRO) ? (
          <Box sx={responsiveRowStyle}>
            <Box sx={responsiveLabelStyle}>
              Crop Type:
              <Tooltip title="Type of cropping to apply to images">
                <InfoIcon sx={infoIconStyle} />
              </Tooltip>
            </Box>
            <Box sx={responsiveControlContainerStyle}>
              {isEdit ? (
                <FormControl
                  sx={{ minWidth: 300 }}
                  size="small"
                  required
                  error={Boolean(errors.cropType)}
                  fullWidth
                >
                  <Select
                    value={productSku.cropType || ''}
                    onChange={e => handleChange('cropType', e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="">None</MenuItem>
                    <MenuItem value={CropType.FACE_CUTOUT}>Face Cutout</MenuItem>
                    <MenuItem value={CropType.BACKGROUND_REMOVAL}>Background Removal</MenuItem>
                  </Select>
                  {errors.cropType && <FormHelperText>{errors.cropType}</FormHelperText>}
                </FormControl>
              ) : (
                <Typography>
                  {productSku.cropType === CropType.FACE_CUTOUT
                    ? 'Face Cutout'
                    : productSku.cropType === CropType.BACKGROUND_REMOVAL
                      ? 'Background Removal'
                      : 'N/A'}
                </Typography>
              )}
            </Box>
          </Box>
        ) : null}

        {/* Cropping Review Required field */}
        <Box sx={responsiveRowStyle}>
          <Box sx={responsiveLabelStyle}>
            Cropping Review Required:
            <Tooltip title="Indicates if cropped images require manual review">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={responsiveControlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.croppingReviewRequired}
                onChange={e => handleChange('croppingReviewRequired', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.croppingReviewRequired ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* Artwork Type field */}
        <Box sx={responsiveRowStyle}>
          <Box sx={responsiveLabelStyle}>
            Artwork Type:
            <Tooltip title="Specifies the style of artwork required for this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={responsiveControlContainerStyle}>
            {isEdit ? (
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <FormControl
                  sx={{ minWidth: 300 }}
                  size="small"
                  required
                  error={Boolean(errors.artworkType)}
                  fullWidth
                >
                  <Select
                    value={productSku.artworkType?.id || ''}
                    onChange={e => {
                      const selectedType = artworkTypes.find(type => type.id === e.target.value);
                      handleChange('artworkType', selectedType || null);
                    }}
                    displayEmpty
                    disabled={fetchingTypes}
                  >
                    <MenuItem value="">None</MenuItem>
                    {fetchingTypes ? (
                      <MenuItem disabled>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <CircularProgress size={20} />
                          <Typography>Loading...</Typography>
                        </Box>
                      </MenuItem>
                    ) : (
                      artworkTypes.map(type => (
                        <MenuItem key={type.id} value={type.id}>
                          {type.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                  {errors.artworkType && <FormHelperText>{errors.artworkType}</FormHelperText>}
                </FormControl>
                {ability && ability.can(Actions.AddArtwork, ActionsTarget.PIMS) && (
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<AddIcon />}
                    onClick={() => setOpenArtworkTypeDialog(true)}
                    sx={{ height: 40 }}
                    disabled={fetchingTypes}
                  >
                    Add Value
                  </Button>
                )}
              </Box>
            ) : (
              <Typography>{productSku.artworkType?.name || 'N/A'}</Typography>
            )}
          </Box>
        </Box>

        {/* Artwork Required field */}
        <Box sx={responsiveRowStyle}>
          <Box sx={responsiveLabelStyle}>
            Artwork Required:
            <Tooltip title="Indicates if this product requires custom artwork">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={responsiveControlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.artworkRequired}
                onChange={e => handleChange('artworkRequired', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.artworkRequired ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* Customer Artwork Approval Required field */}
        <Box sx={responsiveRowStyle}>
          <Box sx={responsiveLabelStyle}>
            Customer Artwork Approval Required:
            <Tooltip title="Indicates if customers must approve artwork before production">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={responsiveControlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.requireCustomerArtworkApproval}
                onChange={e => handleChange('requireCustomerArtworkApproval', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.requireCustomerArtworkApproval ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* Template Required field */}
        <Box sx={responsiveRowStyle}>
          <Box sx={responsiveLabelStyle}>
            Template Required:
            <Tooltip title="Indicates if this product requires a design template">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={responsiveControlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.requireTemplate}
                onChange={e => handleChange('requireTemplate', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.requireTemplate ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* File Upload Format field */}
        <Box sx={responsiveRowStyle}>
          <Box sx={responsiveLabelStyle}>
            File Upload Format:
            <Tooltip title="Allowed file formats for image uploads">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={responsiveControlContainerStyle}>
            {isEdit ? (
              <FormControl sx={{ minWidth: 300 }} size="small" fullWidth>
                <Select
                  value={productSku.fileUploadFormat || ''}
                  onChange={e => handleChange('fileUploadFormat', e.target.value)}
                  displayEmpty
                >
                  <MenuItem value="">None</MenuItem>
                  <MenuItem value={FileUploadFormat.BMP}>{FileUploadFormat.BMP}</MenuItem>
                  <MenuItem value={FileUploadFormat.PNG}>{FileUploadFormat.PNG}</MenuItem>
                  <MenuItem value={FileUploadFormat.JPEG}>{FileUploadFormat.JPEG}</MenuItem>
                </Select>
              </FormControl>
            ) : (
              <Typography>{productSku.fileUploadFormat || 'N/A'}</Typography>
            )}
          </Box>
        </Box>

        {/* Image Naming Convention field */}
        <Box sx={responsiveRowStyle}>
          <Box sx={responsiveLabelStyle}>
            Image Naming Convention:
            <Tooltip title="Naming pattern for uploaded images">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={responsiveControlContainerStyle}>
            {isEdit ? (
              <FormControl
                sx={{ minWidth: 300 }}
                size="small"
                error={Boolean(errors.imageNamingConvention)}
                required={productSku.requireCropping}
                fullWidth
              >
                <Select
                  value={productSku.imageNamingConvention || ''}
                  onChange={e => handleChange('imageNamingConvention', e.target.value)}
                  displayEmpty
                >
                  <MenuItem value="">None</MenuItem>
                  {/* Dynamic menu items will be populated from API */}
                </Select>
                {errors.imageNamingConvention && (
                  <FormHelperText>{errors.imageNamingConvention}</FormHelperText>
                )}
              </FormControl>
            ) : (
              <Typography>{productSku.imageNamingConvention || 'N/A'}</Typography>
            )}
          </Box>
        </Box>
      </Box>

      {/* Dialog for adding new artwork type */}
      <Dialog open={openArtworkTypeDialog} onClose={() => setOpenArtworkTypeDialog(false)}>
        <DialogTitle>Add New Artwork Type</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Artwork Type"
            fullWidth
            value={newArtworkType}
            onChange={e => setNewArtworkType(e.target.value)}
            variant="outlined"
            disabled={loading}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenArtworkTypeDialog(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleAddArtworkType} variant="contained" disabled={loading}>
            {loading ? 'Adding...' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ImageProcessingTab;
