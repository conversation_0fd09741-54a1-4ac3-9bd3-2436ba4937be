import {
  Typography,
  Box,
  FormControl,
  TextField,
  Switch,
  FormHelperText,
  Paper,
  Tooltip,
  InputAdornment,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { ProductSku } from '@/types/product-sku';
import { useState, useEffect } from 'react';
import {
  tabContainerStyle,
  tabTitleStyle,
  rowStyle,
  labelStyle,
  infoIconStyle,
  controlContainerStyle,
} from './styles';

interface PricingAccountingTabProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
}

const PricingAccountingTab = ({
  productSku,
  isEdit = false,
  setProductSku,
}: PricingAccountingTabProps) => {
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Ensure chinaWOFEPrice is always a string
  useEffect(() => {
    if (
      isEdit &&
      setProductSku &&
      productSku.chinaWOFEPrice !== null &&
      productSku.chinaWOFEPrice !== undefined
    ) {
      const currentValue = productSku.chinaWOFEPrice;
      if (typeof currentValue !== 'string') {
        setProductSku(prev => ({
          ...prev,
          chinaWOFEPrice: String(currentValue),
        }));
      }
    }
  }, []);

  const handleChange = (field: string, value: any) => {
    if (isEdit && setProductSku) {
      // Validate numeric fields
      if (field === 'chinaWOFEPrice') {
        const numValue = Number(value);
        if (value !== '' && value !== null && (isNaN(numValue) || numValue <= 0)) {
          setErrors({
            ...errors,
            [field]: 'Please enter a positive number',
          });
        } else {
          // Clear error if valid
          const newErrors = { ...errors };
          delete newErrors[field];
          setErrors(newErrors);
        }

        // Explicitly convert to string before storing
        setProductSku(prev => ({
          ...prev,
          [field]: value === null || value === '' ? null : String(value),
        }));
      } else {
        setProductSku(prev => ({ ...prev, [field]: value }));
      }
    }
  };

  return (
    <Box sx={tabContainerStyle}>
      <Paper elevation={0} sx={{ p: 3, mb: 6, bgcolor: 'background.default' }}>
        <Typography variant="h6" sx={tabTitleStyle}>
          Pricing & Accounting Attributes
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Configure pricing details and accounting attributes for this product
        </Typography>
      </Paper>

      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        {/* China Sales WOFE Price field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            China Sales WOFE Price:
            <Tooltip title="The price used for sales in China WOFE">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl error={!!errors.chinaWOFEPrice}>
                <TextField
                  size="small"
                  type="number"
                  value={productSku.chinaWOFEPrice || ''}
                  onChange={e =>
                    handleChange('chinaWOFEPrice', e.target.value === '' ? null : e.target.value)
                  }
                  InputProps={{
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }}
                  sx={{ width: '100%', minWidth: 300 }}
                  inputProps={{ min: 0.01, step: 0.01 }}
                  placeholder="Enter price"
                />
                {errors.chinaWOFEPrice && <FormHelperText>{errors.chinaWOFEPrice}</FormHelperText>}
              </FormControl>
            ) : (
              <Typography>
                {productSku.chinaWOFEPrice ? `$${productSku.chinaWOFEPrice}` : 'N/A'}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Accounting Work Paper Mapping field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Accounting Work Paper Mapping:
            <Tooltip title="Enable accounting work paper mapping for this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.hasWorkPaper || false}
                onChange={e => handleChange('hasWorkPaper', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.hasWorkPaper ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default PricingAccountingTab;
