import {
  Typo<PERSON>,
  Box,
  Switch,
  TextField,
  FormControl,
  Select,
  MenuItem,
  Tooltip,
  Paper,
  FormHelperText,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { ProductSku } from '@/types/product-sku';
import { useState, useEffect, useCallback, useRef } from 'react';
import { ImageInheritRule, ExceptionHandlingRule } from '@/constants/product.constants';
import {
  tabContainerStyle,
  tabTitleStyle,
  rowStyle,
  labelStyle,
  infoIconStyle,
  controlContainerStyle,
} from './styles';
import { validateSkuMappingComplete } from '@/utils/productValidation';

interface SkuMappingTabProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
  errors?: Record<string, string>;
  setErrors?: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  setHasErrors?: (hasErrors: boolean) => void;
}

const SkuMappingTab = ({
  productSku,
  isEdit = false,
  setProductSku,
  errors = {},
  setErrors = () => {},
  setHasErrors = () => {},
}: SkuMappingTabProps) => {
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const prevProductSkuRef = useRef(productSku);
  const prevErrorsRef = useRef(errors);

  // Memoize the validation function
  const validateForm = useCallback(() => {
    return validateSkuMappingComplete(productSku, isEdit);
  }, [
    isEdit,
    productSku.canInheritImage,
    productSku.parentSku,
    productSku.imageInheritRule,
    productSku.imageInheritancePriority,
    productSku.exceptionHandlingRule,
    productSku.customerFollowupEnabled,
    productSku.followupTiming,
  ]);

  // Run validation only when productSku changes significantly
  useEffect(() => {
    // Skip initial render
    if (prevProductSkuRef.current === productSku) return;

    // Update ref
    prevProductSkuRef.current = productSku;

    // Run validation
    const newErrors = validateForm();

    // Only update if errors have changed
    if (JSON.stringify(newErrors) !== JSON.stringify(prevErrorsRef.current)) {
      prevErrorsRef.current = newErrors;
      setErrors(newErrors);
      setHasErrors(Object.keys(newErrors).length > 0);
    }
  }, [productSku, validateForm, setErrors, setHasErrors]);

  // Simplified handleChange to avoid state updates during render
  const handleChange = useCallback(
    (field: string, value: any) => {
      if (!isEdit || !setProductSku) return;

      // Mark field as touched
      setTouched(prev => ({ ...prev, [field]: true }));

      // Update the product SKU
      setProductSku(prev => ({ ...prev, [field]: value }));
    },
    [isEdit, setProductSku],
  );

  const handleBlur = useCallback((field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  }, []);

  return (
    <Box sx={tabContainerStyle}>
      <Paper elevation={0} sx={{ p: 3, mb: 6, bgcolor: 'background.default' }}>
        <Typography variant="h6" sx={tabTitleStyle}>
          SKU Mapping & Image Inheritance Attributes
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Configure SKU mapping and image inheritance settings for this product
        </Typography>
      </Paper>

      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        {/* Can Inherit Image field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Can Inherit Image from Parent SKU?:
            <Tooltip title="Enables this SKU to inherit images from a parent SKU">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.canInheritImage || false}
                onChange={e => handleChange('canInheritImage', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.canInheritImage ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* Image Inheritance Rule - only show if canInheritImage is true */}
        {(isEdit && productSku.canInheritImage) || (!isEdit && productSku.canInheritImage) ? (
          <Box sx={rowStyle}>
            <Box sx={labelStyle}>
              Image Inheritance Rule:
              <Tooltip title="Determines how images are inherited from the parent SKU">
                <InfoIcon sx={infoIconStyle} />
              </Tooltip>
            </Box>
            <Box sx={controlContainerStyle}>
              {isEdit ? (
                <FormControl
                  sx={{ minWidth: 200 }}
                  size="small"
                  required
                  error={!!errors.imageInheritRule && touched.imageInheritRule}
                  fullWidth
                >
                  <Select
                    value={productSku.imageInheritRule || ''}
                    onChange={e => handleChange('imageInheritRule', e.target.value)}
                    sx={{ width: '100%', minWidth: 300 }}
                    onBlur={() => handleBlur('imageInheritRule')}
                    displayEmpty
                  >
                    <MenuItem value={ImageInheritRule.NONE}>None</MenuItem>
                    <MenuItem value={ImageInheritRule.AUTO_INHERIT}>Auto-inherit</MenuItem>
                    <MenuItem value={ImageInheritRule.REQUIRES_REVIEW}>Requires Review</MenuItem>
                  </Select>
                  {errors.imageInheritRule && touched.imageInheritRule && (
                    <FormHelperText>{errors.imageInheritRule}</FormHelperText>
                  )}
                </FormControl>
              ) : (
                <Typography>
                  {productSku.imageInheritRule === ImageInheritRule.AUTO_INHERIT
                    ? 'Auto-inherit'
                    : productSku.imageInheritRule === ImageInheritRule.REQUIRES_REVIEW
                      ? 'Requires Review'
                      : 'N/A'}
                </Typography>
              )}
            </Box>
          </Box>
        ) : null}

        {/* Manual Override Allowed - only show if canInheritImage is true */}
        {(isEdit && productSku.canInheritImage) || (!isEdit && productSku.canInheritImage) ? (
          <Box sx={rowStyle}>
            <Box sx={labelStyle}>
              Manual Override Allowed:
              <Tooltip title="Allows manual override of inherited images">
                <InfoIcon sx={infoIconStyle} />
              </Tooltip>
            </Box>
            <Box sx={controlContainerStyle}>
              {isEdit ? (
                <Switch
                  checked={productSku.canManualOverride || false}
                  onChange={e => handleChange('canManualOverride', e.target.checked)}
                />
              ) : (
                <Typography>{productSku.canManualOverride ? 'Yes' : 'No'}</Typography>
              )}
            </Box>
          </Box>
        ) : null}

        {/* Priority for Image Inheritance - only show if parentSku has a value */}
        {(isEdit && productSku.parentSku?.[0]?.parentSku?.sku) ||
        (!isEdit && productSku.parentSku?.[0]?.parentSku?.sku) ? (
          <Box sx={rowStyle}>
            <Box sx={labelStyle}>
              Priority for Image Inheritance:
              <Tooltip title="Priority level for image inheritance (lower numbers have higher priority)">
                <InfoIcon sx={infoIconStyle} />
              </Tooltip>
            </Box>
            <Box sx={controlContainerStyle}>
              {isEdit ? (
                <FormControl
                  error={!!errors.imageInheritancePriority && touched.imageInheritancePriority}
                  fullWidth
                >
                  <TextField
                    type="number"
                    size="small"
                    value={productSku.imageInheritancePriority || ''}
                    onChange={e =>
                      handleChange(
                        'imageInheritancePriority',
                        e.target.value === '' ? null : Number(e.target.value),
                      )
                    }
                    onBlur={() => handleBlur('imageInheritancePriority')}
                    placeholder="Enter priority (e.g., 1)"
                    inputProps={{ min: 1 }}
                    required
                    sx={{ width: 120 }}
                  />
                  {errors.imageInheritancePriority && touched.imageInheritancePriority && (
                    <FormHelperText>{errors.imageInheritancePriority}</FormHelperText>
                  )}
                </FormControl>
              ) : (
                <Typography>{productSku.imageInheritancePriority || 'N/A'}</Typography>
              )}
            </Box>
          </Box>
        ) : null}

        {/* Exception Handling Rule - only show if canInheritImage is true */}
          <Box sx={rowStyle}>
            <Box sx={labelStyle}>
              Exception Handling Rule:
              <Tooltip title="Determines how to handle cases when image inheritance fails">
                <InfoIcon sx={infoIconStyle} />
              </Tooltip>
            </Box>
            <Box sx={controlContainerStyle}>
              {isEdit ? (
                <FormControl
                  sx={{ minWidth: 300 }}
                  size="small"
                  required
                  error={!!errors.exceptionHandlingRule && touched.exceptionHandlingRule}
                  fullWidth
                >
                  <Select
                    value={productSku.exceptionHandlingRule || ''}
                    onChange={e => handleChange('exceptionHandlingRule', e.target.value)}
                    onBlur={() => handleBlur('exceptionHandlingRule')}
                    displayEmpty
                  >
                    <MenuItem value={ExceptionHandlingRule.NONE}>None</MenuItem>
                    <MenuItem value={ExceptionHandlingRule.QUEUE}>
                      Send to Image Needed Queue
                    </MenuItem>
                    <MenuItem value={ExceptionHandlingRule.FLAG}>
                      Auto-Assign from First Image on Order
                    </MenuItem>
                  </Select>
                  {errors.exceptionHandlingRule && touched.exceptionHandlingRule && (
                    <FormHelperText>{errors.exceptionHandlingRule}</FormHelperText>
                  )}
                </FormControl>
              ) : (
                <Typography>
                  {productSku.exceptionHandlingRule === ExceptionHandlingRule.QUEUE
                    ? 'Send to Image Needed Queue'
                    : productSku.exceptionHandlingRule === ExceptionHandlingRule.FLAG
                      ? 'Auto-Assign from First Image on Order'
                      : 'N/A'}
                </Typography>
              )}
            </Box>
          </Box>

        {/* Automated Customer Follow-Up - only show if canInheritImage is true AND exceptionHandlingRule is QUEUE */}
        {(isEdit &&
          productSku.exceptionHandlingRule === ExceptionHandlingRule.QUEUE) ||
        (!isEdit &&
          productSku.exceptionHandlingRule === ExceptionHandlingRule.QUEUE) ? (
          <Box sx={rowStyle}>
            <Box sx={labelStyle}>
              Automated Customer Follow-Up Enabled?:
              <Tooltip title="Enables automated follow-up with customers for missing images">
                <InfoIcon sx={infoIconStyle} />
              </Tooltip>
            </Box>
            <Box sx={controlContainerStyle}>
              {isEdit ? (
                <Switch
                  checked={productSku.customerFollowupEnabled || false}
                  onChange={e => handleChange('customerFollowupEnabled', e.target.checked)}
                />
              ) : (
                <Typography>{productSku.customerFollowupEnabled ? 'Yes' : 'No'}</Typography>
              )}
            </Box>
          </Box>
        ) : null}
      </Box>
    </Box>
  );
};

export default SkuMappingTab;
