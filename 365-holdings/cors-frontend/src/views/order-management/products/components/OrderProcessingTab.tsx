import {
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  TextField,
  FormControlLabel,
  Switch,
  FormHelperText,
  InputAdornment,
  Tooltip,
  Paper,
} from "@mui/material";
import { ProductSku } from "@/types/product-sku";
import { useState, useEffect } from "react";
import InfoIcon from "@mui/icons-material/Info";
import { RoutingMethod } from "@/constants/product.constants";
import {
  tabContainerStyle,
  tabTitleStyle,
  rowStyle,
  labelStyle,
  infoIconStyle,
  controlContainerStyle,
} from "./styles";
import apiClient from "@/utils/axios";

interface OrderProcessingTabProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
}

const OrderProcessingTab = ({
  productSku,
  isEdit = false,
  setProductSku,
}: OrderProcessingTabProps) => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [shipStationStores, setShipStationStores] = useState<
    Array<{ id: string; name: string }>
  >([]);

  // Fetch ShipStation stores from API
  useEffect(() => {
    const fetchShipStationStores = async () => {
      try {
        const response = await apiClient.get("/api/shipstation/stores");
        setShipStationStores(response.data);
      } catch (error) {
        console.error("Error fetching ShipStation stores:", error);
        // Set some default stores if API fails
        setShipStationStores([
          { id: "store1", name: "Cuddle Clones - Custom Mugs" },
          { id: "store2", name: "Cuddle Clones - Plush" },
          { id: "store3", name: "Cuddle Clones - Ornaments" },
          { id: "store4", name: "Cuddle Clones - Figurines" },
        ]);
      }
    };

    // Comment out for now to prevent errors if endpoint doesn't exist
    // fetchShipStationStores();
  }, []);

  // Validate form when component mounts or productSku changes
  useEffect(() => {
    if (isEdit) {
      validateForm();
    }
  }, [productSku]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate shipping weight
    if (
      productSku.shippingWeight !== null &&
      productSku.shippingWeight !== undefined
    ) {
      const numValue = Number(productSku.shippingWeight);
      if (isNaN(numValue) || numValue <= 0) {
        newErrors.shippingWeight = "Please enter a positive number";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (field: string, value: any) => {
    if (isEdit && setProductSku) {
      // Mark field as touched
      setTouched((prev) => ({ ...prev, [field]: true }));

      // Validate numeric fields
      if (field === "shippingWeight") {
        const numValue = Number(value);
        if (value !== "" && (isNaN(numValue) || numValue <= 0)) {
          setErrors({
            ...errors,
            [field]: "Please enter a positive number",
          });
        } else {
          // Clear error if valid
          const newErrors = { ...errors };
          delete newErrors[field];
          setErrors(newErrors);
        }
      }

      setProductSku((prev) => ({ ...prev, [field]: value }));
    }
  };

  const handleBlur = (field: string) => {
    setTouched((prev) => ({ ...prev, [field]: true }));
  };

  return (
    <Box sx={tabContainerStyle}>
      <Paper elevation={0} sx={{ p: 3, mb: 6, bgcolor: "background.default" }}>
        <Typography variant="h6" sx={tabTitleStyle}>
          Order Processing & Routing
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Configure order processing settings and routing methods for this
          product
        </Typography>
      </Paper>

      <Box sx={{ display: "flex", flexDirection: "column" }}>
        {/* Product Routing Method field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Product Routing Method:
            <Tooltip title="The system that will handle this product's orders">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl sx={{ minWidth: 200 }} size="small" fullWidth>
                <Select
                  value={productSku.routingMethod || ""}
                  onChange={(e) =>
                    handleChange("routingMethod", e.target.value)
                  }
                  sx={{ width: "100%", minWidth: 300 }}
                  displayEmpty>
                  <MenuItem value={RoutingMethod.CORS}>
                    {RoutingMethod.CORS}
                  </MenuItem>
                  <MenuItem value={RoutingMethod.OMS}>
                    {RoutingMethod.OMS}
                  </MenuItem>
                </Select>
              </FormControl>
            ) : (
              <Typography>{productSku.routingMethod || "N/A"}</Typography>
            )}
          </Box>
        </Box>

        {/* Pre-Processing Required field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Pre-Processing Required:
            <Tooltip title="Indicates if this product requires pre-processing before manufacturing">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.requirePreprocessing || false}
                onChange={(e) =>
                  handleChange("requirePreprocessing", e.target.checked)
                }
              />
            ) : (
              <Typography>
                {productSku.requirePreprocessing ? "Yes" : "No"}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Processing Priority field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Processing Priority:
            <Tooltip title="Priority level for processing this product (1 is highest)">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl sx={{ minWidth: 200 }} size="small" fullWidth>
                <Select
                  value={
                    productSku.processingPriority !== null
                      ? productSku.processingPriority
                      : ""
                  }
                  onChange={(e) =>
                    handleChange("processingPriority", e.target.value)
                  }
                  sx={{ width: "100%", minWidth: 300 }}
                  displayEmpty>
                  <MenuItem value="">None</MenuItem>
                  {[...Array(10)].map((_, i) => (
                    <MenuItem key={i + 1} value={i + 1}>
                      {i + 1}
                      {i === 0 ? " - Highest" : i === 9 ? " - Lowest" : ""}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : (
              <Typography>
                {productSku.processingPriority !== null
                  ? `${productSku.processingPriority}${
                      productSku.processingPriority === 1
                        ? " - Highest"
                        : productSku.processingPriority === 10
                          ? " - Lowest"
                          : ""
                    }`
                  : "N/A"}
              </Typography>
            )}
          </Box>
        </Box>

        {/* ShipStation Store Mapping field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            ShipStation Store Mapping:
            <Tooltip title="The ShipStation store this product is associated with">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl sx={{ minWidth: 200 }} size="small" fullWidth>
                <Select
                  value={productSku.shipStationStore || ""}
                  onChange={(e) =>
                    handleChange("shipStationStore", e.target.value)
                  }
                  sx={{ width: "100%", minWidth: 300 }}
                  displayEmpty>
                  <MenuItem value="">None</MenuItem>
                  {shipStationStores.map((store) => (
                    <MenuItem key={store.id} value={store.id}>
                      {store.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : (
              <Typography>
                {productSku.shipStationStore
                  ? shipStationStores.find(
                      (s) => s.id === productSku.shipStationStore
                    )?.name || productSku.shipStationStore
                  : "N/A"}
              </Typography>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default OrderProcessingTab;
