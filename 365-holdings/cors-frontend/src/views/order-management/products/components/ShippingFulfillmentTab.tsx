import {
  Typography,
  Box,
  TextField,
  FormControl,
  Select,
  MenuItem,
  FormHelperText,
  InputAdornment,
  Tooltip,
  Paper,
} from '@mui/material';
import { ProductSku } from '@/types/product-sku';
import { useState, useEffect } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import { ShippingMethod } from '@/constants/product.constants';
import {
  tabContainerStyle,
  tabTitleStyle,
  rowStyle,
  labelStyle,
  infoIconStyle,
  controlContainerStyle,
} from './styles';

interface ShippingFulfillmentTabProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
}

const ShippingFulfillmentTab = ({
  productSku,
  isEdit = false,
  setProductSku,
}: ShippingFulfillmentTabProps) => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [displayValues, setDisplayValues] = useState({
    shippingWeight: '',
    productLength: '',
    productWidth: '',
    productHeight: '',
  });

  const handleChange = (field: string, value: any) => {
    if (isEdit && setProductSku) {
      // Mark field as touched
      setTouched(prev => ({ ...prev, [field]: true }));

      // Update display value
      setDisplayValues(prev => ({
        ...prev,
        [field]: value,
      }));

      // Handle numeric fields
      if (
        field === 'shippingWeight' ||
        field === 'productLength' ||
        field === 'productWidth' ||
        field === 'productHeight'
      ) {
        if (value === '') {
          // Clear error if empty
          const newErrors = { ...errors };
          delete newErrors[field];
          setErrors(newErrors);

          setProductSku(prev => ({
            ...prev,
            [field]: null,
          }));
        } else {
          const numValue = Number(value);
          if (isNaN(numValue) || numValue <= 0) {
            setErrors(prev => ({
              ...prev,
              [field]: 'Please enter a positive number',
            }));
          } else {
            // Clear error if valid
            const newErrors = { ...errors };
            delete newErrors[field];
            setErrors(newErrors);

            // Store as number
            setProductSku(prev => ({
              ...prev,
              [field]: numValue,
            }));
          }
        }
      } else {
        // Handle non-numeric fields
        setProductSku(prev => ({ ...prev, [field]: value }));
      }
    }
  };

  // Initialize display values only when productSku changes
  useEffect(() => {
    if (productSku) {
      // Convert any string values to numbers when initializing display values
      const convertToNumber = (value: any) => {
        if (value === null || value === undefined || value === '') return '';
        const num = Number(value);
        return isNaN(num) ? '' : String(num);
      };

      setDisplayValues({
        shippingWeight: convertToNumber(productSku.shippingWeight),
        productLength: convertToNumber(productSku.productLength),
        productWidth: convertToNumber(productSku.productWidth),
        productHeight: convertToNumber(productSku.productHeight),
      });
    }
  }, [productSku]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    const dimensionFields = ['productLength', 'productWidth', 'productHeight', 'shippingWeight'];

    dimensionFields.forEach(field => {
      const value = productSku[field as keyof ProductSku];
      if (value !== null && value !== undefined && value !== '') {
        const numValue = Number(value);
        if (Number(numValue) < 0) {
          newErrors[field] = 'Please enter a positive number';
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  return (
    <Box sx={tabContainerStyle}>
      <Paper elevation={0} sx={{ p: 3, mb: 6, bgcolor: 'background.default' }}>
        <Typography variant="h6" sx={tabTitleStyle}>
          Shipping & Fulfillment
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Configure shipping details and dimensions for this product
        </Typography>
      </Paper>

      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        {/* Shipping Weight field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Shipping Weight:
            <Tooltip title="The weight used for shipping calculations">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl error={!!errors.shippingWeight && touched.shippingWeight} fullWidth>
                <TextField
                  size="small"
                  type="number"
                  value={displayValues.shippingWeight}
                  onChange={e => handleChange('shippingWeight', e.target.value)}
                  sx={{ width: '100%', minWidth: 300 }}
                  onBlur={() => handleBlur('shippingWeight')}
                  InputProps={{
                    endAdornment: <InputAdornment position="end">lbs</InputAdornment>,
                  }}
                  inputProps={{ min: 0.1, step: 0.1 }}
                  placeholder="Enter weight"
                />
                {errors.shippingWeight && touched.shippingWeight && (
                  <FormHelperText>{errors.shippingWeight}</FormHelperText>
                )}
              </FormControl>
            ) : (
              <Typography>
                {productSku.shippingWeight ? `${Number(productSku.shippingWeight)} lbs` : 'N/A'}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Shipping Method field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Shipping Method:
            <Tooltip title="Default shipping method for this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl sx={{ minWidth: 200 }} size="small" fullWidth>
                <Select
                  value={productSku.shippingMethod || ''}
                  onChange={e => handleChange('shippingMethod', e.target.value)}
                  sx={{ width: '100%', minWidth: 300 }}
                  displayEmpty
                >
                  <MenuItem value="">None</MenuItem>
                  <MenuItem value={ShippingMethod.STANDARD}>Standard</MenuItem>
                  <MenuItem value={ShippingMethod.EXPEDITED}>Expedited</MenuItem>
                </Select>
              </FormControl>
            ) : (
              <Typography>{productSku.shippingMethod || 'N/A'}</Typography>
            )}
          </Box>
        </Box>

        {/* Product Dimensions fields */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Product Dimensions:
            <Tooltip title="Physical dimensions of the product for shipping calculations">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <Box
                sx={{
                  display: 'flex',
                  gap: 2,
                  width: '100%',
                  flexWrap: 'wrap',
                }}
              >
                <TextField
                  size="small"
                  type="number"
                  value={displayValues.productLength}
                  onChange={e => handleChange('productLength', e.target.value)}
                  onBlur={() => handleBlur('productLength')}
                  label="Length"
                  InputProps={{
                    endAdornment: <InputAdornment position="end">inch</InputAdornment>,
                  }}
                  inputProps={{ min: 0.1, step: 0.1 }}
                  error={!!errors.productLength && touched.productLength}
                  helperText={touched.productLength && errors.productLength}
                  sx={{ flex: 1, minWidth: 150 }}
                />
                <TextField
                  size="small"
                  type="number"
                  value={displayValues.productWidth}
                  onChange={e => handleChange('productWidth', e.target.value)}
                  onBlur={() => handleBlur('productWidth')}
                  label="Width"
                  InputProps={{
                    endAdornment: <InputAdornment position="end">inch</InputAdornment>,
                  }}
                  inputProps={{ min: 0.1, step: 0.1 }}
                  error={!!errors.productWidth && touched.productWidth}
                  helperText={touched.productWidth && errors.productWidth}
                  sx={{ flex: 1, minWidth: 150 }}
                />
                <TextField
                  size="small"
                  type="number"
                  value={displayValues.productHeight}
                  onChange={e => handleChange('productHeight', e.target.value)}
                  onBlur={() => handleBlur('productHeight')}
                  label="Height"
                  InputProps={{
                    endAdornment: <InputAdornment position="end">inch</InputAdornment>,
                  }}
                  inputProps={{ min: 0.1, step: 0.1 }}
                  error={!!errors.productHeight && touched.productHeight}
                  helperText={touched.productHeight && errors.productHeight}
                  sx={{ flex: 1, minWidth: 150 }}
                />
              </Box>
            ) : (
              <Typography>
                {productSku.productLength && productSku.productWidth && productSku.productHeight
                  ? `${Number(productSku.productLength)} × ${Number(productSku.productWidth)} × ${Number(productSku.productHeight)} inches`
                  : 'N/A'}
              </Typography>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ShippingFulfillmentTab;
