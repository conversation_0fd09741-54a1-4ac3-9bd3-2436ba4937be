import {
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  TextField,
  Chip,
  FormHelperText,
  Tooltip,
  Paper,
  SelectChangeEvent,
  CircularProgress,
  Checkbox,
} from '@mui/material';
import { ProductSku } from '@/types/product-sku';
import { useState, useEffect, useCallback } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import {
  tabContainerStyle,
  tabTitleStyle,
  rowStyle,
  labelStyle,
  infoIconStyle,
  controlContainerStyle,
} from './styles';
import apiClient from '@/utils/axios';
import { VendorAssignmentRule } from '@/constants/product.constants';

interface Vendor {
  id: string;
  name: string;
}

interface ManufacturingVendorTabProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
}

const ManufacturingVendorTab = ({
  productSku,
  isEdit = false,
  setProductSku,
}: ManufacturingVendorTabProps) => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [search, setSearch] = useState('');

  const fetchVendors = useCallback(async (pageNum = 1, searchQuery = '') => {
    setLoading(true);
    try {
      const response = await apiClient.get('/vendors', {
        params: {
          page: pageNum,
          limit: 25,
          q: searchQuery ? `name:like:${searchQuery}` : undefined,
        },
      });

      const newVendors = response.data?.data || [];
      setVendors(prev => (pageNum === 1 ? newVendors : [...prev, ...newVendors]));
      setHasMore(newVendors.length === 25);
    } catch (error) {
      console.error('Error fetching vendors:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchVendors(1, search);
  }, [fetchVendors]);

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setPage(1);
      fetchVendors(1, search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search, fetchVendors]);

  // Load more vendors when scrolling
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchVendors(nextPage, search);
    }
  };

  // Validate form when component mounts or productSku changes
  useEffect(() => {
    if (isEdit) {
      validateForm();
    }
  }, [productSku, isEdit]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate numeric fields
    if (productSku.productionCycleTime !== null && productSku.productionCycleTime !== undefined) {
      const numValue = Number(productSku.productionCycleTime);
      if (!Number.isInteger(numValue) || numValue <= 0) {
        newErrors.productionCycleTime = 'Please enter a positive integer value';
      }
    }

    if (productSku.maxCapacityPerDay !== null && productSku.maxCapacityPerDay !== undefined) {
      const numValue = Number(productSku.maxCapacityPerDay);
      if (!Number.isInteger(numValue) || numValue <= 0) {
        newErrors.maxCapacityPerDay = 'Please enter a positive integer value';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (field: string, value: any) => {
    if (isEdit && setProductSku) {
      // Mark field as touched
      setTouched(prev => ({ ...prev, [field]: true }));

      // Validate numeric fields
      if (field === 'productionCycleTime' || field === 'maxCapacityPerDay') {
        const numValue = Number(value);
        if (value !== '' && (!Number.isInteger(numValue) || numValue <= 0)) {
          setErrors({
            ...errors,
            [field]: 'Please enter a positive integer value',
          });
        } else {
          const newErrors = { ...errors };
          delete newErrors[field];
          setErrors(newErrors);
        }
      }

      // If changing eligible vendors, ensure primary vendor is still in the list
      if (field === 'eligibleVendors' && productSku.primaryVendor) {
        const eligibleVendorIds = value as string[];

        if (!eligibleVendorIds.includes(productSku.primaryVendor.id)) {
          setProductSku(prev => ({
            ...prev,
            [field]: value,
            primaryVendor: null,
          }));
          return;
        }
      }

      // If changing primaryVendor, update with the full vendor object
      if (field === 'primaryVendor') {
        const selectedVendor = vendors.find(v => v.id === value) || null;

        // Only update if the selected vendor is different from the current one
        if (
          (selectedVendor === null && productSku.primaryVendor !== null) ||
          (selectedVendor !== null && productSku.primaryVendor === null) ||
          (selectedVendor !== null &&
            productSku.primaryVendor !== null &&
            selectedVendor.id !== productSku.primaryVendor.id)
        ) {
          setProductSku(prev => ({ ...prev, [field]: selectedVendor }));
        }
        return;
      }

      setProductSku(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  // Handle eligible vendors change
  const handleEligibleVendorsChange = (event: SelectChangeEvent<string[]>) => {
    const selectedVendors = event.target.value as string[];
    handleChange('eligibleVendors', selectedVendors);
  };

  // Filter primary vendor options to only show eligible vendors
  const getPrimaryVendorOptions = () => {
    if (!productSku.eligibleVendors || productSku.eligibleVendors.length === 0) {
      return []; // Return empty array if no eligible vendors
    }
    return vendors.filter(vendor => productSku.eligibleVendors?.includes(vendor.id));
  };

  // Handle scroll event for infinite loading
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight } = event.currentTarget;
    if (scrollHeight - scrollTop <= clientHeight * 1.5) {
      handleLoadMore();
    }
  };

  // Handle primary vendor change
  const handlePrimaryVendorChange = (event: SelectChangeEvent<string[]>) => {
    const selectedVendors = event.target.value as string[];

    // Take the last selected vendor as the primary vendor
    const primaryVendorId =
      selectedVendors.length > 0 ? selectedVendors[selectedVendors.length - 1] : null;

    if (primaryVendorId) {
      const selectedVendor = vendors.find(v => v.id === primaryVendorId) || null;

      // Only update if the selected vendor is different from the current one
      if (
        (selectedVendor === null && productSku.primaryVendor !== null) ||
        (selectedVendor !== null && productSku.primaryVendor === null) ||
        (selectedVendor !== null &&
          productSku.primaryVendor !== null &&
          selectedVendor.id !== productSku.primaryVendor.id)
      ) {
        setProductSku?.(prev => ({ ...prev, primaryVendor: selectedVendor }));
      }
    } else {
      // If no vendor is selected, set primaryVendor to null
      setProductSku?.(prev => ({ ...prev, primaryVendor: null }));
    }
  };

  return (
    <Box sx={tabContainerStyle}>
      <Paper elevation={0} sx={{ p: 3, mb: 6, bgcolor: 'background.default' }}>
        <Typography variant="h6" sx={tabTitleStyle}>
          Manufacturing & Vendor Assignment
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Configure manufacturing details and vendor assignments for this product
        </Typography>
      </Paper>

      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Eligible Vendors:
            <Tooltip title="All vendors that can manufacture this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl sx={{ minWidth: 200 }} size="small" fullWidth>
                <Select
                  multiple
                  value={productSku.eligibleVendors || []}
                  onChange={handleEligibleVendorsChange}
                  sx={{ width: '100%', minWidth: 300 }}
                  renderValue={selected => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map(value => {
                        const vendor = vendors.find(v => v.id === value);
                        return (
                          <Chip key={value} label={vendor ? vendor.name : value} size="small" />
                        );
                      })}
                    </Box>
                  )}
                  MenuProps={{
                    PaperProps: {
                      style: { maxHeight: 300 },
                      onScroll: handleScroll,
                    },
                  }}
                >
                  {vendors.map(vendor => (
                    <MenuItem key={vendor.id} value={vendor.id}>
                      <Checkbox
                        checked={productSku.eligibleVendors?.includes(vendor.id) || false}
                      />
                      {vendor.name}
                    </MenuItem>
                  ))}
                  {loading && hasMore && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
                      <CircularProgress size={24} />
                    </Box>
                  )}
                </Select>
              </FormControl>
            ) : (
              <Typography>
                {productSku.eligibleVendors && productSku.eligibleVendors.length > 0
                  ? productSku.eligibleVendors
                      .map(id => {
                        const vendor = vendors.find(v => v.id === id);
                        return vendor ? vendor.name : 'Unknown Vendor';
                      })
                      .join(', ')
                  : 'None'}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Primary Vendor field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Primary Vendor:
            <Tooltip title="The main vendor responsible for manufacturing this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl sx={{ minWidth: 200 }} size="small" fullWidth>
                <Select
                  value={productSku.primaryVendor?.id || ''}
                  onChange={e => handleChange('primaryVendor', e.target.value)}
                  sx={{ width: '100%', minWidth: 300 }}
                  displayEmpty
                  renderValue={selected => {
                    if (!selected)
                      return <Typography color="text.secondary">Select a vendor</Typography>;
                    const vendor = vendors.find(v => v.id === selected);
                    return vendor ? vendor.name : 'Unknown Vendor';
                  }}
                  MenuProps={{
                    PaperProps: {
                      style: { maxHeight: 300 },
                      onScroll: handleScroll,
                    },
                  }}
                  disabled={!productSku.eligibleVendors || productSku.eligibleVendors.length === 0}
                >
                  {getPrimaryVendorOptions().map(vendor => (
                    <MenuItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </MenuItem>
                  ))}
                  {loading && hasMore && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
                      <CircularProgress size={24} />
                    </Box>
                  )}
                </Select>
                {(!productSku.eligibleVendors || productSku.eligibleVendors.length === 0) && (
                  <FormHelperText>Select eligible vendors first</FormHelperText>
                )}
              </FormControl>
            ) : (
              <Typography>{productSku.primaryVendor?.name || 'None'}</Typography>
            )}
          </Box>
        </Box>

        {/* Vendor SKU Mapping field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Vendor SKU Mapping:
            <Tooltip title="The vendor's internal SKU code for this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <TextField
                size="small"
                fullWidth
                value={productSku.vendorSkuMapping || ''}
                onChange={e => handleChange('vendorSkuMapping', e.target.value)}
                placeholder="Enter vendor SKU mapping (e.g., P12345)"
                sx={{ width: '100%', minWidth: 300 }}
              />
            ) : (
              <Typography>{productSku.vendorSkuMapping || 'N/A'}</Typography>
            )}
          </Box>
        </Box>

        {/* Vendor Assignment Rule field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Vendor Assignment Rule:
            <Tooltip title="Rule used to automatically assign vendors to orders">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl sx={{ minWidth: 200 }} size="small" fullWidth>
                <Select
                  value={productSku.vendorAssignmentRule || ''}
                  onChange={e => handleChange('vendorAssignmentRule', e.target.value)}
                  sx={{ width: '100%', minWidth: 300 }}
                  displayEmpty
                >
                  <MenuItem value="">None</MenuItem>
                  <MenuItem value={VendorAssignmentRule.FASTEST_CYCLE_TIME}>
                    {VendorAssignmentRule.FASTEST_CYCLE_TIME}
                  </MenuItem>
                  <MenuItem value={VendorAssignmentRule.LOWEST_COST}>
                    {VendorAssignmentRule.LOWEST_COST}
                  </MenuItem>
                </Select>
              </FormControl>
            ) : (
              <Typography>
                {productSku.vendorAssignmentRule === VendorAssignmentRule.FASTEST_CYCLE_TIME
                  ? VendorAssignmentRule.FASTEST_CYCLE_TIME
                  : productSku.vendorAssignmentRule === VendorAssignmentRule.LOWEST_COST
                    ? VendorAssignmentRule.LOWEST_COST
                    : 'N/A'}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Production Cycle Time field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Production Cycle Time:
            <Tooltip title="Average time needed to manufacture this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl
                error={!!errors.productionCycleTime && touched.productionCycleTime}
                fullWidth
              >
                <TextField
                  size="small"
                  type="number"
                  value={productSku.productionCycleTime ?? ''}
                  onChange={e => handleChange('productionCycleTime', e.target.value)}
                  onBlur={() => handleBlur('productionCycleTime')}
                  InputProps={{
                    endAdornment: 'Days',
                  }}
                  inputProps={{ min: 1 }}
                  required
                  sx={{ width: '100%', minWidth: 300 }}
                />
                {errors.productionCycleTime && touched.productionCycleTime && (
                  <FormHelperText>{errors.productionCycleTime}</FormHelperText>
                )}
              </FormControl>
            ) : (
              <Typography>
                {productSku.productionCycleTime ? `${productSku.productionCycleTime} Days` : 'N/A'}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Max Capacity per Day field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Max Capacity per Day:
            <Tooltip title="Maximum number of units that can be produced per day">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl
                error={!!errors.maxCapacityPerDay && touched.maxCapacityPerDay}
                fullWidth
              >
                <TextField
                  size="small"
                  type="number"
                  value={productSku.maxCapacityPerDay ?? ''}
                  onChange={e => handleChange('maxCapacityPerDay', e.target.value)}
                  onBlur={() => handleBlur('maxCapacityPerDay')}
                  inputProps={{ min: 1 }}
                  required
                  sx={{ width: '100%', minWidth: 300 }}
                />
                {errors.maxCapacityPerDay && touched.maxCapacityPerDay && (
                  <FormHelperText>{errors.maxCapacityPerDay}</FormHelperText>
                )}
              </FormControl>
            ) : (
              <Typography>{productSku.maxCapacityPerDay || 'N/A'}</Typography>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ManufacturingVendorTab;
