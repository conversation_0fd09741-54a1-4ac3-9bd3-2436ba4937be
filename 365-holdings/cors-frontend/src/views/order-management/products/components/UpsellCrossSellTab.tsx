import {
  Typography,
  Box,
  Switch,
  FormControl,
  Select,
  MenuItem,
  Paper,
  Tooltip,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { ProductSku } from '@/types/product-sku';
import { useState, useEffect } from 'react';
import {
  tabContainerStyle,
  tabTitleStyle,
  rowStyle,
  labelStyle,
  infoIconStyle,
  controlContainerStyle,
} from './styles';
import apiClient from '@/utils/axios';

interface UpsellCrossSellTabProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
}

const UpsellCrossSellTab = ({
  productSku,
  isEdit = false,
  setProductSku,
}: UpsellCrossSellTabProps) => {
  const [availableSkus, setAvailableSkus] = useState<Array<{ id: string; sku: string }>>([]);
  useEffect(() => {
    const fetchAvailableSkus = async () => {
      try {
        const response = await apiClient.get('/api/product-skus');
        const skus = response.data.map((sku: any) => ({
          id: sku.id,
          sku: sku.sku,
        }));
        setAvailableSkus(skus);
      } catch (error) {
        console.error('Error fetching available SKUs:', error);
      }
    };
  }, []);

  const handleChange = (field: string, value: any) => {
    if (isEdit && setProductSku) {
      if (field === 'canUpSold' || field === 'canCrossSold') {
        setProductSku(prev => {
          if (prev[field] === value) {
            return prev;
          }
          return {
            ...prev,
            [field]: value,
          };
        });
      } else if (field === 'upSellParentSkuId') {
        setProductSku(prev => ({
          ...prev,
          upSellParentSku: value
            ? [
                {
                  upSellParentSku: {
                    id: value,
                    sku: availableSkus.find(sku => sku.id === value)?.sku || '',
                  },
                },
              ]
            : [],
        }));
      }
    }
  };

  return (
    <Box sx={tabContainerStyle}>
      <Paper elevation={0} sx={{ p: 3, mb: 6, bgcolor: 'background.default' }}>
        <Typography variant="h6" sx={tabTitleStyle}>
          Upsell & Cross-Sell Configuration
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Configure upsell and cross-sell relationships for this product
        </Typography>
      </Paper>

      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        {/* First row - Can Be Upsold */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Can Be Upsold?:
            <Tooltip title="Enable this product to be offered as an upsell">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.canUpSold || false}
                onChange={e => handleChange('canUpSold', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.canUpSold ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* Second row - Cross-sell enabled */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Cross-sell Enabled:
            <Tooltip title="Allow this product to be cross-sold with other products">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={productSku.canCrossSold || false}
                onChange={e => handleChange('canCrossSold', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.canCrossSold ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* Fourth row - Parent SKU dropdown */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Parent SKU for Upsells:
            <Tooltip title="Select the parent SKU that this product can be upsold from">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl size="small" fullWidth>
                <Select
                  value={
                    productSku.upSellParentSku?.length
                      ? productSku.upSellParentSku[0].upSellParentSku.id
                      : ''
                  }
                  onChange={e => handleChange('upSellParentSkuId', e.target.value)}
                  sx={{ width: '100%', minWidth: 300 }}
                  displayEmpty
                  disabled={!productSku.canUpSold}
                >
                  <MenuItem value="">None</MenuItem>
                  {availableSkus.map(skuItem => (
                    <MenuItem key={skuItem.id} value={skuItem.id}>
                      {skuItem.sku}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : (
              <Typography>
                {productSku.upSellParentSku?.length
                  ? productSku.upSellParentSku[0].upSellParentSku.sku
                  : 'N/A'}
              </Typography>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default UpsellCrossSellTab;
