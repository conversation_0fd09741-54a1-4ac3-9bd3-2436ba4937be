import { Box, Card, CardContent, Divider, Typography } from '@mui/material';
import QueuesTabs from './components/QueuesTabs';
import { TabQueues } from '@/types/queues.types';

const QueuesWrapper = ({ allQueues }: { allQueues: TabQueues[] }) => {
  return (
    <Card>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
          }}
        >
          <Typography variant="h5">Queues</Typography>
        </Box>

        <Divider sx={{ mb: 4 }} />

        <QueuesTabs allQueues={allQueues} />
      </CardContent>
    </Card>
  );
};

export default QueuesWrapper;
