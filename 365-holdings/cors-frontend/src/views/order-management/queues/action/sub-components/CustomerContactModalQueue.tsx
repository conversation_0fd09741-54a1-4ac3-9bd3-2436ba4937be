'use client';
import { useState, useRef, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Chip,
  Checkbox,
} from '@mui/material';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'react-toastify';
import useApiCall from '@/hooks/useApiCall';
import {
  CustomerContactFormType,
  CustomerContactReason,
  CustomerContactReasonsResponse,
} from '@/types/customer-contact.types';

const validationSchema = yup.object().shape({
  reason: yup.string().required('Please select a reason'),
  message: yup
    .string()
    .required('Message is required')
    .min(10, 'Message must be at least 10 characters'),
}) as yup.ObjectSchema<CustomerContactFormType>;

interface CustomerContactModalProps {
  open: boolean;
  onClose: () => void;
  queueItem?: any;
  queueId?: string;
}

const CustomerContactModal = ({ open, onClose, queueItem, queueId }: CustomerContactModalProps) => {
  const [contactReasons, setContactReasons] = useState<CustomerContactReason[]>([]);
  const dialogRef = useRef<HTMLDivElement>(null);

  const { isLoading, makeRequest: sendCustomerContact } = useApiCall(
    '/workflow-queues/customer-contact',
    'post',
    false,
  );

  const { makeRequest: fetchContactReasons, isLoading: isLoadingReasons } =
    useApiCall<CustomerContactReasonsResponse>(
      '/order-tracking/customer-contact-reasons',
      'get',
      false,
    );

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CustomerContactFormType>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      reason: '',
      message: '',
      selectedThreadColors: [],
    },
  });

  const selectedReason = useWatch({
    control,
    name: 'reason',
  });

  const selectedThreadColors = useWatch({
    control,
    name: 'selectedThreadColors',
  });

  // Handle reason change and populate message
  const handleReasonChange = (reason: string) => {
    setValue('reason', reason);
    setValue('selectedThreadColors', []); // Reset thread colors when reason changes

    // Get email template content from selected reason
    const foundReason = contactReasons.find(r => r.reason === reason);
    if (foundReason) {
      let templateBody = foundReason.emailTemplate.content;

      // If it's sweater thread color reason, add placeholder for colors
      if (reason === 'SWEATER_THREAD_COLOR') {
        templateBody = templateBody.replace(
          '{Sweater color image}',
          'Selected colors will appear here',
        );
      }

      setValue('message', templateBody);
    }
  };

  const handleThreadColorChange = (colorHex: string) => {
    const newSelectedColors = selectedThreadColors.includes(colorHex)
      ? selectedThreadColors.filter(c => c !== colorHex)
      : selectedThreadColors.length < 4
        ? [...selectedThreadColors, colorHex]
        : selectedThreadColors;

    setValue('selectedThreadColors', newSelectedColors);

    // Update message with selected colors for sweater color reasons
    const foundReason = contactReasons.find(r => r.reason === selectedReason);
    if (
      foundReason &&
      (selectedReason === 'SWEATER_THREAD_COLOR' || selectedReason === 'SWEATER_COLOR_LIMIT')
    ) {
      let templateBody = foundReason.emailTemplate.content;

      if (newSelectedColors.length > 0) {
        const colorDisplay = newSelectedColors
          .map(hex => {
            const color = foundReason.sweaterColors?.find(c => c.hex === hex);
            return color ? `■ ${color.name} (${color.hex})` : `■ ${hex}`;
          })
          .join('\n');

        templateBody = templateBody.replace('{Sweater color image}', colorDisplay);
      } else {
        templateBody = templateBody.replace('{Sweater color image}', 'No colors selected');
      }

      setValue('message', templateBody);
    }
  };

  const onSubmit = async (data: CustomerContactFormType) => {
    try {
      const response = await sendCustomerContact({
        body: {
          queueId: queueId,
          lineItemId: queueItem?.lineItems?.id,
          reason: data.reason,
          message: data.message,
          selectedColors: selectedThreadColors,
        },
      });

      if (response) {
        toast.success('Customer contact message sent successfully');
        onClose();
        setValue('reason', '');
        setValue('message', '');
        setValue('selectedThreadColors', []);
      }
    } catch (error) {
      toast.error('Failed to send customer contact message');
    }
  };

  const getSelectedReasonData = () => {
    return contactReasons.find(r => r.reason === selectedReason);
  };

  // Fetch contact reasons when modal opens
  useEffect(() => {
    if (open) {
      const loadData = async () => {
        try {
          const response = await fetchContactReasons();

          if (response) {
            setContactReasons(response.reasons || []);
          }
        } catch (error) {
          toast.error('Failed to load contact reasons');
        }
      };
      loadData();
    }
  }, [open, fetchContactReasons]);

  // Handle focus management
  useEffect(() => {
    if (open && dialogRef.current) {
      // Focus the first focusable element in the dialog
      const firstFocusable = dialogRef.current.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
      ) as HTMLElement;
      if (firstFocusable) {
        firstFocusable.focus();
      }
    }
  }, [open]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      disableEnforceFocus
      disableAutoFocus
      disableRestoreFocus
      keepMounted={false}
      aria-labelledby="customer-contact-dialog-title"
      slotProps={{
        backdrop: {
          onClick: onClose,
        },
      }}
      ref={dialogRef}
      container={() => document.body}
    >
      <DialogTitle id="customer-contact-dialog-title">Customer Contact Needed</DialogTitle>
      <DialogContent sx={{ minHeight: '400px' }}>
        {isLoadingReasons ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary">
              Loading contact reasons...
            </Typography>
          </Box>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box sx={{ mt: 2 }}>
              {/* Reason Dropdown */}
              <Controller
                name="reason"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth sx={{ mb: 4 }}>
                    <InputLabel sx={{ fontWeight: 500 }}>Select Contact Reason</InputLabel>
                    <Select
                      {...field}
                      label="Select Contact Reason"
                      onChange={e => handleReasonChange(e.target.value)}
                      error={!!errors.reason}
                      sx={{
                        '& .MuiSelect-select': {
                          py: 1.5,
                        },
                      }}
                    >
                      {contactReasons.map(reason => (
                        <MenuItem key={reason.reason} value={reason.reason} sx={{ py: 1 }}>
                          <Typography variant="body1">{reason.label}</Typography>
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.reason && (
                      <Typography color="error" variant="caption" sx={{ mt: 1, display: 'block' }}>
                        {errors.reason.message}
                      </Typography>
                    )}
                  </FormControl>
                )}
              />

              {/* Actions Display */}
              {selectedReason && getSelectedReasonData() && (
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="subtitle1"
                    sx={{ fontWeight: 600, mb: 2, color: 'text.primary' }}
                  >
                    Required Actions:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {getSelectedReasonData()?.actions.map((action, index) => (
                      <Chip
                        key={index}
                        label={action}
                        variant="outlined"
                        color="primary"
                        sx={{
                          fontWeight: 500,
                          '& .MuiChip-label': {
                            px: 2,
                          },
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Thread Color Selection (only for sweater thread color) */}
              {selectedReason === 'SWEATER_THREAD_COLOR' &&
                getSelectedReasonData()?.sweaterColors && (
                  <Box sx={{ mb: 4 }}>
                    <Typography
                      variant="subtitle1"
                      sx={{ fontWeight: 600, mb: 2, color: 'text.primary' }}
                    >
                      Select Thread Colors (Max 4):
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {getSelectedReasonData()?.sweaterColors?.map(color => (
                        <Box
                          key={color.hex}
                          sx={{
                            position: 'relative',
                            cursor: 'pointer',
                            border: `2px solid ${color.hex}`,
                            borderRadius: 1,
                            backgroundColor: color.hex,
                            color:
                              color.hex === '#fdfafa' ||
                              color.hex === '#edd9d9' ||
                              color.hex === '#e2dfdc' ||
                              color.hex === '#dcd3cc' ||
                              color.hex === '#c6b5a7' ||
                              color.hex === '#d6c6b4' ||
                              color.hex === '#eddea4' ||
                              color.hex === '#d1c6ae' ||
                              color.hex === '#c5d1d0'
                                ? '#333'
                                : '#fff',
                            p: 1,
                            minWidth: 80,
                            textAlign: 'center',
                            fontSize: '0.875rem',
                            fontWeight: 500,
                            '&:hover': {
                              opacity: 0.8,
                            },
                          }}
                          onClick={() => handleThreadColorChange(color.hex)}
                        >
                          {color.name}
                          {selectedThreadColors.includes(color.hex) && (
                            <Checkbox
                              checked={true}
                              size="small"
                              sx={{
                                position: 'absolute',
                                top: -8,
                                right: -8,
                                color: 'success.main',
                                borderRadius: '50%',
                                p: 0.5,
                                '& .MuiSvgIcon-root': {
                                  fontSize: '1rem',
                                },
                              }}
                            />
                          )}
                        </Box>
                      ))}
                    </Box>
                    {selectedThreadColors.length > 0 && (
                      <Typography variant="body2" color="text.secondary">
                        Selected: {selectedThreadColors.length}/4 colors
                      </Typography>
                    )}
                  </Box>
                )}

              {/* Message Text Field */}
              <Controller
                name="message"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    multiline
                    rows={8}
                    label="Message to Customer"
                    placeholder="The email template will be populated automatically when you select a reason..."
                    error={!!errors.message}
                    helperText={errors.message?.message}
                    sx={{
                      mb: 4,
                      '& .MuiInputBase-root': {
                        fontSize: '14px',
                      },
                    }}
                  />
                )}
              />

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', pt: 2 }}>
                <Button
                  variant="outlined"
                  onClick={onClose}
                  disabled={isSubmitting || isLoading}
                  sx={{ minWidth: '100px', py: 1.5 }}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  disabled={isSubmitting || isLoading}
                  sx={{ minWidth: '140px', py: 1.5 }}
                >
                  {isSubmitting || isLoading ? 'Sending...' : 'Send Message'}
                </Button>
              </Box>
            </Box>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CustomerContactModal;
