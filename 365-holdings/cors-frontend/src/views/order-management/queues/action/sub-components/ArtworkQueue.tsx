'use client';
import Button from '@/components/Button';
import { Box, Chip, Dialog, DialogContent, DialogTitle, Divider, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import SingleImageViewCard from '@/components/card-statistics/SingleImageViewCard';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import { useEffect, useState } from 'react';
import ProductAttributesViewer from '../../components/ProductAttributesViewer';
import { SingleQueueItem } from '@/types/queues.types';
import { useRouter } from 'nextjs-toploader/app';
import LoadingView from '@/components/LoadingView';
import { toast } from 'react-toastify';
import useApiCall from '@/hooks/useApiCall';
import AddArtworkRequest from './AddArtworkRequest';
import CustomerContactModal from './CustomerContactModalQueue';
import { handleDownload } from '@/services/queues.services';
import { useAbility } from '@/libs/casl/AbilityContext';
import { Actions, ActionsTarget } from '@/libs/casl/ability';

type FormType = {
  image: string[];
};
const ArtworkQueue = ({
  queueItem,
  queueId,
  actionType,
  isArtworkRevision,
}: {
  queueItem: SingleQueueItem | undefined;
  queueId: string;
  actionType: string;
  isArtworkRevision: boolean;
}) => {
  const ability = useAbility();

  const router = useRouter();
  const [showAddRequest, setShowAddRequest] = useState(false);
  const [showCustomerContact, setShowCustomerContact] = useState(false);
  const [upcomingQueue, setUpcomingQueue] = useState<SingleQueueItem | undefined>(undefined);
  const { isLoading: loading, makeRequest: requestUpdateQueueItem } = useApiCall<SingleQueueItem>(
    `/workflow-queues/upload-template-placement-file`,
    'post',
    false,
  );
  const hasAddRequestPermission = () => {
    if (isArtworkRevision) {
      return ability?.can(Actions.AddArtworkRevisionRequest, ActionsTarget.ARTWORK_REVISION_QUEUE);
    }
    const addRequestPermission = `Add Request ${actionType}`?.toString();
    return ability?.can(addRequestPermission as Actions, actionType);
  };
  useEffect(() => {
    if (upcomingQueue) {
      if (upcomingQueue?.lineItems == null) {
        router.push(`/ordermanagement/queues/?tab=${queueId}`);
      }
    }
  }, [upcomingQueue, router]);

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormType>({
    resolver: yupResolver(
      yup.object().shape({
        image: yup
          .array()
          .of(yup.string().required())
          .min(1, 'At least one art file is required')
          .max(1, 'Only one art file is allowed')
          .required('Art file is required'),
      }),
    ),
    defaultValues: {
      image: [],
    },
  });
  const onSubmit = async (data: FormType) => {
    const response = await requestUpdateQueueItem({
      body: {
        lineItemId: queueItem?.lineItems?.id,
        templatePlacementFileUrl: data?.image[0],
        queueId: queueId,
      },
    });
    if (response) {
      setUpcomingQueue(response);
      toast.success('Queue item updated successfully');
    }
  };

  if (loading) return <LoadingView />;

  if (upcomingQueue?.lineItems) {
    return (
      <ArtworkQueue
        queueItem={upcomingQueue}
        queueId={queueId}
        actionType={actionType}
        isArtworkRevision={isArtworkRevision}
      />
    );
  }

  return (
    <>
      <ProductAttributesViewer actionType={actionType} queueItem={queueItem} />
      {/* Images Section */}
      <Grid container spacing={4} sx={{ mb: 4 }}>
        {queueItem?.lineItems?.attachments?.map((attachment, index) => (
          <Grid
            key={index}
            size={{
              xs: 12,
              md:
                queueItem?.lineItems?.attachments && queueItem?.lineItems?.attachments?.length >= 3
                  ? 4
                  : 6,
            }}
          >
            <SingleImageViewCard
              imageUrl={attachment.attachment_url}
              title={`Pet ${index + 1}`}
              downloadUrl={attachment.attachment_url}
              imageName={attachment?.attachment_url?.split('/').pop() || ''}
            />
          </Grid>
        ))}
      </Grid>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name={`image`}
          control={control}
          rules={{ required: 'Image is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <ImageUploadField
              key="image-upload-field"
              control={control}
              errors={errors}
              setValue={setValue}
              name={`image`}
              minImages={1}
              maxImages={1}
              formValue={value}
              errorField={error?.message}
              title="Upload Art File"
              buttonText="Upload"
            />
          )}
        />
        <Grid container spacing={6} sx={{ mb: 4 }}>
          <Grid
            size={{ xs: 12, md: 6 }}
            sx={{
              flexWrap: 'wrap',
              height:
                queueItem?.lineItems?.requested_data &&
                queueItem?.lineItems?.requested_data?.length > 0
                  ? '200px'
                  : 'auto',
              overflow: 'auto',
            }}
          >
            <Box sx={{ mt: 4, maxWidth: '100%' }}>
              <Typography variant="h5" sx={{ mb: 2 }}>
                Artwork Requests
              </Typography>
              {queueItem?.lineItems?.requested_data &&
              queueItem?.lineItems?.requested_data?.length > 0 ? (
                queueItem?.lineItems?.requested_data?.map((requestedData, index) => (
                  <Box
                    key={index}
                    sx={{
                      px: 2,
                      py: 3,
                      border: '1px solid rgba(160, 155, 155, 0.28)',
                      borderRadius: 2,
                      mb: 2,
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: 2,
                        alignItems: 'center',
                      }}
                    >
                      <Typography variant="body1">
                        {requestedData.created_at
                          ? new Date(requestedData.created_at)
                              .toLocaleString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric',
                              })
                              .toUpperCase()
                          : '-'}
                      </Typography>
                      <Divider orientation="vertical" variant="middle" flexItem />

                      <Typography variant="body1">
                        {requestedData.created_at
                          ? new Date(requestedData.created_at).toLocaleTimeString()
                          : '-'}
                      </Typography>
                      <Divider orientation="vertical" variant="middle" flexItem />

                      <Typography variant="body1">{queueItem?.lineItems?.assigned_to}</Typography>
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ display: 'flex', flexDirection: 'row', gap: 1, flexWrap: 'wrap' }}>
                      {requestedData?.attachments?.map((att, index) => (
                        <Chip
                          label={att?.attachment_url?.split('/').pop() || ''}
                          onClick={() =>
                            handleDownload(
                              att?.attachment_url || '',
                              `${att?.attachment_url?.split('/').pop()}.png`,
                            )
                          }
                          size="medium"
                          variant="outlined"
                          style={{ marginRight: 4, marginTop: 4, width: 'fit-content' }}
                        />
                      ))}
                    </Box>
                  </Box>
                ))
              ) : (
                <Typography variant="body1">No artwork requests</Typography>
              )}
            </Box>
          </Grid>

          <Grid
            size={{ xs: 12, md: 6 }}
            sx={{
              flexWrap: 'wrap',
              height:
                queueItem?.lineItems?.requested_data &&
                queueItem?.lineItems?.requested_data?.length > 0
                  ? '200px'
                  : 'auto',
              overflow: 'auto',
            }}
          >
            {isArtworkRevision && (
              <Box sx={{ mt: 4, maxWidth: '100%' }}>
                <Typography variant="h5" sx={{ mb: 2 }}>
                  Revision Completed Art Files
                </Typography>
                {queueItem?.lineItems?.rejected_attachments &&
                queueItem?.lineItems?.rejected_attachments?.length > 0 ? (
                  queueItem?.lineItems?.rejected_attachments?.map((attachment, index) => (
                    <Box
                      key={index}
                      sx={{
                        px: 2,
                        py: 3,
                        border: '1px solid rgba(160, 155, 155, 0.28)',
                        borderRadius: 2,
                        mb: 2,
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'row',
                          gap: 2,
                          alignItems: 'center',
                        }}
                      >
                        <Typography variant="body1">
                          {attachment.created_at
                            ? new Date(attachment.created_at)
                                .toLocaleString('en-US', {
                                  month: 'short',
                                  day: 'numeric',
                                  year: 'numeric',
                                })
                                .toUpperCase()
                            : '-'}
                        </Typography>
                        <Divider orientation="vertical" variant="middle" flexItem />

                        <Typography variant="body1">
                          {attachment.created_at
                            ? new Date(attachment.created_at).toLocaleTimeString()
                            : '-'}
                        </Typography>
                        <Divider orientation="vertical" variant="middle" flexItem />

                        <Typography variant="body1">{queueItem?.lineItems?.assigned_to}</Typography>
                      </Box>
                      <Divider sx={{ my: 2 }} />

                      <Chip
                        label={attachment?.completed_art_file_url?.split('/').pop() || ''}
                        onClick={() =>
                          handleDownload(
                            attachment?.completed_art_file_url || '',
                            `${attachment?.completed_art_file_url?.split('/').pop()}.png`,
                          )
                        }
                        size="medium"
                        variant="outlined"
                        style={{ marginRight: 4, marginTop: 4 }}
                      />
                    </Box>
                  ))
                ) : (
                  <Typography variant="body1">No revision completed art files</Typography>
                )}
              </Box>
            )}
          </Grid>
        </Grid>

        {/* Actions Section */}
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            justifyContent: 'space-between',
            mt: 8,
            alignItems: 'center',
          }}
        >
          <Box>
            {hasAddRequestPermission() && (
              <Button
                variant="outlined"
                fullWidth
                size="small"
                title="Add Request"
                type="button"
                sx={{ width: '200px' }}
                onClick={() => setShowAddRequest(!showAddRequest)}
              />
            )}
          </Box>
          <Box sx={{ display: 'flex', gap: 2, height: '100%' }}>
            <Button
              variant="outlined"
              fullWidth
              size="small"
              color="warning"
              title="Customer Contact Needed"
              type="button"
              disabled={isSubmitting}
              sx={{ width: '250px' }}
              onClick={() => setShowCustomerContact(true)}
            />
            <Button
              variant="outlined"
              fullWidth
              size="small"
              title="Submit"
              type="submit"
              disabled={isSubmitting}
              sx={{ width: '250px' }}
            />
          </Box>
        </Box>
      </form>
      <Dialog
        open={showAddRequest}
        onClose={() => setShowAddRequest(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Add Artwork Request</DialogTitle>
        <DialogContent>
          <AddArtworkRequest
            queueId={queueId}
            queueItem={queueItem}
            setShowAddRequest={setShowAddRequest}
            setUpcomingQueue={setUpcomingQueue}
          />
        </DialogContent>
      </Dialog>
      <CustomerContactModal
        open={showCustomerContact}
        onClose={() => setShowCustomerContact(false)}
        queueItem={queueItem}
        queueId={queueId}
      />
    </>
  );
};

export default ArtworkQueue;
