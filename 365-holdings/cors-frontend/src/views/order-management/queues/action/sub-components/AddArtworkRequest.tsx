import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, TextField } from '@mui/material';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import { SingleQueueItem } from '@/types/queues.types';
import useApiCall from '@/hooks/useApiCall';
import { toast } from 'react-toastify';
type FormType = {
  image: string[];
  request: string;
};
const AddArtworkRequest = ({
  queueId,
  queueItem,
  setShowAddRequest,
  setUpcomingQueue,
}: {
  queueId: string;
  queueItem: SingleQueueItem | undefined;
  setShowAddRequest: (show: boolean) => void;
  setUpcomingQueue: (queue: SingleQueueItem) => void;
}) => {
  const { makeRequest: requestCreateArtworkRequest, isLoading: isLoadingCreateArtworkRequest } =
    useApiCall(`/workflow-queues/add-artwork-request`, 'post', false);
  const {
    handleSubmit,
    control,
    register,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormType>({
    resolver: yupResolver(
      yup.object().shape({
        image: yup
          .array()
          .of(yup.string().required())
          .min(1, 'At least one art file is required')
          .max(3, 'Only one art file is allowed')
          .required('Art file is required'),
        request: yup.string().required('Request is required'),
      }),
    ),
    defaultValues: {
      image: [],
      request: '',
    },
  });

  const onSubmit = async (data: FormType) => {
    const response = await requestCreateArtworkRequest({
      body: {
        queueId: queueId,
        lineItemId: queueItem?.lineItems?.id,
        attachmentUrls: data.image,
        notes: data.request,
      },
    });
    if (response) {
      toast.success('Artwork request created successfully');
      setShowAddRequest(false);
      setUpcomingQueue(response);
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <TextField
          fullWidth
          multiline
          rows={4}
          label="Reason"
          {...register('request')}
          error={!!errors.request}
          helperText={errors.request?.message}
          sx={{ my: 4 }}
        />{' '}
        <Controller
          name={`image`}
          control={control}
          rules={{ required: 'Image is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <ImageUploadField
              key="image-upload-field"
              control={control}
              errors={errors}
              setValue={setValue}
              name={`image`}
              minImages={1}
              maxImages={3}
              formValue={value}
              errorField={error?.message}
              title="Upload Art File"
              buttonText="Upload"
            />
          )}
        />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
          <Button
            variant="outlined"
            fullWidth
            size="small"
            type="submit"
            disabled={isSubmitting || isLoadingCreateArtworkRequest}
            sx={{ width: '150px' }}
          >
            {isLoadingCreateArtworkRequest ? 'Adding...' : 'Add Request'}
          </Button>
        </Box>
      </form>
    </div>
  );
};

export default AddArtworkRequest;
