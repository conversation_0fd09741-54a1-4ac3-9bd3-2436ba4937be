import { Box, Chip, Divider, Typography } from '@mui/material';
import React from 'react';
import Grid from '@mui/material/Grid2';
import { CropTypeEnum } from '@/constants/queue.constants';
import { FlagCircleRounded } from '@mui/icons-material';

const ProductAttributesViewer = ({
  actionType,
  queueItem,
}: {
  actionType: string;
  queueItem: any;
}) => {
  const attachment = queueItem?.attachments;
  const lineItems = queueItem?.lineItems;
  let attributes: Array<{ name: string; value: string }> = [];
  switch (true) {
    case actionType.toLowerCase().includes('crop review'):
    case actionType.toLowerCase().includes('crop needed'):
    case actionType.toLowerCase().includes('flagged crops'):
      attributes = [
        {
          name: 'Priority',
          value: (
            <Chip
              label={attachment?.priority || 'Standard'}
              variant="outlined"
              color={attachment?.priority ? 'warning' : 'success'}
            />
          ),
        },
        {
          name: 'Status',
          value: attachment?.attachment_status || '-',
        },
        {
          name: 'Order Date',
          value: attachment?.order_date
            ? new Date(attachment.order_date).toLocaleDateString()
            : '-',
        },
        {
          name: 'Order Number',
          value: attachment?.order_number || '-',
        },
        {
          name: 'Crop Type',
          value: CropTypeEnum[attachment?.cropType as keyof typeof CropTypeEnum] || '-',
        },
        ...(attachment?.flagged
          ? [
              {
                name: 'Flagged',
                value: <FlagCircleRounded sx={{ color: 'red' }} />,
              },
            ]
          : []),
      ];
      break;
    case actionType.toLowerCase().includes('template placement'):
    case actionType.toLowerCase().includes('ready for artwork'):
    case actionType.toLowerCase().includes('artwork revision'):
      attributes = [
        {
          name: 'Priority',
          value: (
            <Chip
              label={lineItems?.priority || 'Standard'}
              variant="outlined"
              color={lineItems?.priority ? 'warning' : 'success'}
            />
          ),
        },
        {
          name: 'Status',
          value: lineItems?.line_item_status || '-',
        },
        {
          name: 'Order Date',
          value: lineItems?.order_date ? new Date(lineItems.order_date).toLocaleDateString() : '-',
        },
        {
          name: 'Order Number',
          value: lineItems?.order_number || '-',
        },
        ...(lineItems?.crop_type
          ? [
              {
                name: 'Image Style',
                value: CropTypeEnum[lineItems?.crop_type as keyof typeof CropTypeEnum] || '-',
              },
            ]
          : []),
        {
          name: 'Product Name',
          value: lineItems?.product_name,
        },
        {
          name: 'SKU',
          value: lineItems?.sku,
        },
        {
          name: 'Quantity',
          value: lineItems?.quantity,
        },
        ...Object.entries(lineItems?.line_item_properties || {})
          .filter(
            ([key, value]) =>
              !key.startsWith('_') && value !== null && value !== '' && !key.includes('image_url'),
          )
          .map(([key, value]) => ({
            name: key,
            value: value,
          })),
        ...(lineItems?.artwork_type
          ? [
              {
                name: 'Art Style',
                value: lineItems?.artwork_type,
              },
            ]
          : []),
        ...(lineItems?.flagged
          ? [
              {
                name: 'Flagged',
                value: <FlagCircleRounded sx={{ color: 'red' }} />,
              },
            ]
          : []),
      ];
      break;
    default:
      attributes = [];
  }

  return (
    <>
      <Divider sx={{ my: 4 }} />

      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={4} alignItems="center">
          {attributes.map((item, index) => (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  flexWrap: 'wrap',
                }}
              >
                <Box sx={{ fontWeight: 'bold' }}>{item.name}:</Box>
                <Box sx={{ wordBreak: 'break-word' }}>{item.value || '-'}</Box>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
      <Divider sx={{ my: 6 }} />
    </>
  );
};

export default ProductAttributesViewer;
