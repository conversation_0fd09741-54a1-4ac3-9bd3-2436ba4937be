import React from 'react';
import Grid from '@mui/material/Grid2';
import { Box, Chip, Typography } from '@mui/material';
import StatusChip from '@/components/StatusChip';
import { OrderDetailsTabProps } from '@/types/orderDetailsTab.types';

interface MainOrderDetailsProps {
  order: OrderDetailsTabProps['orderData'];
}

const MainOrderDetails = ({ order }: MainOrderDetailsProps) => {
  return (
    <Grid container spacing={3} sx={{ mt: 2 }}>
      <Grid size={{ xs: 12, md: 3 }}>
        <Typography variant="body1" fontWeight="bold">
          Shopify Order Number
        </Typography>
        <Typography>{order?.shopifyOrderNumber}</Typography>
      </Grid>

      <Grid size={{ xs: 12, md: 3 }}>
        <Typography variant="body1" fontWeight="bold">
          Order Date
        </Typography>
        <Typography>
          {order?.orderDate
            ? new Date(order.orderDate)
                .toLocaleString('en-US', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false,
                })
                .replace(',', '')
            : ''}
        </Typography>
      </Grid>

      <Grid size={{ xs: 12, md: 3 }}>
        <Typography variant="body1" fontWeight="bold">
          Status
        </Typography>
        <StatusChip status={order?.orderStatus} />
      </Grid>

      <Grid size={{ xs: 12, md: 3 }}>
        <Typography variant="body1" fontWeight="bold">
          Priority
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
          {Array.isArray(order?.priorities) && order?.priorities?.length > 0 ? (
            order?.priorities?.map((priority: any, index: any) => (
              <Chip
                key={index}
                label={priority}
                size="small"
                sx={{
                  backgroundColor: priority.toUpperCase().includes('HOLIDAY')
                    ? '#ffcccc'
                    : priority.toUpperCase().includes('RUSH')
                      ? '#ffe0b2'
                      : '#ccc',
                  color: '#000',
                }}
              />
            ))
          ) : (
            <Chip
              label="Standard"
              size="small"
              sx={{
                backgroundColor: '#ccc',
                color: '#000',
              }}
            />
          )}
        </Box>
      </Grid>

      <Grid size={{ xs: 12, md: 3 }}>
        <Typography variant="body1" fontWeight="bold">
          Order Items Count
        </Typography>
        <Typography>{order?.itemCount}</Typography>
      </Grid>

      <Grid size={{ xs: 12, md: 3 }}>
        <Typography variant="body1" fontWeight="bold">
          Customer Name
        </Typography>
        <Typography>{order.customerFirstName + ' ' + order.customerLastName}</Typography>
      </Grid>

      <Grid size={{ xs: 12, md: 6 }}>
        <Typography variant="body1" fontWeight="bold">
          Customer Email
        </Typography>
        <Typography>{order?.customerEmail}</Typography>
      </Grid>
    </Grid>
  );
};

export default MainOrderDetails;
