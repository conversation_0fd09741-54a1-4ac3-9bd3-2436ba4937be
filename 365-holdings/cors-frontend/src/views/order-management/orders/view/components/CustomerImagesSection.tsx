import { useState } from 'react';
import {
  Typography,
  Box,
  Paper,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Link,
} from '@mui/material';
import Grid2 from '@mui/material/Grid2';
import DownloadIcon from '@mui/icons-material/Download';
import CloseIcon from '@mui/icons-material/Close';
import { CustomerImagesSectionProps } from '@/types/customer-images.types';

const CustomerImagesSection = ({ metadata, formatDate }: CustomerImagesSectionProps) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const extractImageUrls = (metadata: Record<string, any> | null | undefined): string[] => {
    if (!metadata) return [];

    const imageUrls = Object.entries(metadata)
      .filter(([key]) => key.includes('_image_url') || key.startsWith('image_url_'))
      .map(([_, value]) => value as string)
      .filter(url => url && typeof url === 'string');

    return imageUrls;
  };

  const isCorsUploaded = (metadata: Record<string, any> | null | undefined): boolean => {
    if (!metadata) return false;
    return Object.keys(metadata).some(key => key.startsWith('image_url_'));
  };

  const imageUrls = extractImageUrls(metadata);
  const corsUploaded = isCorsUploaded(metadata);

  const handleImageClick = (imageUrl: string) => {
    setSelectedImage(imageUrl);
  };

  const handleCloseDialog = () => {
    setSelectedImage(null);
  };

  const handleDownload = (imageUrl: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    // Create a link element
    const link = document.createElement('a');
    link.href = imageUrl;
    // Extract filename from URL
    const fileName = imageUrl.split('/').pop() || 'image.jpg';
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!imageUrls || imageUrls.length === 0) {
    return (
      <Paper sx={{ p: 2, mt: 2, bgcolor: 'background.paper' }}>
        <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
          Customer Images
        </Typography>
        <Box sx={{ py: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No customer images provided
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 2, mt: 2, bgcolor: 'background.paper' }}>
      <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
        {corsUploaded ? 'CORS-uploaded Images' : 'Customer-provided Images'}
      </Typography>

      {imageUrls.length > 0 ? (
        <Grid2 container spacing={2}>
          {imageUrls.map((imageUrl, index) => (
            <Grid2 size={{ xs: 12, sm: 6, md: 4 }} key={index}>
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  bgcolor: 'background.default',
                  height: '100%',
                }}
              >
                {/* Image Title */}
                <Typography variant="subtitle2" gutterBottom>
                  {corsUploaded ? `CORS Image ${index + 1}` : `Customer Image ${index + 1}`}
                </Typography>
                <Box
                  sx={{
                    cursor: 'pointer',
                    overflow: 'hidden',
                    borderRadius: 1,
                    mb: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'rgba(0, 0, 0, 0.05)',
                    p: 1,
                    height: 150,
                  }}
                  onClick={() => handleImageClick(imageUrl)}
                >
                  <img
                    src={imageUrl}
                    alt={`Customer Image ${index + 1}`}
                    style={{
                      maxWidth: '100%',
                      maxHeight: '140px',
                      objectFit: 'contain',
                    }}
                  />
                </Box>
                <Typography
                  variant="caption"
                  sx={{
                    display: 'block',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    mb: 1,
                  }}
                  title={imageUrl.split('/').pop() || `image-${index}.jpg`}
                >
                  {imageUrl.split('/').pop() || `image-${index}.jpg`}
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<DownloadIcon />}
                  onClick={e => handleDownload(imageUrl, e)}
                  fullWidth
                >
                  Download
                </Button>
              </Paper>
            </Grid2>
          ))}
        </Grid2>
      ) : (
        <Box sx={{ py: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No customer images provided
          </Typography>
        </Box>
      )}

      {/* Full-size image dialog */}
      <Dialog open={!!selectedImage} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        {selectedImage && (
          <>
            <DialogTitle
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Link
                  component="button"
                  variant="subtitle1"
                  onClick={() => handleDownload(selectedImage)}
                  underline="hover"
                  sx={{ fontWeight: 'medium', cursor: 'pointer' }}
                >
                  {selectedImage.split('/').pop() || 'image.jpg'}
                </Link>
              </Box>
              <IconButton edge="end" onClick={handleCloseDialog}>
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent sx={{ textAlign: 'center', p: 2 }}>
              <img
                src={selectedImage}
                alt="Full size image"
                style={{
                  maxWidth: '100%',
                  maxHeight: 'calc(100vh - 200px)',
                  objectFit: 'contain',
                }}
              />
            </DialogContent>
          </>
        )}
      </Dialog>
    </Paper>
  );
};

export default CustomerImagesSection;
