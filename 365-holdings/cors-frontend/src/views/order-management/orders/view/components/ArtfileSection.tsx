import { useState } from 'react';
import { Typography, Box, Paper, Button, Chip } from '@mui/material';
import Grid2 from '@mui/material/Grid2';
import DownloadIcon from '@mui/icons-material/Download';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { ArtfileSectionProps } from '@/types/artfile.types';
import ImagePreviewDialog from '@/components/ImagePreviewDialog';
import { toast } from 'react-toastify';
import { UploadImage } from '@/actions/image-upload';

const ArtfileSection = ({ item, formatDate }: ArtfileSectionProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
      setPreviewUrl(URL.createObjectURL(e.target.files[0]));
      setUploadedUrl(null);
      e.target.value = '';
    }
  };

  const handleSubmit = async () => {
    if (!selectedFile) return;
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      const url = await UploadImage(formData);
      setUploadedUrl(url);
      toast.success('Artwork uploaded successfully');
      setSelectedFile(null);
      setPreviewUrl(null);
    } catch {
      toast.error('Failed to upload artwork');
    } finally {
      setUploading(false);
    }
  };

  const artworkFiles = item?.artworkFiles || [];

  return (
    <Paper sx={{ p: 2, mt: 2, bgcolor: 'background.paper' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1" fontWeight="bold">
          Artwork Files
        </Typography>
        <Button
          variant="contained"
          startIcon={<CloudUploadIcon />}
          size="small"
          component="label"
          disabled={!item?.productSku?.artworkRequired}
        >
          Upload Artwork
          <input type="file" hidden accept="image/*,.pdf,.ai,.psd" onChange={handleFileInput} />
        </Button>
      </Box>

      {/* Preview Section */}
      {selectedFile && (
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="subtitle2">{selectedFile.name}</Typography>
          {selectedFile.type.startsWith('image/') ? (
            <>
              <img
                src={previewUrl!}
                alt="Preview"
                style={{ maxWidth: 300, maxHeight: 200, cursor: 'pointer' }}
                onClick={() => setShowPreviewDialog(true)}
              />
              <ImagePreviewDialog
                open={showPreviewDialog}
                onClose={() => setShowPreviewDialog(false)}
                imageUrl={previewUrl}
                title="Artwork Preview"
              />
            </>
          ) : (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              No preview available for this file type.
            </Typography>
          )}
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => {
                setSelectedFile(null);
                setPreviewUrl(null);
              }}
              disabled={uploading}
            >
              Cancel
            </Button>
            <Button variant="contained" color="primary" onClick={handleSubmit} disabled={uploading}>
              {uploading ? 'Uploading...' : 'Submit'}
            </Button>
          </Box>
        </Box>
      )}

      {/* Uploaded File/Image Display */}
      {uploadedUrl && (
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          {uploadedUrl.endsWith('.jpg') ||
          uploadedUrl.endsWith('.jpeg') ||
          uploadedUrl.endsWith('.png') ||
          uploadedUrl.endsWith('.gif') ? (
            <Box
              sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}
            >
              <img
                src={uploadedUrl}
                alt="Uploaded Artwork"
                style={{ maxWidth: 300, maxHeight: 250, width: '100%', objectFit: 'contain' }}
              />
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                sx={{ mt: 2 }}
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = uploadedUrl;
                  link.download = uploadedUrl.split('/').pop() || 'artwork.jpg';
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                Download
              </Button>
            </Box>
          ) : (
            <a href={uploadedUrl} target="_blank" rel="noopener noreferrer">
              Download Uploaded File
            </a>
          )}
        </Box>
      )}

      {artworkFiles.length > 0 ? (
        <Grid2 container spacing={2}>
          {artworkFiles.map((file: any, index: number) => (
            <Grid2 size={{ xs: 12, sm: 6, md: 4 }} key={file.id || index}>
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  bgcolor: 'background.default',
                  height: '100%',
                }}
              >
                {/* File Title */}
                <Typography variant="subtitle2" gutterBottom>
                  {file.fileType === 'artwork' ? 'Artwork File' : 'Template File'}
                </Typography>
                <Box
                  sx={{
                    cursor: 'pointer',
                    overflow: 'hidden',
                    borderRadius: 1,
                    mb: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'rgba(0, 0, 0, 0.05)',
                    p: 1,
                    height: 150,
                  }}
                >
                  {file.thumbnailUrl ? (
                    <img
                      src={file.thumbnailUrl}
                      alt={file.fileName}
                      style={{
                        maxWidth: '100%',
                        maxHeight: '140px',
                        objectFit: 'contain',
                      }}
                    />
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        width: '100%',
                      }}
                    >
                      <Typography variant="body2" color="text.secondary" align="center">
                        {file.fileName.split('.').pop()?.toUpperCase()} File
                      </Typography>
                      <Typography variant="caption" color="text.secondary" align="center">
                        (No preview available)
                      </Typography>
                    </Box>
                  )}
                </Box>
                <Typography
                  variant="caption"
                  sx={{
                    display: 'block',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    mb: 1,
                  }}
                  title={file.fileName}
                >
                  {file.fileName}
                </Typography>
                <Box
                  sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Chip
                    label={file.status || 'Uploaded'}
                    size="small"
                    color={
                      file.status === 'Approved'
                        ? 'success'
                        : file.status === 'Rejected'
                          ? 'error'
                          : 'default'
                    }
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {formatDate(file.uploadedAt)}
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<DownloadIcon />}
                  onClick={() => {
                    // Create a link element
                    const link = document.createElement('a');
                    link.href = file.fileUrl;
                    link.download = file.fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }}
                  fullWidth
                  sx={{ mt: 1 }}
                >
                  Download
                </Button>
              </Paper>
            </Grid2>
          ))}
        </Grid2>
      ) : !selectedFile && !previewUrl && !uploadedUrl ? (
        <Box sx={{ py: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {item?.productSku?.artworkRequired
              ? 'No artwork files have been uploaded yet'
              : 'This item does not require artwork files'}
          </Typography>
        </Box>
      ) : null}
    </Paper>
  );
};

export default ArtfileSection;
