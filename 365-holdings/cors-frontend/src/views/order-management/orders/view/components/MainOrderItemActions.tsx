import React, { useState } from 'react';
import Grid from '@mui/material/Grid2';
import { Button } from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import CancelIcon from '@mui/icons-material/Cancel';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import ContactSupportIcon from '@mui/icons-material/ContactSupport';
import {
  canInitiateCustomerContact,
  isItemCancellable,
  isItemEditable,
  isItemRemakeable,
} from '@/utils/ordermoduleUtils';
import EditItemDialog from '../dialogs/EditItemDialog';
import CancelItemDialog from '../dialogs/CancelItemDialog';
import RemakeItemDialog from '../dialogs/RemakeItemDialog';
import useApiCall from '@/hooks/useApiCall';
import CustomerContactDialog from '../dialogs/CustomerContactDialog';
import { OrderLineItemProps } from '@/types/orderDetailsTab.types';

const MainOrderItemActions = ({
  item,
  orderId,
  refreshOrderData,
}: {
  item: OrderLineItemProps;
  orderId: string;
  refreshOrderData: () => void;
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [actionType, setActionType] = useState('');

  const { isLoading: isUpdating, makeRequest: updateItem } = useApiCall(
    `/orders/line-items/${item.id}`,
    'put',
    false,
    {},
  );

  const { isLoading: isRemaking, makeRequest: remakeItem } = useApiCall(
    `/orders/line-items/remake`,
    'post',
    false,
    {},
  );

  const { isLoading: isCustomerContacting, makeRequest: customerContactItem } = useApiCall(
    `/orders/${orderId}/customer-contact`,
    'post',
    false,
    {},
  );

  return (
    <>
      <Grid
        size={12}
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          flexWrap: 'wrap',
          gap: 2,
          mt: 2,
        }}
      >
        <Button
          variant="contained"
          startIcon={<EditIcon />}
          onClick={() => {
            setActionType('edit');
            setIsModalOpen(true);
          }}
          disabled={!isItemEditable(item)}
          size="small"
        >
          Edit Item
        </Button>

        <Button
          variant="outlined"
          color="error"
          startIcon={<CancelIcon />}
          onClick={() => {
            setActionType('cancel');
            setIsModalOpen(true);
          }}
          disabled={!isItemCancellable(item)}
          size="small"
        >
          Cancel Item
        </Button>

        <Button
          variant="outlined"
          color="warning"
          startIcon={<RestartAltIcon />}
          onClick={() => {
            setActionType('remake');
            setIsModalOpen(true);
          }}
          disabled={!isItemRemakeable(item)}
          size="small"
        >
          Remake Item
        </Button>

        <Button
          variant="outlined"
          color="info"
          startIcon={<ContactSupportIcon />}
          onClick={() => {
            setActionType('contact');
            setIsModalOpen(true);
          }}
          disabled={!canInitiateCustomerContact() || true}
          size="small"
        >
          Customer Contact Needed
        </Button>
      </Grid>
      {actionType === 'edit' && (
        <EditItemDialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          currentItem={item}
          onSave={async (data: any) => {
            await updateItem({
              body: data,
            });
            setIsModalOpen(false);
            refreshOrderData();
          }}
          updating={isUpdating}
        />
      )}

      {actionType === 'cancel' && (
        <CancelItemDialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          currentItem={item}
          onCancel={async (reason: string) => {
            if (!reason) return;
            await updateItem({
              body: {
                status: 'cancelled',
                cancelReason: reason,
              },
            });
            setIsModalOpen(false);
            refreshOrderData();
          }}
          updating={isUpdating}
        />
      )}

      {actionType === 'remake' && (
        <RemakeItemDialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          currentItem={item}
          onSubmit={async (data: {
            remakeReasons: string[];
            detailedReasons: string[];
            sku: string;
            images: File[];
            imageUrls?: string[];
          }) => {
            const metadata: Record<string, any> = {};
            if (data.imageUrls && data.imageUrls.length > 0) {
              data.imageUrls.forEach((url, index) => {
                metadata[`image_url_${index + 1}`] = url;
              });
            }
            await remakeItem({
              body: {
                productSkuId: data.sku,
                lineItemId: item.id,
                remakeReason: data.remakeReasons,
                detailedRemakeReason: data.detailedReasons,
                metadata,
              },
            });
            setIsModalOpen(false);
            refreshOrderData();
          }}
          updating={isRemaking}
        />
      )}
      {actionType === 'contact' && (
        <CustomerContactDialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSend={questions => {
            const formData = new FormData();
            formData.append('itemId', item.id);
            formData.append('questions', JSON.stringify(questions));
            customerContactItem({
              body: formData,
            });
            refreshOrderData();
          }}
          customerImages={item?.customerImages || []}
          artFiles={item?.artFiles || []}
          itemId={item?.id || ''}
          updating={isCustomerContacting}
        />
      )}
    </>
  );
};

export default MainOrderItemActions;
