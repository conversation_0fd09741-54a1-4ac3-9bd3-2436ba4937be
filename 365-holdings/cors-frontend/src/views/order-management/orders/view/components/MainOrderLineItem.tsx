import React, { useState, useEffect } from 'react';
import Grid from '@mui/material/Grid2';
import {
  Accordion,
  AccordionSummary,
  Box,
  Typography,
  Tooltip,
  IconButton,
  Chip,
  AccordionDetails,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FlagIcon from '@mui/icons-material/Flag';
import StatusChip from '@/components/StatusChip';

import { canFlagLineItem, checkWorkflowCategory } from '@/utils/ordermoduleUtils';
import MainOrderItemActions from './MainOrderItemActions';
import FlagItemDialog from '../dialogs/FlagItemDialog';
import useApiCall from '@/hooks/useApiCall';
import SingleImageViewCard from '@/components/card-statistics/SingleImageViewCard';
import { CustomAttachmentProps, OrderLineItemProps } from '@/types/orderDetailsTab.types';

const MainOrderLineItem = ({
  item,
  index,
  orderId,
  refreshOrderData,
  initiallyExpanded = false,
}: {
  item: OrderLineItemProps;
  index: number;
  orderId: string;
  refreshOrderData: () => void;
  initiallyExpanded?: boolean;
}) => {
  const [expanded, setExpanded] = useState<string>(initiallyExpanded ? `panel${index}` : '');
  const [isFlagDialogOpen, setIsFlagDialogOpen] = useState(false);

  useEffect(() => {
    if (initiallyExpanded) {
      setExpanded(`panel${index}`);
    }
  }, [initiallyExpanded, index]);
  const isAddon = item?.productSku?.isAddon;
  const { isLoading: isFlagLoading, makeRequest: flagItem } = useApiCall(
    `/orders/line-items/${item.id}`,
    'put',
    false,
    {},
  );
  const {
    images,
    artFiles,
    showArtfile,
    showCustomerImages,
    showCutoutProImage,
  }: CustomAttachmentProps = checkWorkflowCategory(item);

  return (
    <>
      <Accordion
        key={item.id || index}
        expanded={expanded === `panel${index}`}
        onChange={(event: React.SyntheticEvent, isExpanded: boolean) => {
          setExpanded(isExpanded ? `panel${index}` : '');
        }}
        sx={{ mb: 2 }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls={`panel${index}-content`}
          id={`panel${index}-header`}
          sx={{
            backgroundColor: item.flagged ? 'rgba(255, 235, 235, 0.5)' : 'background.default',
            '&.Mui-expanded': {
              borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
            },
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', gap: 46 }}>
            <Typography variant="body1" fontWeight="bold">
              Item #: {item.itemNumber}
            </Typography>
            <Typography variant="body1" sx={{ flexGrow: 1 }}>
              SKU: {item?.productSku?.sku || 'Not Provided'}
            </Typography>
            {!isAddon && (
              <Tooltip title={item.flagged ? 'Unflag this item' : 'Flag this item'}>
                <Box
                  sx={{
                    position: 'relative',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      width: '28px',
                      height: '28px',
                      borderRadius: '50%',
                      backgroundColor: item.flagged ? 'error.light' : 'success.light',
                    }}
                  />
                  <IconButton
                    size="small"
                    onClick={e => {
                      e.stopPropagation();
                      setIsFlagDialogOpen(true);
                    }}
                    disabled={!canFlagLineItem()}
                    sx={{
                      color: 'text.primary',
                      position: 'relative',
                      zIndex: 1,
                      '&:hover': {
                        backgroundColor: 'transparent',
                      },
                    }}
                  >
                    <FlagIcon />
                  </IconButton>
                </Box>
              </Tooltip>
            )}
          </Box>
        </AccordionSummary>

        <AccordionDetails>
          <Grid container spacing={3} sx={{ mt: 2, width: '100%' }}>
            <Grid container spacing={3} sx={{ mt: 2, width: '100%' }}>
              {/* First row */}
              <Grid size={{ xs: 6, md: 3 }}>
                <Typography variant="body1" fontWeight="bold">
                  Item Number
                </Typography>
                <Typography>{item?.itemNumber}</Typography>
              </Grid>

              <Grid size={{ xs: 6, md: 3 }}>
                <Typography variant="body1" fontWeight="bold">
                  SKU
                </Typography>
                <Typography>{item?.productSku?.sku}</Typography>
              </Grid>

              <Grid size={{ xs: 6, md: 3 }}>
                <Typography variant="body1" fontWeight="bold">
                  Quantity
                </Typography>
                <Typography>{item.quantity}</Typography>
              </Grid>

              <Grid size={{ xs: 6, md: 3 }}>
                <Typography variant="body1" fontWeight="bold">
                  Status
                </Typography>
                <StatusChip status={item?.status} variant="item" />
              </Grid>

              {/* Second row */}
              <Grid size={{ xs: 6, md: 3 }}>
                <Typography variant="body1" fontWeight="bold">
                  Remake Status
                </Typography>
                <Chip
                  label={item?.isRemake ? 'Yes' : 'No'}
                  size="small"
                  sx={{
                    mt: 1,
                    bgcolor: item.isRemake ? '#e1bee7' : '#bbdefb',
                    color: '#000',
                  }}
                />
              </Grid>

              <Grid size={{ xs: 6, md: 3 }}>
                <Typography variant="body1" fontWeight="bold">
                  Priority
                </Typography>
                <Chip
                  label={item?.priority || 'None'}
                  size="small"
                  sx={{
                    bgcolor: item?.priority?.includes('Rush') ? '#ffe0b2' : '#e0e0e0',
                    color: '#000',
                  }}
                />
              </Grid>

              {(item.remakeReason || (item.remakeReasons && item.remakeReasons.length > 0)) && (
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body1" fontWeight="bold">
                    Remake Reasons
                  </Typography>
                  <Typography>
                    {item.remakeReasons && Array.isArray(item.remakeReasons)
                      ? item.remakeReasons.join(', ')
                      : item.remakeReason
                        ? Array.isArray(item.remakeReason)
                          ? item.remakeReason.join(', ')
                          : item.remakeReason
                        : 'Not specified'}
                  </Typography>
                </Grid>
              )}

              {item.detailedRemakeReason && item.detailedRemakeReason.length > 0 && (
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body1" fontWeight="bold">
                    Detailed Remake Reasons
                  </Typography>
                  <Typography>
                    {Array.isArray(item.detailedRemakeReason)
                      ? item.detailedRemakeReason.join(', ')
                      : item.detailedRemakeReason}
                  </Typography>
                </Grid>
              )}

              {item.flagged && (
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body1" fontWeight="bold">
                    Flag Reason
                  </Typography>
                  <Typography color="error">{item.flagReason || 'Item is flagged'}</Typography>
                </Grid>
              )}

              {item.type === 'addon' && (
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body1" fontWeight="bold">
                    Add-on Type
                  </Typography>
                  <Typography>{item.addonType || 'Standard'}</Typography>
                </Grid>
              )}

              {item.shopifyVariant && (
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body1" fontWeight="bold">
                    Shopify Variant
                  </Typography>
                  <Typography>{item.shopifyVariant}</Typography>
                </Grid>
              )}

              {item.customOptions && (
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body1" fontWeight="bold" sx={{ mb: 1 }}>
                    Custom Options
                  </Typography>
                  <Box sx={{ pl: 2 }}>
                    {Object.entries(item.customOptions).map(([key, value]) => (
                      <Typography key={key} sx={{ mb: 0.5 }}>
                        <strong>{key}:</strong> {String(value)}
                      </Typography>
                    ))}
                  </Box>
                </Grid>
              )}
            </Grid>

            {/* Artwork and Images Section */}
            <Grid container spacing={3} sx={{ mt: 2, width: '100%' }}>
              {!isAddon && (
                <>
                  {images.length > 0 && !isAddon && showCustomerImages && (
                    <Grid size={12}>
                      <Typography variant="h5" sx={{ mb: 5, mt: 5, fontWeight: 'bold' }}>
                        Customer Images
                      </Typography>
                      <Grid container spacing={3} sx={{ mt: 2, width: '100%' }}>
                        {images.map((image: CustomAttachmentProps['images'][0], ind: number) => (
                          <Grid
                            key={`customer-image-${ind}-${image.url || ind}`}
                            size={{ xs: 12, md: 4 }}
                          >
                            <SingleImageViewCard
                              imageUrl={image.url || ''}
                              title={`Customer Image ${ind + 1}`}
                              downloadUrl={image.url || ''}
                              imageName={image.baseName}
                              isDanger={true}
                              small={true}
                            />
                          </Grid>
                        ))}
                      </Grid>
                    </Grid>
                  )}
                  {showCutoutProImage && images.length > 0 && !isAddon && (
                    <Grid size={12}>
                      <Typography variant="h5" sx={{ mb: 5, mt: 5, fontWeight: 'bold' }}>
                        Cutout Pro Image
                      </Typography>
                      <Grid container spacing={3} sx={{ mt: 2, width: '100%' }}>
                        {images.map((image: CustomAttachmentProps['images'][0], ind: number) => (
                          <Grid
                            key={`cutout-pro-image-${ind}-${image.cutoutProImageUrl || ind}`}
                            size={{ xs: 12, md: 4 }}
                          >
                            <SingleImageViewCard
                              imageUrl={image.cutoutProImageUrl || ''}
                              title={`Cutout Pro Image ${ind + 1}`}
                              downloadUrl={image.cutoutProImageUrl || ''}
                              imageName={image.cutoutProImageName || ''}
                              isDanger={true}
                              small={true}
                            />
                          </Grid>
                        ))}
                      </Grid>
                    </Grid>
                  )}
                  {artFiles.length > 0 && !isAddon && showArtfile && (
                    <Grid size={12}>
                      <Typography variant="h5" sx={{ mb: 5, mt: 5, fontWeight: 'bold' }}>
                        Art Files
                      </Typography>
                      <Grid container spacing={3} sx={{ mt: 2, width: '100%' }}>
                        {artFiles.map(
                          (image: CustomAttachmentProps['artFiles'][0], ind: number) => (
                            <Grid
                              key={`art-file-${ind}-${image.url || ind}`}
                              size={{ xs: 12, md: 4 }}
                            >
                              <SingleImageViewCard
                                imageUrl={image.url || ''}
                                title={`Art File ${ind + 1}`}
                                downloadUrl={image.url || ''}
                                imageName={image.baseName}
                                isDanger={true}
                                small={true}
                              />
                            </Grid>
                          ),
                        )}
                      </Grid>
                    </Grid>
                  )}
                </>
              )}
            </Grid>
            <MainOrderItemActions
              item={item}
              orderId={orderId}
              refreshOrderData={refreshOrderData}
            />
          </Grid>
        </AccordionDetails>
      </Accordion>
      <FlagItemDialog
        open={isFlagDialogOpen}
        onClose={() => setIsFlagDialogOpen(false)}
        currentItem={item}
        onFlag={async (flagReason: string, flagged: boolean) => {
          await flagItem({
            body: {
              flagged: flagged,
              ...(flagged ? { flagReason: flagReason } : {}),
            },
          });
          setIsFlagDialogOpen(false);
          refreshOrderData();
        }}
        updating={isFlagLoading}
      />
    </>
  );
};

export default MainOrderLineItem;
