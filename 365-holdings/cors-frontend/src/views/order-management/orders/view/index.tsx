'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'nextjs-toploader/app';
import { Typography, Card, CardContent, Button, Box, Divider, Tab, Tabs } from '@mui/material';
import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';
import LoadingView from '@/components/LoadingView';
import OrderDetailsDialog from './tabs/OrderDetailsDialog';
import OrderDetailsTab from './tabs/OrderDetailsTab';
import { useSearchParams } from 'next/navigation';
import useApiCall from '@/hooks/useApiCall';
import DataError from '@/components/DataError';

const OrderView = ({ id }: { id: string }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [itemNotes, setItemNotes] = useState<Record<string, string[]>>({});
  const [activeTab, setActiveTab] = useState('0');

  // Get the lineitem parameter from URL
  const lineItemParam = searchParams.get('lineitem');

  const {
    data: orderData,
    isLoading: isOrderLoading,
    error: orderError,
    makeRequest: fetchOrderData,
  } = useApiCall(`/orders/${id}`, 'get', false, {});

  useEffect(() => {
    if (id) {
      fetchOrderData();
    }
  }, [id, fetchOrderData]);

  const handleBack = () => {
    // Use returnUrl parameter if available, otherwise fallback to base URL
    const returnUrl = searchParams.get('returnUrl');
    if (returnUrl) {
      router.push(returnUrl, { scroll: false });
    } else {
      router.push('/ordermanagement/orders', { scroll: false });
    }
  };
  if (isOrderLoading) {
    return <LoadingView />;
  }
  if (orderError) {
    return <DataError />;
  }
  return (
    <Card>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
          }}
        >
          <Typography variant="h5">Order #{orderData?.shopifyOrderNumber}</Typography>
          <Box>
            <Button
              variant="contained"
              color="primary"
              onClick={() => {
                setDetailsOpen(true);
              }}
              sx={{ mr: 2 }}
            >
              Order Details
            </Button>
            <Button variant="outlined" color="secondary" onClick={handleBack}>
              Back to Orders
            </Button>
          </Box>
        </Box>

        <Divider sx={{ mb: 4 }} />

        {/* Order Tabs Component */}
        <TabContext value={activeTab}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={(event: React.SyntheticEvent, newValue: string) => setActiveTab(newValue)}
              aria-label="order tabs"
              variant="scrollable"
              scrollButtons="auto"
              allowScrollButtonsMobile
            >
              <Tab label=" Main Order Details" value="0" />
              {/* <Tab label="Order History" value="1" /> */}
            </Tabs>
          </Box>
          <TabPanel value="0">
            <OrderDetailsTab
              orderData={orderData}
              fetchOrderData={fetchOrderData}
              lineItemParam={lineItemParam}
            />
          </TabPanel>
          {/* <TabPanel value="1">
            <OrderHistoryTab orderData={orderData} />
          </TabPanel> */}
        </TabContext>

        {/* Order Details Dialog */}
        <OrderDetailsDialog
          open={detailsOpen}
          onClose={() => setDetailsOpen(false)}
          orderData={orderData}
          itemNotes={itemNotes}
          setItemNotes={setItemNotes}
        />
      </CardContent>
    </Card>
  );
};

export default OrderView;
