import {
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';

interface OrderHistoryTabProps {
  orderData: any;
}

const OrderHistoryTab = ({ orderData }: OrderHistoryTabProps) => {
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" component="div" gutterBottom>
        Order History
      </Typography>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Date</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Updated By</TableCell>
              <TableCell>Notes</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <TableCell>{orderData.orderDate}</TableCell>
              <TableCell>Order Created</TableCell>
              <TableCell>System</TableCell>
              <TableCell>Order placed by customer</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>
                {
                  new Date(new Date(orderData.orderDate).getTime() + 86400000)
                    .toISOString()
                    .split('T')[0]
                }
              </TableCell>
              <TableCell>Processing</TableCell>
              <TableCell>John <PERSON></TableCell>
              <TableCell>Order verified and processing started</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default OrderHistoryTab;
