import { useState, useEffect } from 'react';
import { Typography, Divider } from '@mui/material';

import { OrderDetailsTabProps } from '@/types/orderDetailsTab.types';
import MainOrderDetails from '../components/MainOrderDetails';
import MainOrderLineItem from '../components/MainOrderLineItem';

const OrderDetailsTab = ({ orderData, fetchOrderData, lineItemParam }: OrderDetailsTabProps) => {
  const [updatedOrderData, setUpdatedOrderData] = useState(orderData || {});

  useEffect(() => {
    setUpdatedOrderData(orderData);
  }, [orderData]);

  const sortLineItemsByNumber = (items: any[]) => {
    if (!items || !Array.isArray(items)) return [];
    return [...items].sort((a, b) => {
      const aNum = a.itemNumber ? parseInt(a.itemNumber.toString().replace(/\D/g, ''), 10) : 0;
      const bNum = b.itemNumber ? parseInt(b.itemNumber.toString().replace(/\D/g, ''), 10) : 0;
      return (isNaN(aNum) ? 0 : aNum) - (isNaN(bNum) ? 0 : bNum);
    });
  };

  return (
    <>
      <MainOrderDetails order={updatedOrderData} />
      <Divider sx={{ my: 4 }} />

      <Typography variant="h6" component="div" sx={{ mb: 2 }}>
        Line Items
      </Typography>

      {updatedOrderData?.lineItems &&
        sortLineItemsByNumber(updatedOrderData.lineItems).map((item: any, index: number) => (
          <MainOrderLineItem
            key={item.id || item.itemNumber || `line-item-${index}`}
            item={item}
            index={index}
            orderId={updatedOrderData?.id}
            refreshOrderData={fetchOrderData}
            initiallyExpanded={lineItemParam === item.itemNumber}
          />
        ))}

      {/* <CustomerContactDialog
        open={contactDialogOpen}
        onClose={() => setContactDialogOpen(false)}
        onSend={handleSendCustomerContact}
        customerImages={currentContactItem?.customerImages || []}
        artFiles={currentContactItem?.artFiles || []}
        itemId={currentContactItem?.id || ''}
      /> */}
    </>
  );
};

export default OrderDetailsTab;
