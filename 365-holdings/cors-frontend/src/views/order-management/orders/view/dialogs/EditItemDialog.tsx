import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  TextField,
  Typography,
} from '@mui/material';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import React, { useEffect, useState } from 'react';
import SkuSelect from '@/components/common/SkuSelect';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import Grid from '@mui/material/Grid2';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import { EditItemDialogProps } from '@/types/edit-item.types';
import { LineItemAttachmentProps } from '@/types/orderDetailsTab.types';

const EditItemDialog = ({ open, onClose, currentItem, onSave, updating }: EditItemDialogProps) => {
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const validationSchema = yup.object().shape({
    sku: yup.object().nullable().required('SKU is required'),
    quantity: yup.number().min(1, 'Quantity must be at least 1').required('Quantity is required'),

    imagesdata: yup.array().of(
      yup.object().shape({
        old_url: yup.string().required('Image URL is required'),
        isSelected: yup.boolean().required('Is Selected is required'),
        new_url: yup
          .array()
          .of(yup.string())
          .when('isSelected', {
            is: true,
            then: schema =>
              schema
                .required('New uploaded URL is required when selected')
                .length(1, 'Image is required'),
            otherwise: schema => schema.default([]),
          }),
        attachment_id: yup.string().required('Attachment ID is required'),
      }),
    ),
  });
  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    setValue,
    reset,
  } = useForm({
    defaultValues: {
      sku: currentItem?.productSku || undefined,
      quantity: currentItem?.quantity || 1,
      imagesdata: [
        ...(currentItem?.attachments?.map((attachment: LineItemAttachmentProps) => ({
          old_url: attachment.url,
          isSelected: false,
          new_url: [],
          attachment_id: attachment.id,
        })) || []),
      ],
    },
    mode: 'onChange',
    resolver: yupResolver(validationSchema),
  });
  const { fields: imagesDataFields, update: updateImagesData } = useFieldArray({
    control,
    name: 'imagesdata',
  });

  const onSubmitForm = (data: any) => {
    const dataToSend = {
      productSkuId: data?.sku?.id,
      quantity: data?.quantity,
      newAttachments: data?.imagesdata
        ?.filter((image: { isSelected: boolean }) => image?.isSelected)
        ?.map((image: { old_url: string; new_url: string[]; attachment_id: string }) => ({
          old_url: image?.old_url,
          new_url: image?.new_url?.[0],
          attachment_id: image?.attachment_id,
        })),
    };

    onSave(dataToSend);
  };
  useEffect(() => {
    if (open && currentItem) {
      setValue('sku', currentItem?.productSku || undefined);
      setValue('quantity', currentItem?.quantity || 1);
    }
  }, [open, currentItem]);

  return (
    <>
      {' '}
      <Dialog
        open={open}
        onClose={(_, reason) => {
          if (reason === 'backdropClick') return;
          onClose();
        }}
        aria-labelledby="custom-dialog-title"
        maxWidth="lg"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            overflow: 'visible',
          },
        }}
      >
        <DialogTitle sx={{ m: 0, p: 5 }} id="customized-dialog-title">
          Edit Item
        </DialogTitle>
        <DialogContent sx={{ p: 5 }}>
          <form onSubmit={handleSubmit(onSubmitForm)} style={{ marginTop: '10px' }}>
            <Grid container spacing={3}>
              <Grid size={6}>
                <SkuSelect name="sku" control={control} error={errors} onChange={() => {}} />
              </Grid>
              <Grid size={6}>
                <Controller
                  name={`quantity`}
                  defaultValue={currentItem?.quantity}
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      type="number"
                      disabled={true}
                      label="Quantity"
                      error={!!errors.quantity}
                      helperText={errors.quantity?.message}
                      onChange={e => field.onChange(Number(e.target.value))}
                    />
                  )}
                />
              </Grid>
              <Grid size={12} sx={{ mt: 6, mb: 2 }}>
                <Typography variant="h5">Select Images to be replaced</Typography>
              </Grid>
              {imagesDataFields.map((parentField: any, index: number) => {
                return (
                  <Grid size={4} key={index}>
                    <Box sx={{ position: 'relative', display: 'inline-block' }}>
                      <img
                        src={parentField.old_url}
                        alt={`Attachment ${index + 1}`}
                        width={200}
                        style={{
                          objectFit: 'contain',
                          height: 'auto',
                          borderRadius: '8px',
                          opacity: !parentField?.isSelected ? 0.5 : 1,
                        }}
                      />
                      <FormControlLabel
                        control={
                          <Controller
                            name={`imagesdata.${index}.isSelected`}
                            control={control}
                            render={({ field: { onChange, value } }) => (
                              <Checkbox
                                checked={value}
                                onChange={e => {
                                  onChange(e.target.checked);
                                  updateImagesData(index, {
                                    ...parentField,
                                    isSelected: e.target.checked,
                                    new_url: [],
                                  });
                                  setValue(`imagesdata.${index}.new_url`, []);
                                }}
                                disabled={updating}
                                sx={{ position: 'absolute', top: 0, left: 0 }}
                              />
                            )}
                          />
                        }
                        label=""
                        sx={{ m: 0 }}
                      />
                    </Box>
                    <Controller
                      name={`imagesdata.${index}.new_url`}
                      control={control}
                      render={({ field }) => (
                        <ImageUploadField
                          key={index}
                          control={control}
                          name={`imagesdata.${index}.new_url`}
                          setValue={setValue}
                          errors={errors}
                          buttonText="Select Image"
                          title="Upload Image"
                          errorField={errors?.imagesdata?.[index]?.new_url?.message}
                          minImages={1}
                          maxImages={1}
                          disabled={updating || !parentField?.isSelected}
                          formValue={field.value as string[]}
                        />
                      )}
                    />
                  </Grid>
                );
              })}

              <Grid size={12} sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                  <Button
                    type="button"
                    variant="outlined"
                    onClick={() => {
                      if (isDirty) {
                        setConfirmDialogOpen(true);
                      } else {
                        reset();
                        onClose();
                      }
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={updating || !isDirty} variant="contained">
                    {updating ? 'Submitting...' : 'Submit'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </DialogContent>
      </Dialog>
      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Discard Changes"
        message="You have entered edit information. Are you sure you want to close without submitting?"
        confirmLabel="Discard"
        cancelLabel="Continue Editing"
        confirmColor="error"
        onConfirm={() => {
          setConfirmDialogOpen(false);
          reset();
          onClose();
        }}
        onCancel={() => setConfirmDialogOpen(false)}
      />
    </>
  );
};

export default EditItemDialog;
