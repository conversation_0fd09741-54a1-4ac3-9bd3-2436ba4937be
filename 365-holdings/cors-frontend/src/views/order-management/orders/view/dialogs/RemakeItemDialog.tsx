import { RemakeItemDialogProps } from '@/types/remake-item.types';
import {
  Autocomplete,
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  TextField,
} from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import React, { useEffect, useState } from 'react';
import SkuSelect from '@/components/common/SkuSelect';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux-store';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import Grid from '@mui/material/Grid2';
import ConfirmationDialog from '@/components/ConfirmationDialog';

const RemakeItemDialog = ({
  open,
  onClose,
  currentItem,
  onSubmit,
  updating,
}: RemakeItemDialogProps) => {
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const commonState = useSelector((state: RootState) => state.common);
  const remakeReasonField = commonState.simpleFields?.allFields?.find(
    field => field?.key === 'remakeReason',
  );
  const validationSchema = yup.object().shape({
    sku: yup.object().nullable().required('SKU is required'),
    remakeReasons: yup
      .array()
      .of(yup.string())
      .min(1, 'At least one remake reason is required')
      .required('Remake reason is required'),
    detailedReasons: yup
      .array()
      .of(yup.string())
      .min(1, 'At least one detailed reason is required')
      .required('Detailed reason is required'),
    images: yup
      .array()
      .of(yup.string())
      .min(1, 'At least one image is required')
      .max(
        currentItem?.attachments?.length || 1,
        `Maximum images allowed is ${currentItem?.attachments?.length || 1}`,
      )
      .required('Images are required'),
  });
  const remakeReasons = remakeReasonField?.options?.map(option => option.value) || [];

  let detailedRemakeReasonField;
  if (commonState.simpleFields?.allFields) {
    detailedRemakeReasonField = commonState.simpleFields.allFields.find(
      field => field && field.key === 'detailedRemakeReason',
    );
  }

  const options = Object.entries(detailedRemakeReasonField?.remakeReason).flatMap(
    ([_, reasons]: any) => reasons.map((reason: string) => reason),
  );
  const capitalizeWords = (str: string) => {
    return str
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };
  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    setValue,
    reset,
  } = useForm({
    defaultValues: {
      remakeReasons: [],
      detailedReasons: [],
      sku: currentItem?.productSku || undefined,
      images: [],
    },
    mode: 'onChange',
    resolver: yupResolver(validationSchema),
  });

  const onSubmitForm = (data: any) => {
    const dataToSubmit = {
      remakeReasons: data?.remakeReasons,
      detailedReasons: data?.detailedReasons,
      sku: data?.sku?.id,
      images: data?.images,
      imageUrls: data?.images,
    };
    onSubmit(dataToSubmit);
  };
  useEffect(() => {
    if (open && currentItem) {
      setValue('sku', currentItem?.productSku || undefined);
    }
  }, [open, currentItem]);
  return (
    <>
      {' '}
      <Dialog
        open={open}
        onClose={(_, reason) => {
          if (reason === 'backdropClick') return;
          onClose();
        }}
        aria-labelledby="custom-dialog-title"
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            overflow: 'visible',
          },
        }}
      >
        <DialogTitle sx={{ m: 0, p: 5 }} id="customized-dialog-title">
          Remake Item
        </DialogTitle>
        <DialogContent sx={{ p: 5 }}>
          <form onSubmit={handleSubmit(onSubmitForm)} style={{ marginTop: '10px' }}>
            <Grid container spacing={3}>
              <Grid size={12}>
                <SkuSelect
                  name="sku"
                  control={control}
                  error={errors}
                  onChange={() => {}}
                  disabled={updating}
                />
              </Grid>

              <Grid size={12}>
                <Controller
                  control={control}
                  name="remakeReasons"
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      multiple
                      disableCloseOnSelect
                      options={remakeReasons}
                      getOptionLabel={option => capitalizeWords(option?.toString() || '')}
                      value={field.value || []}
                      onChange={(_, value) => field.onChange(value)}
                      disabled={updating}
                      renderInput={params => (
                        <TextField
                          {...params}
                          label="Remake Reason"
                          error={!!errors.remakeReasons}
                          helperText={errors.remakeReasons?.message}
                        />
                      )}
                    />
                  )}
                />
              </Grid>

              <Grid size={12}>
                <Controller
                  control={control}
                  name="detailedReasons"
                  render={({ field }) => (
                    <Autocomplete
                      multiple
                      disableCloseOnSelect
                      id="issues-autocomplete"
                      disabled={updating}
                      options={options}
                      getOptionLabel={option => capitalizeWords(option?.toString() || '')}
                      value={field.value || []}
                      onChange={(_, value) => field.onChange(value)}
                      renderInput={params => (
                        <TextField
                          {...params}
                          label="Select Issues"
                          variant="outlined"
                          error={!!errors.detailedReasons}
                          helperText={errors.detailedReasons?.message}
                        />
                      )}
                      sx={{ width: '100%' }}
                    />
                  )}
                />
              </Grid>

              <Grid size={12}>
                <Controller
                  name={`images`}
                  control={control}
                  rules={{ required: 'Image is required' }}
                  render={({ field: { onChange, value }, fieldState: { error } }) => (
                    <ImageUploadField
                      control={control}
                      name="images"
                      setValue={setValue}
                      errors={errors}
                      buttonText="Select Images"
                      title="Upload Images"
                      errorField={errors?.images?.message}
                      minImages={1}
                      disabled={updating}
                      // maxImages={getPetsCountFromSKU(currentItem?.productSku?.sku || '')}
                      maxImages={currentItem?.attachments?.length || 1}
                      formValue={value as string[]}
                    />
                  )}
                />
              </Grid>

              <Grid size={12} sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                  <Button
                    type="button"
                    variant="outlined"
                    disabled={updating}
                    onClick={() => {
                      if (isDirty) {
                        setConfirmDialogOpen(true);
                      } else {
                        reset();
                        onClose();
                      }
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={updating || !isDirty} variant="contained">
                    {updating ? 'Submitting...' : 'Submit'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </DialogContent>
      </Dialog>
      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Discard Changes"
        message="You have entered remake information. Are you sure you want to close without submitting?"
        confirmLabel="Discard"
        cancelLabel="Continue Editing"
        confirmColor="error"
        onConfirm={() => {
          setConfirmDialogOpen(false);
          reset();
          onClose();
        }}
        onCancel={() => setConfirmDialogOpen(false)}
      />
    </>
  );
};

export default RemakeItemDialog;
