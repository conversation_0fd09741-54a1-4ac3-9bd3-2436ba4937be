import { SingleProduct } from '@/types/manual-order.type';
import React, { useEffect } from 'react';
import { OrderFormSchemaType } from '../validations/manual-order.validation';
import {
  Control,
  Controller,
  FieldErrors,
  useFieldArray,
  UseFormSetValue,
  UseFormTrigger,
} from 'react-hook-form';
import Grid from '@mui/material/Grid2';

import {
  Box,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  Typography,
  TextField,
  Select,
  MenuItem,
  InputLabel,
  Card,
  Checkbox,
} from '@mui/material';
import ImageUploadField from '@/@core/components/mui/ImageUpload';

interface CustomizerProductProps {
  selectedProduct: SingleProduct;
  index: number;
  control: Control<OrderFormSchemaType>;
  errors: FieldErrors<OrderFormSchemaType>;
  setValue: UseFormSetValue<OrderFormSchemaType>;
  trigger: UseFormTrigger<OrderFormSchemaType>;
}

interface PetImageData {
  pet_name: string;
  pet_images: string[];
  front_engraving?: string;
  back_engraving?: string;
  back_engraving_sku?: string;
}

interface FrameOption {
  label: string;
  extra_charges: number;
  sub_options?: {
    label: string;
    extra_charges: number;
    imageUrl?: string;
  }[];
}

const PdpCustomizerProducts = ({
  selectedProduct,
  index,
  control,
  errors,
  setValue,
}: CustomizerProductProps) => {
  const customizer = JSON.parse(selectedProduct?.metadata?.pdp_customizer)?.steps || [];
  const {
    fields: petImagesFields,
    append: appendPetImage,
    update: updatePetImage,
    replace: replacePetImage,
  } = useFieldArray<OrderFormSchemaType>({
    control,
    name: `orderItems.${index}.pdp_pet_images_data`,
  });

  const {
    fields: frameFields,
    append: appendFrame,
    replace: replaceFrame,
  } = useFieldArray<OrderFormSchemaType>({
    control,
    name: `orderItems.${index}.pdp_frame_options`,
  });

  const {
    append: appendExtraOption,
    remove: removeExtraOption,
    replace: replaceExtraOption,
  } = useFieldArray<OrderFormSchemaType>({
    control,
    name: `orderItems.${index}.pdp_extras`,
  });

  const {
    fields: swatchFields,
    append: appendSwatch,
    replace: replaceSwatch,
    update: updateSwatch,
  } = useFieldArray<OrderFormSchemaType>({
    control,
    name: `orderItems.${index}.pdp_swatches`,
  });
  useEffect(() => {
    replaceExtraOption([]);

    const frameSection = customizer.find((section: any) => section.step_name === 'FRAME');
    if (frameSection && frameSection?.toggle_options) {
      replaceFrame([]);
      frameSection.toggle_options.forEach((toggleOption: any) => {
        appendFrame({
          label: toggleOption.label,
          options: toggleOption.options,
          selectedOption: toggleOption.options[0]?.label || '',
          selectedSubOption: toggleOption.options[0]?.sub_options?.[0]?.label || '',
        });
      });
    }
    const swatchSection = customizer.filter((section: any) => section.identifier === 'swatches');
    replaceSwatch([]);
    if (swatchSection && swatchSection.length > 0) {
      swatchSection.forEach((swatch: any) => {
        swatch.swatches.forEach((swatchSingle: any) => {
          appendSwatch({
            label: swatchSingle?.name?.toLowerCase() || swatchSingle.label?.toLowerCase(),
            value: '',
          });
        });
      });
    }
  }, [selectedProduct]);

  useEffect(() => {
    const imageUploaderSection = customizer.find(
      (section: { identifier: string; fixed_number_of_uploaders: number }) =>
        section.identifier === 'image_uploader',
    );
    const swatchFieldsPendent = swatchFields.find((section: { label: string }) =>
      section?.label?.toLowerCase()?.includes('pendents'),
    );

    if (
      imageUploaderSection &&
      selectedProduct &&
      !imageUploaderSection.dynamic_number_of_uploaders
    ) {
      if (petImagesFields.length !== imageUploaderSection.fixed_number_of_uploaders) {
        replacePetImage([]);

        for (let i = 0; i < imageUploaderSection.fixed_number_of_uploaders; i++) {
          appendPetImage({
            pet_images: [],
            pet_name: '',
            front_engraving: '',
            back_engraving: '',
          } as PetImageData);
        }
      }
    } else if (
      imageUploaderSection &&
      imageUploaderSection.dynamic_number_of_uploaders &&
      swatchFieldsPendent
    ) {
      replacePetImage([]);
      const pendentCount = parseInt(swatchFieldsPendent.value.match(/\d+/)?.[0] || '1');
      for (let i = 0; i < pendentCount; i++) {
        appendPetImage({
          pet_images: [],
          pet_name: '',
          front_engraving: '',
          back_engraving: '',
        } as PetImageData);
      }
    } else {
      replacePetImage([]);
    }
  }, [selectedProduct, swatchFields]);

  const errorsMap = (errors?.orderItems?.[index] as any) || {};
  return (
    <Grid container spacing={4}>
      {customizer.map((section: any, sectionIndex: number) => (
        <React.Fragment key={sectionIndex}>
          {section.identifier === 'swatches' &&
            section.swatches?.map(
              (
                swatch: {
                  label: string;
                  name: string;
                  options: { label: string; extra_charges: number }[];
                },
                swatchIndex: number,
              ) => {
                const swatchField = swatchFields.find(
                  field =>
                    field.label === swatch?.name?.toLowerCase() ||
                    field.label === swatch?.label?.toLowerCase(),
                );
                const swatchFieldIndex = swatchFields.findIndex(
                  field =>
                    field.label === swatch?.name?.toLowerCase() ||
                    field.label === swatch?.label?.toLowerCase(),
                );

                return (
                  <Grid size={6} key={swatchIndex}>
                    <FormControl fullWidth error={!!errorsMap[`pdp_swatches`]}>
                      <InputLabel>{swatch.label}</InputLabel>
                      <Controller
                        name={`orderItems.${index}.pdp_swatches.${swatchFieldIndex}.value`}
                        control={control}
                        render={({ field }) => (
                          <Select
                            {...field}
                            value={swatchField?.value || ''}
                            fullWidth
                            label={swatch.label}
                            onChange={e => {
                              field.onChange(e);
                              updateSwatch(swatchFieldIndex, {
                                label: swatchField?.label || '',
                                value: e.target.value,
                              });
                            }}
                          >
                            {swatch.options.map(
                              (
                                option: { label: string; extra_charges: number },
                                optionIndex: number,
                              ) => (
                                <MenuItem key={optionIndex} value={option.label}>
                                  <Typography variant="body2">
                                    {option.label}
                                    {option.extra_charges > 0 && ` (+$${option.extra_charges})`}
                                  </Typography>
                                </MenuItem>
                              ),
                            )}
                          </Select>
                        )}
                      />
                      <FormHelperText>
                        {errorsMap[`pdp_swatches`]?.[swatchFieldIndex]?.value?.message}
                      </FormHelperText>
                    </FormControl>
                  </Grid>
                );
              },
            )}

          {section.step_name === 'FRAME' &&
            frameFields.map((frameField, frameIndex) => (
              <Grid size={12} key={frameIndex}>
                <FormControl fullWidth error={!!errorsMap[`pdp_${frameField.label.toLowerCase()}`]}>
                  <Typography variant="h6" gutterBottom>
                    {frameField.label}
                  </Typography>
                  <Controller
                    name={`orderItems.${index}.pdp_frame_options.${frameIndex}.selectedOption`}
                    control={control}
                    defaultValue={frameField.options[0]?.label || ''}
                    render={({ field }) => {
                      const selectedOption =
                        frameField.options.find((opt: FrameOption) => opt.label === field.value) ||
                        frameField.options[0];

                      const subOptions = selectedOption?.sub_options || [];

                      return (
                        <Box>
                          <Box sx={{ mb: 3 }}>
                            <RadioGroup
                              {...field}
                              row
                              value={field.value || frameField.options[0]?.label || ''}
                              onChange={e => {
                                field.onChange(e);
                                setValue(
                                  `orderItems.${index}.pdp_frame_options.${frameIndex}.selectedSubOption`,
                                  frameField.options.find(
                                    (opt: FrameOption) => opt.label === e.target.value,
                                  )?.sub_options?.[0]?.label || '',
                                );
                              }}
                            >
                              {frameField.options.map(
                                (option: FrameOption, optionIndex: number) => (
                                  <FormControlLabel
                                    key={optionIndex}
                                    value={option.label}
                                    control={<Radio color="primary" />}
                                    label={
                                      <Typography variant="body1">
                                        {option.label}
                                        {option.extra_charges > 0 && (
                                          <Typography component="span" color="text.secondary">
                                            {` (+$${option.extra_charges})`}
                                          </Typography>
                                        )}
                                      </Typography>
                                    }
                                  />
                                ),
                              )}
                            </RadioGroup>
                          </Box>

                          {subOptions.length > 0 && (
                            <Box sx={{ mt: 2, pl: 4, borderLeft: '2px solid #e0e0e0' }}>
                              <FormControl fullWidth>
                                <Controller
                                  name={`orderItems.${index}.pdp_frame_options.${frameIndex}.selectedSubOption`}
                                  control={control}
                                  defaultValue={subOptions[0]?.label || ''}
                                  render={({ field: subField }) => (
                                    <RadioGroup
                                      {...subField}
                                      row
                                      value={subField.value || subOptions[0]?.label || ''}
                                    >
                                      {subOptions.map((subOption: any, subIndex: number) => (
                                        <FormControlLabel
                                          key={subIndex}
                                          value={subOption.label}
                                          control={<Radio color="secondary" />}
                                          label={
                                            <Box>
                                              <Typography variant="body2">
                                                {subOption.label}
                                                {subOption.extra_charges > 0 && (
                                                  <Typography
                                                    component="span"
                                                    color="text.secondary"
                                                  >
                                                    {` (+$${subOption.extra_charges})`}
                                                  </Typography>
                                                )}
                                              </Typography>
                                              {subOption.imageUrl && (
                                                <Box sx={{ mt: 1 }}>
                                                  <img
                                                    src={subOption.imageUrl}
                                                    alt={subOption.label}
                                                    style={{
                                                      maxWidth: '100px',
                                                      height: 'auto',
                                                      borderRadius: '4px',
                                                    }}
                                                  />
                                                </Box>
                                              )}
                                            </Box>
                                          }
                                        />
                                      ))}
                                    </RadioGroup>
                                  )}
                                />
                              </FormControl>
                            </Box>
                          )}
                        </Box>
                      );
                    }}
                  />
                  <FormHelperText>
                    {errorsMap[`pdp_${frameField.label.toLowerCase()}`]?.message}
                  </FormHelperText>
                </FormControl>
              </Grid>
            ))}

          {section.identifier === 'image_uploader' && (
            <Grid container size={12} spacing={4}>
              {petImagesFields?.map((petImage: PetImageData, petImageIndex: number) => (
                <Grid
                  container
                  spacing={2}
                  size={petImagesFields.length >= 3 ? 4 : 6}
                  key={petImageIndex}
                >
                  <Grid size={12}>
                    <ImageUploadField
                      control={control}
                      errors={errors}
                      setValue={setValue}
                      errorField={
                        errors?.orderItems?.length
                          ? errorsMap?.pdp_pet_images_data?.[petImageIndex]?.pet_images?.message
                          : ''
                      }
                      name={`orderItems.${index}.pdp_pet_images_data.${petImageIndex}.pet_images`}
                      minImages={1}
                      maxImages={1}
                      preview={false}
                      onChange={images => {
                        if (images) {
                          updatePetImage(petImageIndex, {
                            back_engraving: (
                              petImagesFields[petImageIndex] as unknown as PetImageData
                            )?.back_engraving,
                            pet_name: (petImagesFields[petImageIndex] as unknown as PetImageData)
                              .pet_name,
                            pet_images: images,
                            front_engraving: (
                              petImagesFields[petImageIndex] as unknown as PetImageData
                            )?.front_engraving,
                            back_engraving_sku: (
                              petImagesFields[petImageIndex] as unknown as PetImageData
                            )?.back_engraving_sku,
                          } as PetImageData);
                        }
                      }}
                    />
                    {petImagesFields[petImageIndex]?.pet_images?.length > 0 && (
                      <img
                        src={petImagesFields[petImageIndex]?.pet_images[0]}
                        alt="pet_image"
                        height={100}
                        width={100}
                        style={{
                          borderRadius: '4px',
                          objectFit: 'cover',
                        }}
                      />
                    )}
                  </Grid>{' '}
                  {section?.optional_pet_name_field && (
                    <Grid size={12}>
                      <Controller
                        name={`orderItems.${index}.pdp_pet_images_data.${petImageIndex}.pet_name`}
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            fullWidth
                            label="Pet Name (Optional)"
                            error={!!errorsMap?.pdp_pet_images_data?.[petImageIndex]?.pet_name}
                            helperText={
                              errorsMap?.pdp_pet_images_data?.[petImageIndex]?.pet_name?.message
                            }
                            onChange={e => {
                              field.onChange(e);
                              updatePetImage(petImageIndex, {
                                back_engraving: (
                                  petImagesFields[petImageIndex] as unknown as PetImageData
                                )?.back_engraving,
                                back_engraving_sku: (
                                  petImagesFields[petImageIndex] as unknown as PetImageData
                                )?.back_engraving_sku,
                                pet_name: e.target.value,
                                pet_images: (
                                  petImagesFields[petImageIndex] as unknown as PetImageData
                                )?.pet_images,
                                front_engraving: (
                                  petImagesFields[petImageIndex] as unknown as PetImageData
                                )?.front_engraving,
                              } as PetImageData);
                            }}
                          />
                        )}
                      />
                    </Grid>
                  )}
                  {section?.front_engraving?.display && (
                    <Grid size={12}>
                      <Controller
                        name={`orderItems.${index}.pdp_pet_images_data.${petImageIndex}.front_engraving`}
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            fullWidth
                            value={
                              (petImagesFields[petImageIndex] as unknown as PetImageData)
                                ?.front_engraving || ''
                            }
                            label={section?.front_engraving?.label + ' (Optional)'}
                            error={
                              !!errorsMap?.pdp_pet_images_data?.[petImageIndex]?.front_engraving
                            }
                            helperText={
                              errorsMap?.pdp_pet_images_data?.[petImageIndex]?.front_engraving
                                ?.message
                            }
                            onChange={e => {
                              field.onChange(e);
                              updatePetImage(petImageIndex, {
                                back_engraving: (
                                  petImagesFields[petImageIndex] as unknown as PetImageData
                                )?.back_engraving,
                                pet_name: (
                                  petImagesFields[petImageIndex] as unknown as PetImageData
                                )?.pet_name,
                                pet_images: (
                                  petImagesFields[petImageIndex] as unknown as PetImageData
                                )?.pet_images,
                                front_engraving: e.target.value,
                                back_engraving_sku: (
                                  petImagesFields[petImageIndex] as unknown as PetImageData
                                )?.back_engraving_sku,
                              } as PetImageData);
                            }}
                          />
                        )}
                      />
                    </Grid>
                  )}
                  {section?.back_engraving?.display && (
                    <Grid size={12} className="mt-4">
                      <Card
                        sx={{ p: 2, mb: 2 }}
                        className="border-2 border-gray-300 min-h-[100px] h-full"
                      >
                        <Controller
                          name={`orderItems.${index}.pdp_pet_images_data.${petImageIndex}.enable_back_engraving`}
                          control={control}
                          defaultValue={false}
                          render={({ field: enableField }) => (
                            <Box sx={{ mb: 2 }}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={enableField.value}
                                    onChange={enableField.onChange}
                                  />
                                }
                                label={section?.back_engraving?.label || 'Back Engraving'}
                                className="mb-4"
                              />
                              <Controller
                                name={`orderItems.${index}.pdp_pet_images_data.${petImageIndex}.back_engraving`}
                                control={control}
                                render={({ field }) => (
                                  <TextField
                                    {...field}
                                    fullWidth
                                    required={enableField.value}
                                    disabled={!enableField.value}
                                    value={
                                      (petImagesFields[petImageIndex] as unknown as PetImageData)
                                        ?.back_engraving || ''
                                    }
                                    label={section?.back_engraving?.label}
                                    error={
                                      !!errorsMap?.pdp_pet_images_data?.[petImageIndex]
                                        ?.back_engraving
                                    }
                                    helperText={
                                      errorsMap?.pdp_pet_images_data?.[petImageIndex]
                                        ?.back_engraving?.message
                                    }
                                    onChange={e => {
                                      field.onChange(e);
                                      updatePetImage(petImageIndex, {
                                        back_engraving: e.target.value,
                                        pet_name: (
                                          petImagesFields[petImageIndex] as unknown as PetImageData
                                        )?.pet_name,
                                        pet_images: (
                                          petImagesFields[petImageIndex] as unknown as PetImageData
                                        )?.pet_images,
                                        front_engraving: (
                                          petImagesFields[petImageIndex] as unknown as PetImageData
                                        )?.front_engraving,
                                        back_engraving_sku: section?.back_engraving?.sku || '',
                                      } as PetImageData);
                                    }}
                                  />
                                )}
                              />
                            </Box>
                          )}
                        />
                      </Card>
                    </Grid>
                  )}
                </Grid>
              ))}{' '}
            </Grid>
          )}

          {section.step_name === 'EXTRAS' &&
            section?.extra_options?.map(
              (
                extraOption: { label: string; id: string; descritpion: string; sku?: string },
                extraOptionIndex: number,
              ) => (
                <Grid
                  size={{ xs: 12, lg: section?.extra_options?.length > 1 ? 6 : 12 }}
                  key={extraOptionIndex}
                >
                  <Card
                    sx={{ p: 2, mb: 2 }}
                    className="border-2 border-gray-300 min-h-[150px] h-full"
                  >
                    <Box>
                      <Box sx={{ flexGrow: 1 }}></Box>
                      <Controller
                        name={`orderItems.${index}.pdp_extras`}
                        control={control}
                        render={({ field: { value = [] } }) => {
                          const isChecked = value.some(
                            (item: { name: string; value: number; display: boolean }) =>
                              item?.name === extraOption.label,
                          );

                          return (
                            <React.Fragment>
                              <Box
                                sx={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  alignItems: 'start',
                                }}
                              >
                                <Typography variant="h6" gutterBottom>
                                  {extraOption?.label}
                                </Typography>
                                <Checkbox
                                  checked={isChecked}
                                  onChange={e => {
                                    const existingIndex = value.findIndex(
                                      (item: { name: string; value: number; display: boolean }) =>
                                        item?.name === extraOption.label,
                                    );
                                    if (e.target.checked) {
                                      if (existingIndex === -1) {
                                        appendExtraOption({
                                          name: extraOption.label,
                                          id: extraOption.id || '',
                                          _unique_key: selectedProduct?.timestampkey || '',
                                          sku: extraOption?.sku || '',
                                        });
                                      }
                                    } else {
                                      if (existingIndex >= 0) {
                                        removeExtraOption(existingIndex);
                                      }
                                    }
                                  }}
                                  inputProps={{ 'aria-label': extraOption.label }}
                                />
                              </Box>

                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                {extraOption?.descritpion || 'No description available'}
                              </Typography>
                            </React.Fragment>
                          );
                        }}
                      />
                    </Box>
                  </Card>
                </Grid>
              ),
            )}
        </React.Fragment>
      ))}
    </Grid>
  );
};

export default PdpCustomizerProducts;
