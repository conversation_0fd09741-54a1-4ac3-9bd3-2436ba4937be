'use client';
import React from 'react';
import Grid from '@mui/material/Grid2';
import {
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Checkbox,
  Card,
  TextField,
} from '@mui/material';
import { Control, Controller, useFieldArray } from 'react-hook-form';
import { OrderFormSchemaType } from '../../validations/manual-order.validation';
import {
  CustomizerFinalOptionVarient,
  FinalOption,
  SingleProduct,
} from '@/types/manual-order.type';

interface FinalOptionsSectionProps {
  section: any;
  index: number;
  control: Control<OrderFormSchemaType>;
  errorsMap: any;
  selectedProduct: SingleProduct;
}

const FinalOptionsSection = ({
  section,
  index,
  control,
  errorsMap,
  selectedProduct,
}: FinalOptionsSectionProps) => {
  const { append: appendFinalOption, remove: removeFinalOption } = useFieldArray({
    control,
    name: `orderItems.${index}.finalOptions`,
  });
  // Rush Creation Section
  if (section.display && section.label_for === 'Rush Creation') {
    return (
      <>
        <Grid size={12}>
          <Card sx={{ p: 2, mb: 2 }} className="border-2 border-gray-300 min-h-[150px] h-full">
            <Box>
              <Box sx={{ flexGrow: 1 }}></Box>
              <Controller
                name={`orderItems.${index}.finalOptions`}
                control={control}
                render={({ field: { value = [] } }) => {
                  const isChecked = value.some(
                    (item: { name: string; value: number; display: boolean }) =>
                      item?.name === 'rush_creation',
                  );
                  const selectedFeatureProduct = value.find(
                    (item: { name: string; value: number; display: boolean }) =>
                      item?.name === 'rush_creation',
                  );
                  const selectedVariantId = selectedFeatureProduct?.variant || '';

                  return (
                    <React.Fragment>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'start',
                        }}
                      >
                        <Typography variant="h6" gutterBottom>
                          {section.rush_heading}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {section?.rush_subheading}
                      </Typography>

                      {section?.options?.length > 0 && (
                        <FormControl fullWidth>
                          <InputLabel>Variant</InputLabel>
                          <Select
                            value={selectedVariantId}
                            fullWidth
                            onChange={e => {
                              const selectedVariant = section?.options?.find(
                                (v: CustomizerFinalOptionVarient) => v.value === e.target.value,
                              );
                              const existingIndex = value.findIndex(
                                (item: FinalOption) => item?.name === 'rush_creation',
                              );
                              if (existingIndex >= 0) {
                                removeFinalOption(existingIndex);
                                appendFinalOption({
                                  name: 'rush_creation',
                                  variant: selectedVariant?.value || '',
                                  _unique_key: selectedProduct?.timestampkey || '',
                                  sku: selectedVariant?.sku || '',
                                });
                              } else {
                                appendFinalOption({
                                  name: 'rush_creation',
                                  variant: selectedVariant?.value || '',
                                  _unique_key: selectedProduct?.timestampkey || '',
                                  sku: selectedVariant?.sku || '',
                                });
                              }
                            }}
                            displayEmpty
                            renderValue={selected => {
                              if (!selected) return '';
                              const variant = section?.options.find(
                                (v: CustomizerFinalOptionVarient) => v.value === selected,
                              );
                              return variant?.label;
                            }}
                          >
                            {section?.options.map(
                              (variant: CustomizerFinalOptionVarient, variantIndex: number) =>
                                variant.display && (
                                  <MenuItem key={variantIndex} value={variant.value}>
                                    {variant.label}
                                  </MenuItem>
                                ),
                            )}
                          </Select>
                        </FormControl>
                      )}
                    </React.Fragment>
                  );
                }}
              />
            </Box>
          </Card>
        </Grid>
        <Grid size={12}>
          <FormHelperText error>{errorsMap?.finalOptions?.message}</FormHelperText>
        </Grid>
      </>
    );
  }

  // Final Options Products Section
  if (section?.final_options?.products) {
    return (
      <>
        {section.final_options.products.map(
          (product: any, productIndex: number) =>
            product.display && (
              <Grid
                size={{ xs: 12, lg: section.final_options.products?.length > 1 ? 6 : 12 }}
                key={productIndex}
              >
                <Card
                  sx={{ p: 2, mb: 2 }}
                  className="border-2 border-gray-300 min-h-[150px] h-full"
                >
                  <Box>
                    <Box sx={{ flexGrow: 1 }}></Box>
                    <Controller
                      name={`orderItems.${index}.finalOptions`}
                      control={control}
                      render={({ field: { value = [] } }) => {
                        const isChecked = value.some(
                          (item: { name: string; value: number; display: boolean }) =>
                            item?.name === product.name,
                        );
                        const selectedFeatureProduct = value.find(
                          (item: { name: string; value: number; display: boolean }) =>
                            item?.name === product.name,
                        );
                        const selectedVariantId = selectedFeatureProduct?.variant || '';

                        return (
                          <React.Fragment>
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'start',
                              }}
                            >
                              <Typography variant="h6" gutterBottom>
                                {product?.detail?.heading}
                              </Typography>
                              <Checkbox
                                checked={isChecked}
                                onChange={e => {
                                  const existingIndex = value.findIndex(
                                    (item: { name: string; value: number; display: boolean }) =>
                                      item?.name === product.name,
                                  );
                                  if (e.target.checked) {
                                    if (existingIndex === -1) {
                                      appendFinalOption({
                                        name: product.name,
                                        variant: product.detail.default_variant_id || '',
                                        _unique_key: selectedProduct?.timestampkey || '',
                                        sku: product?.sku || '',
                                      });
                                    }
                                  } else {
                                    if (existingIndex >= 0) {
                                      removeFinalOption(existingIndex);
                                    }
                                  }
                                }}
                                inputProps={{ 'aria-label': product.detail.heading }}
                              />
                            </Box>

                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              {product?.detail?.description || 'No description available'}
                            </Typography>
                            {isChecked && product.detail.variant.length > 0 && (
                              <FormControl fullWidth>
                                <InputLabel>Variant</InputLabel>

                                <Select
                                  value={selectedVariantId}
                                  fullWidth
                                  onChange={e => {
                                    const selectedVariant = product.detail.variant.find(
                                      (v: CustomizerFinalOptionVarient) =>
                                        v.value === e.target.value,
                                    );
                                    const existingIndex = value.findIndex(
                                      (item: FinalOption) => item?.name === product.name,
                                    );
                                    if (existingIndex >= 0) {
                                      removeFinalOption(existingIndex);
                                      appendFinalOption({
                                        name: product.name,
                                        variant: selectedVariant?.value || '',
                                        _unique_key: selectedProduct?.timestampkey || '',
                                        sku: selectedVariant?.sku || '',
                                      });
                                    }
                                  }}
                                  required
                                  displayEmpty
                                  renderValue={selected => {
                                    if (!selected) return '';
                                    const variant = product.detail.variant.find(
                                      (v: CustomizerFinalOptionVarient) => v.value === selected,
                                    );
                                    return variant?.name || '';
                                  }}
                                >
                                  {product.detail.variant.map(
                                    (variant: CustomizerFinalOptionVarient, variantIndex: number) =>
                                      variant.display && (
                                        <MenuItem key={variantIndex} value={variant.value}>
                                          {variant.name}
                                        </MenuItem>
                                      ),
                                  )}
                                </Select>
                              </FormControl>
                            )}
                            {isChecked && product.name === 'custom_bandana' && (
                              <TextField
                                sx={{ mt: 2 }}
                                name={`orderItems.${index}.finalOptions.${productIndex}.pet_name`}
                                value={
                                  value.find(
                                    (item: { name: string; value: number; display: boolean }) =>
                                      item?.name === product.name,
                                  )?.pet_name || ''
                                }
                                fullWidth
                                required
                                label="Pet Name"
                                onChange={e => {
                                  const existingIndex = value.findIndex(
                                    (item: { name: string; value: number; display: boolean }) =>
                                      item?.name === product.name,
                                  );
                                  const existingItem = value[existingIndex];
                                  if (existingIndex >= 0) {
                                    removeFinalOption(existingIndex);
                                    appendFinalOption({
                                      name: product.name,
                                      variant: existingItem?.variant || '',
                                      _unique_key: selectedProduct?.timestampkey || '',
                                      pet_name: e.target.value,
                                      sku: existingItem?.sku || '',
                                    });
                                  }
                                }}
                              />
                            )}
                          </React.Fragment>
                        );
                      }}
                    />
                  </Box>
                </Card>
              </Grid>
            ),
        )}
      </>
    );
  }

  return null;
};

export default FinalOptionsSection;
