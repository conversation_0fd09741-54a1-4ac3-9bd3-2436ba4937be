'use client';
import React from 'react';
import Grid from '@mui/material/Grid2';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  TextField,
  Autocomplete,
} from '@mui/material';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { OrderFormSchemaType } from '../../validations/manual-order.validation';
import { apiCall } from '@/actions/common.actions';

interface PetTypeSelectionProps {
  petTypeSection: any;
  index: number;
  control: Control<OrderFormSchemaType>;
  errorsMap: any;
  breedSpecies: string[];
  setBreedSpecies: (data: string[]) => void;
  resetAllFields: () => void;
}

const PetTypeSelection = ({
  petTypeSection,
  index,
  control,
  errorsMap,
  breedSpecies,
  setBreedSpecies,
  resetAllFields,
}: PetTypeSelectionProps) => {
  return (
    <>
      {petTypeSection.specie && (
        <>
          <Grid size={{ xs: 12, lg: 6 }}>
            <FormControl fullWidth error={!!errorsMap?._pet_type}>
              <InputLabel>Pet Type</InputLabel>
              <Controller
                name={`orderItems.${index}._pet_type`}
                control={control}
                rules={{ required: 'Pet type is required' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    label="Pet Type"
                    value={field.value || ''}
                    onChange={async e => {
                      field.onChange(e);
                      const data = await apiCall<string[]>(
                        'get',
                        `/orders/breeds?specie=${e.target.value}`,
                      );
                      setBreedSpecies(data);
                      resetAllFields();
                    }}
                  >
                    {petTypeSection.specie.map(
                      (pet: Record<'name' | 'image', string>, petIndex: number) => (
                        <MenuItem key={petIndex} value={pet.name}>
                          <img
                            src={pet.image}
                            alt={pet.name}
                            style={{ height: '20px', width: '20px', borderRadius: '50%' }}
                          />{' '}
                          {pet.name.charAt(0).toUpperCase() + pet.name.slice(1)}
                        </MenuItem>
                      ),
                    )}
                  </Select>
                )}
              />
              <FormHelperText>{errorsMap?._pet_type?.message}</FormHelperText>
            </FormControl>
          </Grid>
          <Grid size={{ xs: 12, lg: 6 }}>
            <Controller
              name={`orderItems.${index}._pet_breed_specie`}
              control={control}
              render={({ field }) => (
                <Autocomplete
                  {...field}
                  fullWidth
                  value={field.value || ''}
                  options={breedSpecies}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label="Breed"
                      error={!!errorsMap?._pet_breed_specie}
                      helperText={errorsMap?._pet_breed_specie?.message}
                    />
                  )}
                  onChange={(_, value) => {
                    field.onChange(value);
                  }}
                />
              )}
            />
          </Grid>
        </>
      )}
      {petTypeSection.pet_ages && (
        <>
          <Grid size={{ xs: 12, lg: 6 }}>
            <Controller
              name={`orderItems.${index}._pet_name`}
              control={control}
              rules={{
                required: 'Pet name is required',
                maxLength: {
                  value: 18,
                  message: 'Pet name must be 18 characters or less',
                },
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  value={field.value || ''}
                  fullWidth
                  label="Pet Name"
                  error={!!errorsMap?._pet_name}
                  helperText={errorsMap?._pet_name?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, lg: 6 }}>
            <FormControl fullWidth error={!!errorsMap?._pet_age}>
              <InputLabel>Pet Age</InputLabel>
              <Controller
                name={`orderItems.${index}._pet_age`}
                control={control}
                rules={{ required: 'Pet age is required' }}
                render={({ field }) => (
                  <Select {...field} label="Pet Age" value={field.value || ''}>
                    {petTypeSection.pet_ages.map((age: { name: string }, ageIndex: number) => (
                      <MenuItem key={ageIndex} value={age.name}>
                        {age.name}
                      </MenuItem>
                    ))}
                  </Select>
                )}
              />
              <FormHelperText>
                {errorsMap?._pet_age?.message || petTypeSection.pet_age_subheading}
              </FormHelperText>
            </FormControl>
          </Grid>
        </>
      )}
    </>
  );
};

export default PetTypeSelection;
