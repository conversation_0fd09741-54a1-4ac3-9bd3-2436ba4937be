'use client';
import React, { useEffect, useState } from 'react';
import Grid from '@mui/material/Grid2';
import { Typography } from '@mui/material';
import {
  Control,
  FieldErrors,
  useFieldArray,
  UseFormSetValue,
  UseFormTrigger,
} from 'react-hook-form';
import { SingleProduct } from '@/types/manual-order.type';
import { OrderFormSchemaType } from '../validations/manual-order.validation';

// Import the component chunks
import PetTypeSelection from './sub-components/PetTypeSelection';
import PetDetailsSection from './sub-components/PetDetailsSection';
import PetInfoSection from './sub-components/PetInfoSection';
import ProductOptionsSection from './sub-components/ProductOptionsSection';
import FinalOptionsSection from './sub-components/FinalOptionsSection';
import FrameOptionsSection from './sub-components/FrameOptionsSection';

interface CustomizerProductProps {
  selectedProduct: SingleProduct;
  index: number;
  control: Control<OrderFormSchemaType>;
  errors: FieldErrors<OrderFormSchemaType>;
  setValue: UseFormSetValue<OrderFormSchemaType>;
  trigger: UseFormTrigger<OrderFormSchemaType>;
}

const CustomizerProduct = ({
  selectedProduct,
  index,
  control,
  errors,
  setValue,
  trigger,
}: CustomizerProductProps) => {
  const [breedSpecies, setBreedSpecies] = useState<string[]>([]);

  const customizer = JSON.parse(selectedProduct?.metadata?.product_customizer);
  const errorsMap = (errors?.orderItems?.[index] as any) || {};
  const resetAllFields = () => {
    setValue(`orderItems.${index}._pet_breed_specie`, '');
    setValue(`orderItems.${index}._pet_name`, '');
    setValue(`orderItems.${index}._pet_age`, '');
    setValue(`orderItems.${index}.petImagesCustomizer`, []);
    setValue(`orderItems.${index}.customizer_questions`, []);
    setValue(`orderItems.${index}.uniqueCharacteristics`, []);
    setValue(`orderItems.${index}.left_eye_color`, '');
    setValue(`orderItems.${index}.right_eye_color`, '');
    setValue(`orderItems.${index}.differentEyeColors`, false);
    setValue(`orderItems.${index}.left_ear_position`, '');
    setValue(`orderItems.${index}.right_ear_position`, '');
    setValue(`orderItems.${index}.productOptions`, []);
    setValue(`orderItems.${index}.finalOptions`, []);
    setValue(`orderItems.${index}.otherOptions`, []);
  };

  useEffect(() => {
    resetAllFields();
    setValue(`orderItems.${index}._pet_type`, '');
  }, []);

  return (
    <Grid container spacing={4}>
      {customizer.map((section: Record<string, any>, sectionIndex: number) => (
        <React.Fragment key={sectionIndex}>
          {section.customizer_main_heading && (
            <Grid size={12}>
              <Typography variant="h4" gutterBottom sx={{ textAlign: 'center', my: 5 }}>
                {section.customizer_main_heading}
              </Typography>
            </Grid>
          )}

          {/* Pet Type Selection */}
          {section.pet_type &&
            section.pet_type.map((petTypeSection: any, petTypeIndex: number) => (
              <PetTypeSelection
                key={petTypeIndex}
                petTypeSection={petTypeSection}
                index={index}
                control={control}
                errorsMap={errorsMap}
                breedSpecies={breedSpecies}
                setBreedSpecies={setBreedSpecies}
                resetAllFields={resetAllFields}
              />
            ))}

          {/* Pet Details Section */}
          {section.pet_detail &&
            section.pet_detail.map((detailSection: any, detailIndex: number) => (
              <PetDetailsSection
                key={detailIndex}
                detailSection={detailSection}
                index={index}
                control={control}
                errors={errors}
                setValue={setValue}
                errorsMap={errorsMap}
                trigger={trigger}
              />
            ))}

          {/* Pet Info Section */}
          {section.pet_info &&
            section.pet_info.map((infoSection: any, infoIndex: number) => (
              <PetInfoSection
                key={infoIndex}
                infoSection={infoSection}
                index={index}
                control={control}
                errorsMap={errorsMap}
              />
            ))}

          {/* Product Options Section */}
          {section.product_options &&
            section.product_options.map((optionSection: any, optionIndex: number) => (
              <ProductOptionsSection
                key={optionIndex}
                optionSection={optionSection}
                index={index}
                control={control}
                errorsMap={errorsMap}
              />
            ))}

          {/* Frame Options Section */}
          {section.display && section.label_for === 'Frame Options' && (
            <FrameOptionsSection
              section={section}
              index={index}
              control={control}
              errorsMap={errorsMap}
            />
          )}

          {/* Final Options Section */}
          {(section.display && section.label_for === 'Rush Creation') || section?.final_options ? (
            <FinalOptionsSection
              section={section}
              index={index}
              control={control}
              errorsMap={errorsMap}
              selectedProduct={selectedProduct}
            />
          ) : null}
        </React.Fragment>
      ))}
    </Grid>
  );
};

export default CustomizerProduct;
