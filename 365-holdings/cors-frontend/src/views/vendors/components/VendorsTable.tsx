'use client';

import CustomIconButton from '@/components/CustonIconButton';
import DataTable from '@/components/Datatable';
import { useTableColumns } from '@/hooks/useTableColumns';
import { TVendor } from '@/types/vendors.types';
import { Box, Tooltip, Typography } from '@mui/material';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'nextjs-toploader/app';

import React from 'react';

const VendorsTable = ({ allVendors }: { allVendors: TVendor[] }) => {
  const searchParams = useSearchParams();
  const page = Number(searchParams.get('page')) || 1;
  const limit = Number(searchParams.get('limit')) || 25;
  const router = useRouter();
  const handleRowClick = (row: any, e?: React.MouseEvent) => {
    const path = `/vendors/view/${row.id}`;
    if (e && (e.ctrl<PERSON><PERSON> || e.metaKey)) {
      window.open(path, '_blank', 'noopener,noreferrer');
    } else {
      router.push(path, { scroll: false });
    }
  };
  const columns = useTableColumns<TVendor>([
    {
      accessor: (row: TVendor) => row.id || '-',
      header: 'ID',
      type: 'custom' as const,
      customRender: (value: string, row: any) => {
        return (
          <Typography
            sx={{
              cursor: 'pointer',
              color: 'primary.main',
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
          >
            {value ?? '-'}
          </Typography>
        );
      },
    },
    {
      accessor: (row: TVendor) => row.name || '-',
      header: 'Name',
      type: 'text',
    },
    {
      accessor: (row: TVendor) => row.supportedSkus?.join(', ') || '-',
      header: 'Supported SKUs',
      type: 'custom' as const,
      customRender: (value: string, row: any) => {
        return (
          <Tooltip title={row.supportedSkus.join(', ') ?? '-'} arrow>
            <Typography
              sx={{
                maxWidth: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                cursor: 'default',
              }}
            >
              {row.supportedSkus.join(', ') ?? '-'}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      accessor: (row: TVendor) => row.costPerSku || '-',
      header: 'Cost per SKU',
      type: 'custom' as const,
      customRender: (value: string, row: any) => {
        return (
          <Tooltip
            title={Object.entries(row.costPerSku)
              .map(([sku, cost]) => `${sku}: $${cost}`)
              .join(', ')}
            arrow
          >
            <Typography
              sx={{
                maxWidth: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                cursor: 'default',
              }}
            >
              {Object.entries(row.costPerSku)
                .map(([sku, cost]) => `${sku}: $${cost}`)
                .join(', ')}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      accessor: (row: TVendor) => (row.averageCycleTime ? `${row.averageCycleTime} days` : '-'),
      header: 'Average Cycle Time',
      type: 'text',
    },
    {
      accessor: (row: TVendor) =>
        row.maximumDailyCapacity ? row.maximumDailyCapacity.toString() : '-',
      header: 'Max Daily Capacity',
      type: 'text',
    },
    {
      accessor: (row: TVendor) =>
        row.rushOrderSupport !== undefined ? (row.rushOrderSupport ? 'Yes' : 'No') : '-',
      header: 'Rush Order Support',
      type: 'chip',
      chipConfig: {
        valueMap: {
          Yes: { label: 'Yes', color: 'success' },
          No: { label: 'No', color: 'error' },
          '-': { label: '-', color: 'default' },
        },
      },
    },
    {
      accessor: (row: TVendor) => row.supportedManufacturingTypes?.join(', ') || '-',
      header: 'Manufacturing Types',
      type: 'custom' as const,
      customRender: (value: string, row: any) => {
        return (
          <Tooltip title={row.supportedManufacturingTypes?.join(', ') ?? '-'} arrow>
            <Typography
              sx={{
                maxWidth: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                cursor: 'default',
              }}
            >
              {Object.entries(row.costPerSku)
                .map(([sku, cost]) => `${sku}: $${cost}`)
                .join(', ')}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      accessor: (row: TVendor) => {
        if (!row.shippingPreferences) return '-';
        const prefs = [];
        if (row.shippingPreferences.usesShipStation) prefs.push('ShipStation');
        if (row.shippingPreferences.preferredCarrier)
          prefs.push(row.shippingPreferences.preferredCarrier);
        return prefs.length > 0 ? prefs.join(', ') : '-';
      },
      header: 'Shipping Preferences',
      type: 'text',
    },
    {
      accessor: (row: TVendor) => {
        if (!row.supportedArtworkTypes || row.supportedArtworkTypes.length === 0) return '-';
        return row.supportedArtworkTypes.join(', ');
      },
      header: 'Supported Artwork Types',
      type: 'text',
    },
    {
      accessor: (row: TVendor) =>
        row.apiIntegrationEnabled !== undefined ? (row.apiIntegrationEnabled ? 'Yes' : 'No') : '-',
      header: 'API Integration',
      type: 'chip',
      chipConfig: {
        valueMap: {
          Yes: { label: 'Yes', color: 'success' },
          No: { label: 'No', color: 'warning' },
          '-': { label: '-', color: 'default' },
        },
      },
    },
    {
      accessor: (row: TVendor) => row.flatFileFormat || '-',
      header: 'Flat File Format',
      type: 'text',
    },
    {
      accessor: (row: TVendor) => row.id || '-',
      header: 'Actions',
      type: 'custom' as const,
      customRender: (value: string, row: any) => {
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'Center' }}>
            <Tooltip title="View Vendor" arrow placement="left-end">
              <span>
                <CustomIconButton
                  icon={<i className="ri-eye-line text-textSecondary" />}
                  onClick={e => {
                    e.stopPropagation();
                    const path = `/vendors/view/${row.id}`;
                    router.push(path, { scroll: false });
                  }}
                />
              </span>
            </Tooltip>
            <Tooltip title="Edit Vendor" arrow placement="left-end">
              <span>
                <CustomIconButton
                  icon={<i className="ri-edit-line text-textSecondary" />}
                  onClick={e => {
                    e.stopPropagation();
                    const path = `/vendors/edit/${row.id}`;
                    router.push(path, { scroll: false });
                  }}
                />
              </span>
            </Tooltip>
          </Box>
        );
      },
    },
  ]);
  return (
    <div>
      <DataTable
        data={allVendors}
        columns={columns || []}
        enableFilters={false}
        loading={false}
        totalCount={allVendors.length || 0}
        page={Number(page)}
        onRowClick={row => handleRowClick(row, window.event as unknown as React.MouseEvent)}
        initialPageSize={Number(limit)}
        limit={Number(limit)}
        onPaginationUpdate={(page, limit, isPageChange, isLimitChange) => {
          const params = new URLSearchParams(searchParams);
          if (isLimitChange) {
            params.set('limit', limit.toString());
            params.set('page', '1');
          } else if (isPageChange) {
            params.set('page', !page ? '1' : (page + 1).toString());
            params.set('limit', limit.toString());
          }

          router.push(`?${params.toString()}`);
        }}
      />
    </div>
  );
};

export default VendorsTable;
