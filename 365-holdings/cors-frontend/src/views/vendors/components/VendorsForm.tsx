import { useF<PERSON>, Controller, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Grid2 as Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Button,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Autocomplete,
  Card,
  CardHeader,
  Divider,
  CardContent,
  Box,
} from '@mui/material';
import SkuSelect from '@/components/common/SkuSelect';
import { useRouter } from 'nextjs-toploader/app';
import { useSearchParams } from 'next/navigation';

const VendorsForm = ({
  onSubmit,
  isEdit = false,
}: {
  onSubmit: (data: any) => void;
  isEdit?: boolean;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const validationSchema = yup.object().shape({
    vendorId: yup.string().required('Vendor ID is required'),
    name: yup.string().required('Vendor name is required'),
    supportedSkus: yup
      .array()
      .of(yup.object().nullable())
      .min(1, 'At least one SKU is required')
      .required('Supported SKUs is required'),
    costPerSku: yup.number().positive('Cost must be positive').required('Cost per SKU is required'),
    averageCycleTime: yup
      .number()
      .positive('Cycle time must be positive')
      .required('Average cycle time is required'),
    maximumDailyCapacity: yup
      .number()
      .positive('Capacity must be positive')
      .required('Maximum daily capacity is required'),
    rushOrderSupport: yup.boolean(),
    supportedManufacturingTypes: yup
      .array()
      .min(1, 'At least one manufacturing type must be selected')
      .required('Supported manufacturing types is required'),
    shippingPreferences: yup.string().required('Shipping preferences is required'),

    supportedArtworkTypes: yup
      .array()
      .min(1, 'At least one artwork type must be selected')
      .required('Supported artwork types is required'),
    apiIntegrationEnabled: yup.boolean(),

    flatFileFormat: yup.string().when('apiIntegrationEnabled', {
      is: true,
      then: () => yup.string().nullable(),
      otherwise: () => yup.string().required('Flat file format is required'),
    }),
  });

  const manufacturingTypes = ['Print', 'Embroidery', 'Screen Print', 'DTG', 'Sublimation'];
  const artworkTypes = ['Line Art', 'Cutout', 'Knitted', 'Vector', 'Raster'];
  const carriers = ['UPS', 'FedEx', 'USPS', 'DHL'];
  const flatFileFormat = ['CSV', 'JSON', 'XML'];

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      vendorId: '',
      name: '',
      supportedSkus: [],
      costPerSku: undefined,
      averageCycleTime: undefined,
      maximumDailyCapacity: undefined,
      rushOrderSupport: false,
      supportedManufacturingTypes: [],
      shippingPreferences: '',
      supportedArtworkTypes: [],
      flatFileFormat: '',
      apiIntegrationEnabled: false,
    },
  });

  const apiIntegrationEnabled = useWatch({ control, name: 'apiIntegrationEnabled' });

  const handleBack = () => {
    const returnUrl = searchParams.get('returnUrl');
    if (returnUrl) {
      router.push(returnUrl, { scroll: false });
    } else {
      router.push('/vendors', { scroll: false });
    }
  };
  return (
    <Card>
      <CardHeader
        title={isEdit ? 'Edit Vendor' : 'Add Vendor'}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button variant="outlined" onClick={handleBack}>
              Back to Vendors
            </Button>
          </Box>
        }
      />
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Controller
                name="vendorId"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Vendor ID"
                    error={!!errors.vendorId}
                    helperText={errors.vendorId?.message}
                  />
                )}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Vendor Name"
                    error={!!errors.name}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <SkuSelect
                name="supportedSkus"
                control={control}
                error={errors}
                onChange={() => {}}
                multiple={true}
                //   disabled={updating}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Controller
                name="costPerSku"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Cost per SKU"
                    error={!!errors.costPerSku}
                    helperText={errors.costPerSku?.message}
                  />
                )}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Controller
                name="averageCycleTime"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Average Cycle Time (days)"
                    error={!!errors.averageCycleTime}
                    helperText={errors.averageCycleTime?.message}
                  />
                )}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Controller
                name="maximumDailyCapacity"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Maximum Daily Capacity"
                    error={!!errors.maximumDailyCapacity}
                    helperText={errors.maximumDailyCapacity?.message}
                  />
                )}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="supportedManufacturingTypes"
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    defaultValue={[]}
                    multiple
                    options={manufacturingTypes}
                    renderInput={params => (
                      <TextField
                        {...params}
                        label="Supported Manufacturing Types"
                        error={!!errors.supportedManufacturingTypes}
                        helperText={errors.supportedManufacturingTypes?.message}
                      />
                    )}
                    onChange={(_, data) => field.onChange(data)}
                  />
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="supportedArtworkTypes"
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    multiple
                    freeSolo
                    defaultValue={[]}
                    options={artworkTypes}
                    renderInput={params => (
                      <TextField
                        {...params}
                        label="Supported Artwork Types"
                        error={!!errors.supportedArtworkTypes}
                        helperText={errors.supportedArtworkTypes?.message}
                      />
                    )}
                    onChange={(_, data) => field.onChange(data)}
                  />
                )}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="shippingPreferences"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.shippingPreferences}>
                    <InputLabel>Shipping Preferences</InputLabel>
                    <Select {...field} label="Shipping Preferences">
                      {carriers.map(carrier => (
                        <MenuItem key={carrier} value={carrier}>
                          {carrier}
                        </MenuItem>
                      ))}
                    </Select>
                    <FormHelperText>{errors.shippingPreferences?.message}</FormHelperText>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="flatFileFormat"
                control={control}
                render={({ field }) => (
                  <FormControl
                    fullWidth
                    error={!!errors.flatFileFormat}
                    disabled={apiIntegrationEnabled}
                  >
                    <InputLabel>Flat File Format</InputLabel>
                    <Select {...field} label="Flat File Format" disabled={apiIntegrationEnabled}>
                      {flatFileFormat.map(format => (
                        <MenuItem key={format} value={format}>
                          {format}
                        </MenuItem>
                      ))}
                    </Select>
                    <FormHelperText>{errors.flatFileFormat?.message}</FormHelperText>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 4 }}>
              <FormGroup row>
                <Controller
                  name="rushOrderSupport"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Checkbox {...field} checked={field.value || false} />}
                      label="Rush Order Support"
                    />
                  )}
                />
              </FormGroup>
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <FormGroup row>
                <Controller
                  name="apiIntegrationEnabled"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          {...field}
                          checked={field.value || false}
                          onChange={e => {
                            field.onChange(e);
                            if (e.target.checked) {
                              setValue('flatFileFormat', '');
                            }
                          }}
                        />
                      }
                      label="API Integration Enabled"
                    />
                  )}
                />
              </FormGroup>
            </Grid>

            <Grid size={{ xs: 12 }} sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
              <Button type="submit" variant="contained" color="primary">
                {isEdit ? 'Update Vendor' : 'Add Vendor'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  );
};

export default VendorsForm;
