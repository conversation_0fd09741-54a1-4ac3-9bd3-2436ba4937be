'use client';
import { Button, Typography } from '@mui/material';
import React from 'react';
import VendorsTable from './components/VendorsTable';
import Grid from '@mui/material/Grid2';
import { allVendors } from '@/constants/vendors.constants';
import { Add } from '@mui/icons-material';
import { useRouter } from 'nextjs-toploader/app';

const VendorsWrapper = () => {
  const router = useRouter();
  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }} className="flex justify-between items-center mb-2">
        <Typography variant="h4">Vendors</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<Add />}
          onClick={() => {
            router.push('/vendors/add');
          }}
        >
          Add Vendor
        </Button>
      </Grid>

      <Grid size={{ xs: 12 }}>
        <VendorsTable allVendors={allVendors} />
      </Grid>
    </Grid>
  );
};

export default VendorsWrapper;
