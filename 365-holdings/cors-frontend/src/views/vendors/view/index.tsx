'use client';

import {
  <PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  Typo<PERSON>,
  Chip,
  Divider,
  Grid2 as Grid,
  Avatar,
} from '@mui/material';

import {
  Business as Building2Icon,
  Email as MailIcon,
  Phone as PhoneIcon,
  LocationOn as MapPinIcon,
  Inventory as PackageIcon,
  AttachMoney as DollarSignIcon,
  Schedule as ClockIcon,
  LocalShipping as TruckIcon,
  FlashOn as ZapIcon,
  Description as FileTextIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as XCircleIcon,
} from '@mui/icons-material';
import DataError from '@/components/DataError';
import { allVendors } from '@/constants/vendors.constants';

// Mock vendor data - replace with your actual data structure
const mockVendor = {
  id: '1',
  name: 'Quick Stitch Express',
  email: '<EMAIL>',
  phone: '******-0102',
  address: '456 Embroidery Lane, Miami, FL 33101',
  supportedSkus: ['PLUSH-002', 'PLUSH-005', 'PLUSH-006'],
  costPerSku: {
    'PLUSH-002': 18.5,
    'PLUSH-005': 25,
    'PLUSH-006': 30.75,
  },
  averageCycleTime: 3,
  maximumDailyCapacity: 300,
  rushOrderSupport: true,
  supportedManufacturingTypes: ['embroidery', 'sewing'],
  shippingPreferences: {
    usesShipStation: false,
    preferredCarrier: 'UPS',
  },
  supportedArtworkTypes: ['Embroidery Files', 'Vector Graphics'],
  apiIntegrationEnabled: true,
  flatFileFormat: 'XML',
};

const ViewVendors = ({ id }: { id: string }) => {
  // Replace this with your actual vendor lookup logic
  const vendor = allVendors?.find(vendor => vendor.id === id);

  if (!vendor) {
    return <DataError />;
  }

  return (
    <Box sx={{ p: 3, maxWidth: '1400px', mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 4 }}>
        <Avatar sx={{ bgcolor: 'primary.main', width: 48, height: 48 }}>
          <Building2Icon />
        </Avatar>
        <Box>
          <Typography variant="h3" component="h1" fontWeight="bold">
            {vendor.name}
          </Typography>
          <Typography color="text.secondary">Vendor Details & Information</Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Contact Information */}
        <Grid size={{ xs: 12, lg: 4 }}>
          <Card sx={{ height: '100%' }}>
            <CardHeader
              avatar={<MailIcon />}
              title="Contact Information"
              titleTypographyProps={{ variant: 'h6' }}
            />
            <CardContent sx={{ pt: 0 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                  <MailIcon sx={{ fontSize: 16, mt: 0.5, color: 'text.secondary' }} />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Email
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {vendor.email}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                  <PhoneIcon sx={{ fontSize: 16, mt: 0.5, color: 'text.secondary' }} />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Phone
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {vendor.phone}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                  <MapPinIcon sx={{ fontSize: 16, mt: 0.5, color: 'text.secondary' }} />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Address
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {vendor.address}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        {/* Operational Details */}
        <Grid size={{ xs: 12, lg: 4 }}>
          <Card sx={{ height: '100%' }}>
            <CardHeader
              avatar={<SettingsIcon />}
              title="Operational Details"
              titleTypographyProps={{ variant: 'h6' }}
            />
            <CardContent sx={{ pt: 0 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box
                  sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ClockIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                    <Typography variant="body2" fontWeight="medium">
                      Cycle Time
                    </Typography>
                  </Box>
                  <Chip label={`${vendor.averageCycleTime} days`} size="small" />
                </Box>
                <Box
                  sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PackageIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                    <Typography variant="body2" fontWeight="medium">
                      Daily Capacity
                    </Typography>
                  </Box>
                  <Chip label={`${vendor.maximumDailyCapacity} units`} size="small" />
                </Box>
                <Box
                  sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <FileTextIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                    <Typography variant="body2" fontWeight="medium">
                      File Format
                    </Typography>
                  </Box>
                  <Chip label={vendor.flatFileFormat} variant="outlined" size="small" />
                </Box>
                <Divider sx={{ my: 1 }} />
                <Box
                  sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                >
                  <Typography variant="body2" fontWeight="medium">
                    Rush Orders
                  </Typography>
                  {vendor.rushOrderSupport ? (
                    <Chip
                      icon={<CheckCircleIcon sx={{ color: 'white' }} />}
                      label="Supported"
                      size="small"
                      sx={{ bgcolor: 'primary.light', color: 'white' }}
                    />
                  ) : (
                    <Chip icon={<XCircleIcon />} label="Not Supported" size="small" color="error" />
                  )}
                </Box>
                <Box
                  sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                >
                  <Typography variant="body2" fontWeight="medium">
                    API Integration
                  </Typography>
                  {vendor.apiIntegrationEnabled ? (
                    <Chip
                      icon={<CheckCircleIcon />}
                      label="Enabled"
                      size="small"
                      sx={{ bgcolor: 'primary.light', color: 'white' }}
                    />
                  ) : (
                    <Chip icon={<XCircleIcon />} label="Disabled" size="small" color="default" />
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        {/* Shipping & Logistics */}
        <Grid size={{ xs: 12, lg: 4 }}>
          <Card sx={{ height: '100%' }}>
            <CardHeader
              avatar={<TruckIcon />}
              title="Shipping & Logistics"
              titleTypographyProps={{ variant: 'h6' }}
            />
            <CardContent sx={{ pt: 0 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box
                  sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                >
                  <Typography variant="body2" fontWeight="medium">
                    ShipStation
                  </Typography>
                  {vendor.shippingPreferences?.usesShipStation ? (
                    <Chip
                      icon={<CheckCircleIcon />}
                      label="Integrated"
                      size="small"
                      sx={{ bgcolor: 'primary.light', color: 'white' }}
                    />
                  ) : (
                    <Chip label="Not Used" variant="outlined" size="small" />
                  )}
                </Box>
                <Box
                  sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                >
                  <Typography variant="body2" fontWeight="medium">
                    Preferred Carrier
                  </Typography>
                  <Chip
                    label={vendor.shippingPreferences?.preferredCarrier || 'None'}
                    size="small"
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        {/* Products & Pricing */}
        <Grid size={12}>
          {/* Products & Pricing */}
          <Card sx={{ mt: 3 }}>
            <CardHeader
              avatar={<DollarSignIcon />}
              title="Products & Pricing"
              titleTypographyProps={{ variant: 'h6' }}
            />
            <CardContent sx={{ pt: 0 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                <Box>
                  <Typography variant="body2" fontWeight="medium" sx={{ mb: 2 }}>
                    Supported SKUs
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {vendor.supportedSkus?.map(sku => (
                      <Chip key={sku} label={sku} variant="outlined" />
                    ))}
                  </Box>
                </Box>
                <Divider />
                <Box>
                  <Typography variant="body2" fontWeight="medium" sx={{ mb: 2 }}>
                    Cost per SKU
                  </Typography>
                  <Grid container spacing={2}>
                    {Object.entries(vendor.costPerSku || {}).map(([sku, cost]) => (
                      <Grid size={{ xs: 12, md: 4 }} key={sku}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            p: 2,
                            borderRadius: 4,
                            border: '1px solid grey',
                          }}
                        >
                          <Typography variant="body2">{sku}</Typography>
                          <Chip
                            label={`$${cost}`}
                            size="small"
                            sx={{ bgcolor: 'primary.light', color: 'white' }}
                          />
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        {/* Manufacturing Types */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardHeader
              avatar={<ZapIcon />}
              title="Manufacturing Types"
              titleTypographyProps={{ variant: 'h6' }}
            />
            <CardContent sx={{ pt: 0 }}>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {vendor.supportedManufacturingTypes?.map(type => (
                  <Chip
                    key={type}
                    label={type.charAt(0).toUpperCase() + type.slice(1)}
                    variant="outlined"
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
        {/* Artwork Types */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardHeader
              avatar={<FileTextIcon />}
              title="Artwork Types"
              titleTypographyProps={{ variant: 'h6' }}
            />
            <CardContent sx={{ pt: 0 }}>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {vendor.supportedArtworkTypes?.map(type => (
                  <Chip key={type} label={type} variant="outlined" />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ViewVendors;
