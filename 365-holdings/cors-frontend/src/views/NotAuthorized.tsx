"use client";
import Link from "next/link";
import { useParams } from "next/navigation";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import type { Locale } from "@configs/i18n";
import { getLocalizedUrl } from "@/utils/i18n";
import type { Mode } from "@core/types";
import Illustrations from "@components/Illustrations";
import { useImageVariant } from "@core/hooks/useImageVariant";

const NotAuthorized = ({ mode }: { mode: Mode }) => {
  const darkImg = "/images/pages/misc-mask-dark.png";
  const lightImg = "/images/pages/misc-mask-light.png";
  const { lang: locale } = useParams();
  const miscBackground = useImageVariant(mode, lightImg, darkImg);

  return (
    <div className="flex items-center justify-center min-bs-[100dvh] relative p-6 overflow-x-hidden">
      <div className="flex items-center flex-col text-center gap-10">
        <div className="flex flex-col gap-2 is-[90vw] sm:is-[unset]">
          <Typography className="text-8xl font-medium" color="text.primary">
            401
          </Typography>
          <Typography variant="h4">You are not authorized! 🔐</Typography>
          <Typography>
            You don&#39;t have permission to access this page. Go Home!
          </Typography>
        </div>
        <img
          alt="error-illustration"
          src="/images/illustrations/characters/8.png"
          className="object-cover bs-[400px] md:bs-[450px] lg:bs-[500px]"
        />
        <Button
          href={getLocalizedUrl("/", locale as Locale)}
          component={Link}
          variant="contained">
          Back to Home
        </Button>
      </div>
      <Illustrations maskImg={{ src: miscBackground }} />
    </div>
  );
};

export default NotAuthorized;
