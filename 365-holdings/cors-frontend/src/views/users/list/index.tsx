'use client';

import { useEffect, useState } from 'react';
import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';
import { useRouter } from 'nextjs-toploader/app';

import UserListTable from './UserListTable';
import { UsersType } from '@/types/userTypes';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import Button from '@/components/Button';

const UserList = ({ userData, count }: { userData?: UsersType[]; count: number }) => {
  const router = useRouter();
  const [currentData, setCurrentData] = useState<UsersType[]>(userData || []);
  const [currentCount, setCurrentCount] = useState<number>(count);

  const handleAddUser = () => {
    router.push('/users/add');
  };

  useEffect(() => {
    setCurrentData(userData || []);
    setCurrentCount(count);
  }, [userData, count]);
  const handleDataUpdate = (data: UsersType[], newCount: number) => {
    setCurrentData(data);
    setCurrentCount(newCount);
  };

  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }} className="flex justify-between items-center mb-2">
        <Typography variant="h4">Users List</Typography>

        <Button
          onClick={handleAddUser}
          ButtonAction={Actions.CreateUsers}
          actionTarget={ActionsTarget.UserManagment}
          variant="contained"
          size="medium"
          title="Add User"
        />
      </Grid>

      <Grid size={{ xs: 12 }}>
        <UserListTable
          tableData={currentData}
          count={currentCount}
          onDataUpdate={handleDataUpdate}
        />
      </Grid>
    </Grid>
  );
};

export default UserList;
