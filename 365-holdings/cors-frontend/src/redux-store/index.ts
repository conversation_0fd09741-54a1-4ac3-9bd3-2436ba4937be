import { configureStore } from '@reduxjs/toolkit';
import { persistReducer, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import rootReducer from './stores';

// Create a noop storage object as fallback
const createNoopStorage = () => {
  return {
    getItem(_key: string) {
      return Promise.resolve(null);
    },
    setItem(_key: string, value: any) {
      return Promise.resolve(value);
    },
    removeItem(_key: string) {
      return Promise.resolve();
    },
  };
};

const persistConfig = {
  key: 'root',
  storage: typeof window !== 'undefined' ? storage : createNoopStorage(),
  whitelist: ['common', 'orders', 'productSku'], // persist common, orders, and productSku slices
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware => getDefaultMiddleware({ serializableCheck: false }),
});

export const persistor = persistStore(store);

// Define the base state type without persistence
type BaseState = ReturnType<typeof rootReducer>;

// Define the persisted state type
export type RootState = BaseState & {
  _persist?: {
    version: number;
    rehydrated: boolean;
  };
};

export type AppDispatch = typeof store.dispatch;
