import apiClient from '@/utils/axios';
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

export type Options = {
  value: boolean | string | number;
  key: string;
};

export type FieldsType = {
  key: string;
  label?: string | never;
  type: "select" | "number" | "text" | "multi_select" | "date" | "shopify_native_variant";
  options?: Options[];
  usage: "both" | "filter" | "bulk_update";
  secondary_key?: any;
  fetch_db?: boolean;
  filter_key?: string | never,
  backend_key?: string | never
  show_in_list?: boolean | never
  sortable?: boolean | never,
  category? : string,
  endpoint?: string,
  action?: string
  remakeReason?: any
  operators?: { label: string; value: string }[];
};

// Shared filter type for use in filter UIs
export type Filter = {
  attribute: string;
  operator: string;
  value: string | string[] | number | { start?: string; end?: string } | { key?: string; value?: string };
};

type ConfigType = {
    fields : FieldsType[] | never
    filterDataFields?: FieldsType[] | never;
    bulkInsertFields?: FieldsType[] | Record<string, FieldsType[]> | never,
    allFields?: FieldsType[] | never;
};

interface commonState {
  tableConfig: ConfigType;
  ordersTableConfig: ConfigType;
  simpleFields: ConfigType;
  loading: boolean;
  error: string | null;
}

const initialState: commonState = {
  tableConfig: {
    filterDataFields: [],
    bulkInsertFields : [],
    fields : []
  },
  ordersTableConfig : {
    fields : [],
    filterDataFields : []
  },
  simpleFields: {
    allFields: [],
    fields: []
  },
  
  loading : false,
  error : null
};

export const fetchTableConfigs = createAsyncThunk(
  "table-configs/fetch",
  async () => {
    const productFieldsRes = apiClient.get("/product-sku/fields");
    const ordersFieldRes = apiClient.get('/orders/fields')
    const response = await Promise.all([productFieldsRes,ordersFieldRes]);
    return {productSKuFields :  response[0]?.data.fields , ordersFields : response[1].data.fields}; 
  }
);


  

const commonSlice = createSlice({
  name: 'common',
  initialState,
  reducers: {
    setConfigs: (state, action: PayloadAction<{fields : FieldsType[]}>) => {
        const filterData = action.payload.fields?.filter(val => val.usage == "filter" || val.usage == "both")
        const bulkInsertData = action.payload.fields?.filter(val => val.usage == "bulk_update" || val.usage == "both")
      state.tableConfig = {filterDataFields : filterData, bulkInsertFields : bulkInsertData, fields : action.payload.fields};
    },
  },
  extraReducers: (builder) => {
      builder
        .addCase(fetchTableConfigs.pending, (state) => {
          state.loading = true;
          state.error = null;
        })
        .addCase(fetchTableConfigs.fulfilled, (state, action) => {
          state.loading = false;
          
          let categorizedBulkConfig: Record<string, FieldsType[]> = {}
          const filterData = action.payload.productSKuFields?.filter((val : FieldsType) => val.usage == "filter" || val.usage == "both")
          const bulkInsertData = action.payload.productSKuFields?.filter((val : FieldsType) => val.usage == "bulk_update" || val.usage == "both");
          bulkInsertData.forEach((item : FieldsType)=>{
            Object.assign(categorizedBulkConfig,{[item.category as string] : categorizedBulkConfig[item.category as string] ? [...categorizedBulkConfig[item.category as string],item] : [item]})
          })
          const showInList = action.payload.productSKuFields?.filter((val : FieldsType) => val.show_in_list);
          const ordersFilterData = action.payload.ordersFields?.filter((val : FieldsType) => val.usage == "filter" || val.usage == "both")
          const ordersShowInList = action.payload.ordersFields?.filter((val: FieldsType) => val.show_in_list);
          const cancelOrderFields = action.payload.ordersFields.find((val: FieldsType) => val.key === 'lineItems.cancelReason');
          const remakeRessonFields = action.payload.ordersFields.find((val: FieldsType) => val.key === 'remakeReason');
          const detaildRemakeRessonFields = action.payload.ordersFields.find((val: FieldsType) => val.key === 'detailedRemakeReason');
          
          state.tableConfig = {filterDataFields : filterData, bulkInsertFields : categorizedBulkConfig, fields : showInList};
          state.ordersTableConfig = {
            filterDataFields: ordersFilterData, 
            fields: ordersShowInList,
            allFields: action.payload.ordersFields
          };
          
          state.simpleFields = {
            allFields: [cancelOrderFields, remakeRessonFields, detaildRemakeRessonFields].filter(Boolean),
            fields: []
          };
          
        })
        .addCase(fetchTableConfigs.rejected, (state, action) => {
          state.loading = false;
          state.error = action.error.message || 'Failed to fetch products';
        });
    },

});

export const { setConfigs } = commonSlice.actions;
export default commonSlice.reducer;
