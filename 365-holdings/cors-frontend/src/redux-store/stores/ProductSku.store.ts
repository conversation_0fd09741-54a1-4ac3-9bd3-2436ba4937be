import { createSlice, PayloadAction } from '@reduxjs/toolkit';

type FilterCondition = {
    attribute: string;
    operator: string;
    value: string | number | boolean | (string | number | boolean)[];
  };
  
type FilterGroup = FilterCondition[];
  
type Filters = FilterGroup[];

function formatComplexFields(data: any[]): any[] {
  return data.map(item => {
    const formattedItem = { ...item };
    
    if (formattedItem.shopifyNativeVariant) {
      if (Array.isArray(formattedItem.shopifyNativeVariant)) {
        formattedItem.shopifyNativeVariantDisplay = formattedItem.shopifyNativeVariant
          .map((variant: any) => {
            if (typeof variant === 'object' && variant !== null) {
              return Object.values(variant).join(', ');
            }
            return String(variant);
          })
          .join('; ');
      } else if (typeof formattedItem.shopifyNativeVariant === 'object') {
        formattedItem.shopifyNativeVariantDisplay = Object.values(formattedItem.shopifyNativeVariant)
          .filter(v => v !== null && v !== undefined)
          .join(', ');
      }
    }

    // Handle product_category (backend now returns consistently)
    if (formattedItem.product_category) {
      if (Array.isArray(formattedItem.product_category)) {
        formattedItem.product_category = formattedItem.product_category.join(', ');
      } else if (typeof formattedItem.product_category === 'string') {
        if (/([A-Z][a-z]+)(?:\1)+/.test(formattedItem.product_category)) {
          formattedItem.product_category = formattedItem.product_category
            .match(/[A-Z][a-z]+/g)
            ?.join(', ') || formattedItem.product_category;
        } 
        else if (formattedItem.product_category.includes(',')) {
          formattedItem.product_category = formattedItem.product_category
            .split(',')
            .map((item: string) => item.trim())
            .join(', ');
        }
      }
    }

    if (formattedItem.product_category) {
      if (Array.isArray(formattedItem.product_category)) {
        formattedItem.product_category = formattedItem.product_category.join(', ');
      } else if (typeof formattedItem.product_category === 'string') {
        if (/([A-Z][a-z]+)(?:\1)+/.test(formattedItem.product_category)) {
          formattedItem.product_category = formattedItem.product_category
            .match(/[A-Z][a-z]+/g)
            ?.join(', ') || formattedItem.product_category;
        } 
        else if (formattedItem.product_category.includes(',')) {
          formattedItem.product_category = formattedItem.product_category
            .split(',')
            .map((item: string) => item.trim())
            .join(', ');
        }
      }
    }

    // Format complex fields if they exist
    if (formattedItem.metadata && typeof formattedItem.metadata === 'string') {
      try {
        formattedItem.metadata = JSON.parse(formattedItem.metadata);
      } catch (error) {
        console.error('Error parsing metadata:', error);
      }
    }
    
    return formattedItem;
  });
}

interface ProductSkuType {
    data : any[],
    count : number,
    loading : boolean,
    error : string | null,
    filters : Filters,
}

const initialState: ProductSkuType = {
  data: [],
  count: 0,
  loading: false,
  error: null,
  filters: [],
};


const productSkuSlice = createSlice({
  name: 'ProductSku',
  initialState,
  reducers: {
    setProducts: (state, action: PayloadAction<any>) => {
      state.data = formatComplexFields(action.payload.data || []);
      state.count = action.payload.count || 0;
      state.loading = false;
    },
    setProductsLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setProductsFilters: (state, action: PayloadAction<Filters>) => {
      state.filters = action.payload;
    },

    clearFilters: (state) => {
      state.filters = [];
      state.data = [];
      state.count = 0;
      state.loading = true;
    },
  },
});

export const {
  setProducts,
  setProductsLoading,
  setProductsFilters,
  clearFilters,
} = productSkuSlice.actions;
export default productSkuSlice.reducer;
