import { DynamicModule, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { getDatabaseConfig } from '../config/database.config';
import { MongoConfig } from 'src/common/interface/mongodbInterface';
import { PostgresConfig } from 'src/common/interface/postgresInterface';

@Module({})
export class DatabaseModule {
  static forRoot(): DynamicModule {
    const dbConfig = getDatabaseConfig();

    if (isPostgresConfig(dbConfig)) {
      return {
        module: DatabaseModule,
        imports: [
          TypeOrmModule.forRoot({
            type: 'postgres',
            host: dbConfig.host,
            port: dbConfig.port,
            username: dbConfig.username,
            password: dbConfig.password,
            database: dbConfig.database,
            autoLoadEntities: true,
            synchronize: process.env.NODE_ENV !== 'production',
            logging: process.env.NODE_ENV !== 'production',
          }),
        ],
      };
    }

    if (isMongoConfig(dbConfig)) {
      return {
        module: DatabaseModule,
        imports: [
          MongooseModule.forRoot(dbConfig.uri, {
            dbName: dbConfig.dbName || undefined, // optional support
          }),
        ],
      };
    }

    throw new Error('Unsupported DB_TYPE in environment config');
  }
}

// Type guards
function isPostgresConfig(config: any): config is PostgresConfig {
  return config?.type === 'postgres';
}

function isMongoConfig(config: any): config is MongoConfig {
  return config?.type === 'mongodb';
}
