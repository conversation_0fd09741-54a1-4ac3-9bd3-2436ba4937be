import { INestApplication, ExecutionContext } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { SwaggerAuthGuard } from '../common/guards/swagger-auth.guard';
import { Request, Response } from 'express';
import { ExecutionContextHost } from '@nestjs/core/helpers/execution-context-host';

export function setupSwagger(app: INestApplication): void {
  const config = new DocumentBuilder()
    .setTitle('Api Document Builder')
    .setDescription('Swagger UI for API documentation with secured access')
    .setVersion('1.0')
    .addBasicAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);

  app.use('/api', (req: Request, res: Response, next: () => void) => {
    const guard = new SwaggerAuthGuard();
    // Create a proper ExecutionContext using ExecutionContextHost
    const context: ExecutionContext = new ExecutionContextHost([
      req,
      res,
      next,
    ]);

    const canActivate: boolean | Promise<boolean> = guard.canActivate(context);

    // Type guard to check if value is a Promise
    function isPromise<T>(value: unknown): value is Promise<T> {
      return value instanceof Promise;
    }

    if (isPromise<boolean>(canActivate)) {
      canActivate
        .then((result) => {
          if (result) {
            next();
          } else {
            res.status(401).send('Unauthorized');
          }
        })
        .catch((err) => {
          const message =
            err && typeof err === 'object' && 'message' in err
              ? ((err as { message?: string }).message ?? 'Unauthorized')
              : 'Unauthorized';
          res.status(401).send(message);
        });
    } else if (canActivate) {
      next();
    } else {
      res.status(401).send('Unauthorized');
    }
  });

  SwaggerModule.setup('api', app, document, {
    customSiteTitle: 'Api Document Builder',
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: 'none',
    },
    customCss: `
      .swagger-ui .topbar { background-color: #4b0082; }
      .swagger-ui .topbar .download-url-wrapper { display: none; }
      .swagger-ui .info h1, .swagger-ui .info h2 { color: #4b0082; }
    `,
  });
}
