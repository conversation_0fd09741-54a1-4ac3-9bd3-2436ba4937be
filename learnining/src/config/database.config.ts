// src/config/database.config.ts
import { z } from 'zod';
import dotenv from 'dotenv';
import { PostgresConfig } from 'src/common/interface/postgresInterface';
import { MongoConfig } from 'src/common/interface/mongodbInterface';

// Load .env
const result = dotenv.config();
if (result?.error !== undefined) {
  const error = result.error;
  const errorMessage =
    typeof error === 'object' && error !== null && 'message' in error
      ? (error as Error).message
      : String(error);
  throw new Error(`Failed to load .env file: ${errorMessage}`);
}
// Schemas
const postgresSchema = z.object({
  type: z.literal('postgres'),
  host: z.string(),
  port: z.coerce.number(),
  username: z.string(),
  password: z.string(),
  database: z.string(),
});

const mongoSchema = z.object({
  type: z.literal('mongodb'),
  uri: z.string().url(),
});

const dbSchema = z.discriminatedUnion('type', [postgresSchema, mongoSchema]);

export type DatabaseConfig = PostgresConfig | MongoConfig;

export function getDatabaseConfig(): DatabaseConfig {
  const config = {
    type: process.env.DB_TYPE,
    host: process.env.POSTGRES_HOST,
    port: process.env.POSTGRES_PORT,
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
    uri: process.env.MONGODB_URI,
  };

  const parsed = dbSchema.safeParse(config);

  if (!parsed.success) {
    console.error(
      '❌ Invalid database environment variables:',
      parsed.error.issues,
    );
    throw new Error('Invalid database configuration');
  }

  return parsed.data;
}
