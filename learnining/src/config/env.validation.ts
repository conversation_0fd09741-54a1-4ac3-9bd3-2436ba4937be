import { plainToInstance } from 'class-transformer';
import {
  IsIn,
  IsNotEmpty,
  <PERSON>Optional,
  IsP<PERSON>,
  IsString,
  validateSync,
} from 'class-validator';

export class EnvironmentVariables {
  @IsPort({ message: 'PORT must be a valid port number (0-65535)' })
  @IsNotEmpty({ message: 'PORT is required' })
  PORT: string;

  @IsString({ message: 'SWAGGER_USER must be a string' })
  @IsNotEmpty({ message: 'SWAGGER_USER is required' })
  SWAGGER_USER: string;

  @IsString({ message: 'SWAGGER_PASS must be a string' })
  @IsNotEmpty({ message: 'SWAGGER_PASS is required' })
  SWAGGER_PASS: string;

  @IsString({ message: 'DB_TYPE must be a string' })
  @IsIn(['postgres', 'mongodb'], {
    message: 'DB_TYPE must be either "postgres" or "mongodb"',
  })
  @IsNotEmpty({ message: 'DB_TYPE is required' })
  DB_TYPE: string;

  @IsString({ message: 'POSTGRES_HOST must be a string' })
  @IsOptional()
  POSTGRES_HOST?: string;

  @IsPort({ message: 'POSTGRES_PORT must be a valid port number (0-65535)' })
  @IsOptional()
  POSTGRES_PORT?: string;

  @IsString({ message: 'POSTGRES_USER must be a string' })
  @IsOptional()
  POSTGRES_USER?: string;

  @IsString({ message: 'POSTGRES_PASSWORD must be a string' })
  @IsOptional()
  POSTGRES_PASSWORD?: string;

  @IsString({ message: 'POSTGRES_DB must be a string' })
  @IsOptional()
  POSTGRES_DB?: string;

  @IsString({ message: 'MONGODB_URI must be a string' })
  @IsOptional()
  MONGODB_URI?: string;

  constructor() {
    // Assign required variables with explicit checks
    this.PORT = process.env.PORT ?? this.throwRequiredError('PORT');
    this.SWAGGER_USER =
      process.env.SWAGGER_USER ?? this.throwRequiredError('SWAGGER_USER');
    this.SWAGGER_PASS =
      process.env.SWAGGER_PASS ?? this.throwRequiredError('SWAGGER_PASS');
    this.DB_TYPE = process.env.DB_TYPE ?? this.throwRequiredError('DB_TYPE');

    // Assign optional variables
    this.POSTGRES_HOST = process.env.POSTGRES_HOST;
    this.POSTGRES_PORT = process.env.POSTGRES_PORT;
    this.POSTGRES_USER = process.env.POSTGRES_USER;
    this.POSTGRES_PASSWORD = process.env.POSTGRES_PASSWORD;
    this.POSTGRES_DB = process.env.POSTGRES_DB;
    this.MONGODB_URI = process.env.MONGODB_URI;
  }

  // Helper method to throw errors for required variables
  private throwRequiredError(variable: string): never {
    throw new Error(`${variable} environment variable is required`);
  }

  // Getter for numeric port
  get portNumber(): number {
    return parseInt(this.PORT, 10);
  }

  // Conditional validation for database-specific variables
  validateDatabaseConfig(): void {
    if (this.DB_TYPE === 'postgres') {
      if (
        !this.POSTGRES_HOST ||
        !this.POSTGRES_PORT ||
        !this.POSTGRES_USER ||
        !this.POSTGRES_PASSWORD ||
        !this.POSTGRES_DB
      ) {
        throw new Error(
          'All PostgreSQL variables (POSTGRES_HOST, POSTGRES_PORT, POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB) are required when DB_TYPE is "postgres"',
        );
      }
    } else if (this.DB_TYPE === 'mongodb') {
      if (!this.MONGODB_URI) {
        throw new Error('MONGODB_URI is required when DB_TYPE is "mongodb"');
      }
    }
  }
}

export function validateEnv(): EnvironmentVariables {
  const env = new EnvironmentVariables();
  const validatedEnv = plainToInstance(EnvironmentVariables, env);
  const errors = validateSync(validatedEnv, {
    whitelist: true,
    forbidNonWhitelisted: true,
  });

  if (errors.length > 0) {
    const errorMessages = errors.map((e) => JSON.stringify(e)).join(', ');
    throw new Error(`Environment validation failed: ${errorMessages}`);
  }

  // Run conditional database validation
  validatedEnv.validateDatabaseConfig();

  return validatedEnv;
}
